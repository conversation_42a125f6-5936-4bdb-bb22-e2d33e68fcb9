import React, { useState, useEffect } from 'react';
import './App.css';
import Header from './components/Header';
import YouTubeWebView from './components/YouTubeWebView';
import SettingsEnhanced from './components/SettingsEnhanced';
import SidebarEnhanced from './components/SidebarEnhanced';
import DeveloperInfo from './components/DeveloperInfo';
import { YouTubeHelper } from './utils/youtubeApi';
import { adBlocker } from './utils/adBlocker';
import { developerMonetization, integratedAds } from './utils/developerMonetization';
import { channelConfig, loadChannelVideos, recordView } from './utils/channelConfig';

function AppHybrid() {
  const [currentVideoId, setCurrentVideoId] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [showDeveloperInfo, setShowDeveloperInfo] = useState(false);
  const [channelVideos, setChannelVideos] = useState([]);
  const [channelInfo, setChannelInfo] = useState(null);
  const [isLoadingChannel, setIsLoadingChannel] = useState(true);
  const [settings, setSettings] = useState({
    quality: 'auto',
    hideControls: false,
    adBlock: true,
    darkTheme: true,
    volume: 100,
    autoplay: false
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [appStats, setAppStats] = useState({
    videosWatched: 0,
    adsBlocked: 0,
    timeSpent: 0
  });

  // تحميل الإعدادات من التخزين المحلي
  useEffect(() => {
    const savedSettings = localStorage.getItem('youtube-player-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    }

    const savedStats = localStorage.getItem('youtube-player-stats');
    if (savedStats) {
      try {
        const parsed = JSON.parse(savedStats);
        setAppStats(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Error loading stats:', error);
      }
    }
  }, []);

  // حفظ الإعدادات في التخزين المحلي
  useEffect(() => {
    localStorage.setItem('youtube-player-settings', JSON.stringify(settings));
  }, [settings]);

  // حفظ الإحصائيات في التخزين المحلي
  useEffect(() => {
    localStorage.setItem('youtube-player-stats', JSON.stringify(appStats));
  }, [appStats]);

  // تحميل فيديوهات القناة
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoadingChannel(true);
        const videos = await loadChannelVideos();
        setChannelVideos(videos);
        setChannelInfo(channelConfig.developer);
      } catch (error) {
        console.error('Error loading channel data:', error);
      } finally {
        setIsLoadingChannel(false);
      }
    };

    loadData();
  }, []);

  // تفعيل مانع الإعلانات
  useEffect(() => {
    if (settings.adBlock) {
      adBlocker.enable();
    } else {
      adBlocker.disable();
    }
  }, [settings.adBlock]);

  // تحديث إحصائيات مانع الإعلانات
  useEffect(() => {
    const updateAdStats = () => {
      const blockedCount = adBlocker.getBlockedCount();
      setAppStats(prev => ({
        ...prev,
        adsBlocked: blockedCount
      }));
    };

    const interval = setInterval(updateAdStats, 1000);
    return () => clearInterval(interval);
  }, []);

  // تطبيق الثيم الداكن
  useEffect(() => {
    document.body.className = settings.darkTheme ? 'dark-theme' : 'light-theme';
  }, [settings.darkTheme]);

  // معالجة البحث أو تشغيل الفيديو
  const handleVideoLoad = (input) => {
    const videoId = YouTubeHelper.extractVideoId(input);
    if (videoId) {
      setCurrentVideoId(videoId);
      setSearchQuery('');
      
      // تحديث إحصائية الفيديوهات المشاهدة
      setAppStats(prev => ({
        ...prev,
        videosWatched: prev.videosWatched + 1
      }));

      // إرسال حدث بدء الفيديو لنظام الربح
      window.dispatchEvent(new CustomEvent('video-started', { detail: { videoId } }));
    } else {
      // إذا لم يكن رابط، فهو استعلام بحث
      setSearchQuery(input);
    }
  };

  // تحديث الإعدادات
  const updateSettings = (newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  // معالجة اختيار فيديو من الشريط الجانبي
  const handleVideoSelect = (videoId) => {
    setCurrentVideoId(videoId);
    recordView(videoId);
    
    // تحديث الإحصائيات
    setAppStats(prev => ({
      ...prev,
      videosWatched: prev.videosWatched + 1
    }));

    // إرسال حدث بدء الفيديو
    window.dispatchEvent(new CustomEvent('video-started', { detail: { videoId } }));
  };

  // معالجة تغيير الفيديو في WebView
  const handleVideoChange = (videoData) => {
    if (videoData && videoData.videoId) {
      setCurrentVideoId(videoData.videoId);
      recordView(videoData.videoId);
    }
  };

  return (
    <div className="app hybrid-app">
      <Header 
        onVideoLoad={handleVideoLoad}
        onSettingsClick={() => setShowSettings(!showSettings)}
        onDeveloperClick={() => setShowDeveloperInfo(!showDeveloperInfo)}
        stats={appStats}
      />
      
      <div className="app-content">
        <SidebarEnhanced 
          searchQuery={searchQuery}
          onVideoSelect={handleVideoSelect}
          currentVideoId={currentVideoId}
          channelVideos={channelVideos}
          channelInfo={channelInfo}
          isLoading={isLoadingChannel}
        />
        
        <main className="main-content">
          <YouTubeWebView 
            searchQuery={searchQuery}
            onVideoChange={handleVideoChange}
            settings={settings}
          />
          
          {/* شريط الحالة المحسن */}
          <div className="status-bar">
            <div className="status-item">
              <span>🌐</span>
              <span>يوتيوب مباشر</span>
            </div>
            
            {settings.adBlock && (
              <div className="status-item">
                <span>🛡️</span>
                <span>مانع الإعلانات مفعل</span>
              </div>
            )}
            
            <div className="status-item">
              <span>📊</span>
              <span>{appStats.adsBlocked} إعلان محجوب</span>
            </div>

            <div className="status-item">
              <span>🎥</span>
              <span>{appStats.videosWatched} فيديو مشاهد</span>
            </div>

            <div className="status-item">
              <span>💰</span>
              <span>أرباح المطور: {developerMonetization.getAnalytics().revenueFormatted}</span>
            </div>
          </div>
        </main>
        
        {showSettings && (
          <SettingsEnhanced 
            settings={settings}
            onSettingsChange={updateSettings}
            onClose={() => setShowSettings(false)}
            stats={appStats}
          />
        )}
        
        {showDeveloperInfo && (
          <DeveloperInfo 
            onClose={() => setShowDeveloperInfo(false)}
            stats={appStats}
          />
        )}
      </div>
      
      {/* الإعلانات المدمجة */}
      <div id="integrated-ads-container" />
    </div>
  );
}

export default AppHybrid;
