import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion } from 'framer-motion';
import useAppStore from '../../stores/appStore';
import youtubeAPI from '../../services/youtubeAPI';
import './Player.css';

const Player = ({ routeParams }) => {
  const [isPlayerReady, setIsPlayerReady] = useState(false);
  const [playerError, setPlayerError] = useState(null);
  const playerRef = useRef(null);
  const playerInstanceRef = useRef(null);
  
  const { 
    currentVideo, 
    setCurrentVideo, 
    updateVideoState,
    incrementVideosWatched,
    settings 
  } = useAppStore();

  const videoId = routeParams?.videoId || currentVideo.id;

  useEffect(() => {
    if (videoId) {
      loadYouTubeAPI();
      loadVideoDetails();
    }
  }, [videoId]);

  const loadVideoDetails = async () => {
    if (!videoId) return;

    try {
      const details = await youtubeAPI.getVideoDetails(videoId);
      setCurrentVideo({
        id: videoId,
        title: details.title,
        channel: details.channel,
        duration: details.duration,
        thumbnail: details.thumbnail,
        description: details.description,
        views: details.views,
        publishedAt: details.publishedAt
      });
    } catch (error) {
      console.error('Failed to load video details:', error);
    }
  };

  const loadYouTubeAPI = () => {
    if (!window.YT) {
      const script = document.createElement('script');
      script.src = 'https://www.youtube.com/iframe_api';
      script.async = true;
      document.body.appendChild(script);

      window.onYouTubeIframeAPIReady = () => {
        initializePlayer();
      };
    } else {
      initializePlayer();
    }
  };

  const initializePlayer = () => {
    if (playerRef.current && window.YT?.Player) {
      try {
        playerInstanceRef.current = new window.YT.Player(playerRef.current, {
          height: '100%',
          width: '100%',
          videoId: videoId,
          playerVars: {
            autoplay: settings.autoplay ? 1 : 0,
            controls: settings.hideControls ? 0 : 1,
            disablekb: settings.hideControls ? 1 : 0,
            fs: 1,
            iv_load_policy: 3,
            modestbranding: 1,
            playsinline: 1,
            rel: 0,
            showinfo: 0,
            cc_load_policy: 0,
            hl: 'ar',
            cc_lang_pref: 'ar',
            origin: window.location.origin
          },
          events: {
            onReady: onPlayerReady,
            onStateChange: onPlayerStateChange,
            onError: onPlayerError
          }
        });
      } catch (error) {
        console.error('Error initializing YouTube player:', error);
        setPlayerError('فشل في تحميل مشغل الفيديو');
      }
    }
  };

  const onPlayerReady = (event) => {
    setIsPlayerReady(true);
    setPlayerError(null);
    
    // Set initial volume
    if (settings.volume !== undefined) {
      event.target.setVolume(settings.volume);
    }
    
    // Get video info
    const videoData = event.target.getVideoData();
    setCurrentVideo({
      id: videoId,
      title: videoData.title || 'فيديو يوتيوب',
      channel: videoData.author || 'قناة يوتيوب',
      duration: event.target.getDuration() || 0,
      currentTime: 0,
      isPlaying: false,
      isPaused: true,
      isBuffering: false,
      quality: event.target.getPlaybackQuality() || 'auto',
      volume: event.target.getVolume() || 100,
      muted: event.target.isMuted() || false,
      fullscreen: false
    });

    console.log('YouTube player ready for video:', videoId);
  };

  const onPlayerStateChange = (event) => {
    const state = event.data;
    const player = event.target;
    
    switch (state) {
      case window.YT.PlayerState.PLAYING:
        updateVideoState({
          isPlaying: true,
          isPaused: false,
          isBuffering: false
        });
        break;
        
      case window.YT.PlayerState.PAUSED:
        updateVideoState({
          isPlaying: false,
          isPaused: true,
          isBuffering: false
        });
        break;
        
      case window.YT.PlayerState.BUFFERING:
        updateVideoState({
          isBuffering: true
        });
        break;
        
      case window.YT.PlayerState.ENDED:
        updateVideoState({
          isPlaying: false,
          isPaused: true,
          isBuffering: false,
          currentTime: player.getDuration()
        });
        incrementVideosWatched();
        break;
        
      case window.YT.PlayerState.CUED:
        updateVideoState({
          isPlaying: false,
          isPaused: true,
          isBuffering: false,
          currentTime: 0
        });
        break;
    }

    // Update current time
    if (player.getCurrentTime) {
      updateVideoState({
        currentTime: player.getCurrentTime()
      });
    }
  };

  const onPlayerError = (event) => {
    const errorCode = event.data;
    let errorMessage = 'حدث خطأ في تشغيل الفيديو';
    
    switch (errorCode) {
      case 2:
        errorMessage = 'معرف الفيديو غير صحيح';
        break;
      case 5:
        errorMessage = 'خطأ في مشغل HTML5';
        break;
      case 100:
        errorMessage = 'الفيديو غير موجود أو محذوف';
        break;
      case 101:
      case 150:
        errorMessage = 'صاحب الفيديو لا يسمح بتشغيله في هذا التطبيق';
        break;
    }
    
    setPlayerError(errorMessage);
    console.error('YouTube player error:', errorCode, errorMessage);
  };

  const handleRetry = () => {
    setPlayerError(null);
    setIsPlayerReady(false);
    if (playerInstanceRef.current) {
      playerInstanceRef.current.destroy();
    }
    setTimeout(() => {
      initializePlayer();
    }, 1000);
  };

  if (!videoId) {
    return (
      <div className="player-page">
        <div className="player-empty">
          <div className="empty-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </div>
          <h2>لا يوجد فيديو للتشغيل</h2>
          <p>يرجى البحث عن فيديو أو اختيار فيديو من القائمة</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div 
      className="player-page"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="player-container">
        <div className="player-wrapper">
          {playerError ? (
            <div className="player-error">
              <div className="error-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
              </div>
              <h3>خطأ في تشغيل الفيديو</h3>
              <p>{playerError}</p>
              <button className="retry-button" onClick={handleRetry}>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                </svg>
                إعادة المحاولة
              </button>
            </div>
          ) : (
            <>
              {!isPlayerReady && (
                <div className="player-loading">
                  <div className="loading-spinner">
                    <div className="spinner"></div>
                  </div>
                  <p>جاري تحميل الفيديو...</p>
                </div>
              )}
              <div 
                ref={playerRef}
                className={`youtube-player ${!isPlayerReady ? 'hidden' : ''}`}
              />
            </>
          )}
        </div>

        {/* Video Info */}
        {currentVideo.title && (
          <motion.div 
            className="video-info"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <h1 className="video-title">{currentVideo.title}</h1>
            <div className="video-meta">
              <span className="video-channel">{currentVideo.channel}</span>
              {currentVideo.duration > 0 && (
                <span className="video-duration">
                  المدة: {Math.floor(currentVideo.duration / 60)}:{(currentVideo.duration % 60).toString().padStart(2, '0')}
                </span>
              )}
            </div>
          </motion.div>
        )}

        {/* Ad Blocker Status */}
        {settings.adBlock && (
          <motion.div 
            className="ad-blocker-status"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.5 }}
          >
            <div className="status-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <span>مانع الإعلانات مفعل - تجربة مشاهدة بدون إعلانات</span>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default Player;
