{"version": 3, "file": "static/js/844.e3b82b7f.chunk.js", "mappings": "yLAKA,MAyKA,EAzKiBA,KACf,MAAM,SACJC,EAAQ,eACRC,EAAc,cACdC,EAAa,MACbC,EAAK,SACLC,EAAQ,MACRC,EAAK,WACLC,IACEC,EAAAA,EAAAA,KAEEC,EAAsBA,CAACC,EAAKC,KAChCT,EAAe,CAAE,CAACQ,GAAMC,KAGpBC,EAAqBC,IACzBR,EAASQ,IAGX,OACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTJ,UAAU,qBACVK,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAAMT,SAAA,EAE9BF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,4DAGJC,EAAAA,EAAAA,MAAA,WAASF,UAAU,mBAAkBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,2FAEJC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,SAAAE,SAAO,sIACPC,EAAAA,EAAAA,MAAA,UACEN,MAAOV,EAASyB,QAChBC,SAAWC,GAAMnB,EAAoB,UAAWmB,EAAEC,OAAOlB,OAAOK,SAAA,EAEhEF,EAAAA,EAAAA,KAAA,UAAQH,MAAM,OAAMK,SAAC,0CACrBF,EAAAA,EAAAA,KAAA,UAAQH,MAAM,SAAQK,SAAC,WACvBF,EAAAA,EAAAA,KAAA,UAAQH,MAAM,QAAOK,SAAC,UACtBF,EAAAA,EAAAA,KAAA,UAAQH,MAAM,QAAOK,SAAC,UACtBF,EAAAA,EAAAA,KAAA,UAAQH,MAAM,SAAQK,SAAC,UACvBF,EAAAA,EAAAA,KAAA,UAAQH,MAAM,QAAOK,SAAC,gBAI1BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcC,UAC3BC,EAAAA,EAAAA,MAAA,SAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SACEgB,KAAK,WACLC,QAAS9B,EAAS+B,SAClBL,SAAWC,GAAMnB,EAAoB,WAAYmB,EAAEC,OAAOE,WAC1D,4EAKNd,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,SAAAE,SAAO,mEACPF,EAAAA,EAAAA,KAAA,SACEgB,KAAK,QACLG,IAAI,IACJC,IAAI,MACJvB,MAAOV,EAASkC,OAChBR,SAAWC,GAAMnB,EAAoB,SAAU2B,SAASR,EAAEC,OAAOlB,WAEnEM,EAAAA,EAAAA,MAAA,QAAAD,SAAA,CAAOf,EAASkC,OAAO,cAK3BlB,EAAAA,EAAAA,MAAA,WAASF,UAAU,mBAAkBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,2FAEJC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,SAAAE,SAAO,0CACPC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,UACEC,UAAW,iBAA0B,SAAVX,EAAmB,SAAW,IACzDiC,QAASA,IAAMzB,EAAkB,QAAQI,SAC1C,8BAGDF,EAAAA,EAAAA,KAAA,UACEC,UAAW,iBAA0B,UAAVX,EAAoB,SAAW,IAC1DiC,QAASA,IAAMzB,EAAkB,SAASI,SAC3C,oCAMLF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcC,UAC3BC,EAAAA,EAAAA,MAAA,SAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SACEgB,KAAK,WACLC,QAAS9B,EAASqC,aAClBX,SAAWC,GAAMnB,EAAoB,eAAgBmB,EAAEC,OAAOE,WAC9D,8GAORd,EAAAA,EAAAA,MAAA,WAASF,UAAU,mBAAkBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iGAEJF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcC,UAC3BC,EAAAA,EAAAA,MAAA,SAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SACEgB,KAAK,WACLC,QAAS9B,EAASsC,QAClBZ,SAAWC,GAAMnB,EAAoB,UAAWmB,EAAEC,OAAOE,WACzD,uHAKNjB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcC,UAC3BC,EAAAA,EAAAA,MAAA,SAAAD,SAAA,EACEF,EAAAA,EAAAA,KAAA,SACEgB,KAAK,WACLC,QAAS9B,EAASuC,YAClBb,SAAWC,GAAMnB,EAAoB,cAAemB,EAAEC,OAAOE,WAC7D,kGAORd,EAAAA,EAAAA,MAAA,WAASF,UAAU,mBAAkBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,kEACJC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,oHACNF,EAAAA,EAAAA,KAAA,QAAAE,SAAOV,EAAMmC,oBAEfxB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,8GACNF,EAAAA,EAAAA,KAAA,QAAAE,SAAOV,EAAMoC,iBAEfzB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,0EACNC,EAAAA,EAAAA,MAAA,QAAAD,SAAA,CAAO2B,KAAKC,MAAMtC,EAAMuC,UAAY,IAAI,4CAG5C/B,EAAAA,EAAAA,KAAA,UAAQC,UAAU,eAAesB,QAAS9B,EAAWS,SAAC,mIAMxDC,EAAAA,EAAAA,MAAA,WAASF,UAAU,mBAAkBC,SAAA,EACnCF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,gDACJF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BF,EAAAA,EAAAA,KAAA,UAAQC,UAAU,eAAesB,QAASlC,EAAca,SAAC,4J", "sources": ["components/pages/Settings.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport './Settings.css';\n\nconst Settings = () => {\n  const { \n    settings, \n    updateSettings, \n    resetSettings,\n    theme,\n    setTheme,\n    stats,\n    resetStats\n  } = useAppStore();\n\n  const handleSettingChange = (key, value) => {\n    updateSettings({ [key]: value });\n  };\n\n  const handleThemeChange = (newTheme) => {\n    setTheme(newTheme);\n  };\n\n  return (\n    <div className=\"settings-page\">\n      <motion.div \n        className=\"settings-container\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        <h1>الإعدادات</h1>\n        \n        {/* Video Settings */}\n        <section className=\"settings-section\">\n          <h2>إعدادات الفيديو</h2>\n          \n          <div className=\"setting-item\">\n            <label>جودة الفيديو الافتراضية</label>\n            <select \n              value={settings.quality}\n              onChange={(e) => handleSettingChange('quality', e.target.value)}\n            >\n              <option value=\"auto\">تلقائي</option>\n              <option value=\"hd1080\">1080p</option>\n              <option value=\"hd720\">720p</option>\n              <option value=\"large\">480p</option>\n              <option value=\"medium\">360p</option>\n              <option value=\"small\">240p</option>\n            </select>\n          </div>\n\n          <div className=\"setting-item\">\n            <label>\n              <input\n                type=\"checkbox\"\n                checked={settings.autoplay}\n                onChange={(e) => handleSettingChange('autoplay', e.target.checked)}\n              />\n              تشغيل تلقائي\n            </label>\n          </div>\n\n          <div className=\"setting-item\">\n            <label>مستوى الصوت</label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"100\"\n              value={settings.volume}\n              onChange={(e) => handleSettingChange('volume', parseInt(e.target.value))}\n            />\n            <span>{settings.volume}%</span>\n          </div>\n        </section>\n\n        {/* Interface Settings */}\n        <section className=\"settings-section\">\n          <h2>إعدادات الواجهة</h2>\n          \n          <div className=\"setting-item\">\n            <label>المظهر</label>\n            <div className=\"theme-options\">\n              <button \n                className={`theme-option ${theme === 'dark' ? 'active' : ''}`}\n                onClick={() => handleThemeChange('dark')}\n              >\n                داكن\n              </button>\n              <button \n                className={`theme-option ${theme === 'light' ? 'active' : ''}`}\n                onClick={() => handleThemeChange('light')}\n              >\n                فاتح\n              </button>\n            </div>\n          </div>\n\n          <div className=\"setting-item\">\n            <label>\n              <input\n                type=\"checkbox\"\n                checked={settings.hideControls}\n                onChange={(e) => handleSettingChange('hideControls', e.target.checked)}\n              />\n              إخفاء عناصر التحكم\n            </label>\n          </div>\n        </section>\n\n        {/* Privacy Settings */}\n        <section className=\"settings-section\">\n          <h2>إعدادات الخصوصية</h2>\n          \n          <div className=\"setting-item\">\n            <label>\n              <input\n                type=\"checkbox\"\n                checked={settings.adBlock}\n                onChange={(e) => handleSettingChange('adBlock', e.target.checked)}\n              />\n              تفعيل مانع الإعلانات\n            </label>\n          </div>\n\n          <div className=\"setting-item\">\n            <label>\n              <input\n                type=\"checkbox\"\n                checked={settings.saveHistory}\n                onChange={(e) => handleSettingChange('saveHistory', e.target.checked)}\n              />\n              حفظ سجل المشاهدة\n            </label>\n          </div>\n        </section>\n\n        {/* Statistics */}\n        <section className=\"settings-section\">\n          <h2>الإحصائيات</h2>\n          <div className=\"stats-display\">\n            <div className=\"stat-item\">\n              <span>الفيديوهات المشاهدة:</span>\n              <span>{stats.videosWatched}</span>\n            </div>\n            <div className=\"stat-item\">\n              <span>الإعلانات المحجوبة:</span>\n              <span>{stats.adsBlocked}</span>\n            </div>\n            <div className=\"stat-item\">\n              <span>وقت المشاهدة:</span>\n              <span>{Math.floor(stats.timeSpent / 60)} دقيقة</span>\n            </div>\n          </div>\n          <button className=\"reset-button\" onClick={resetStats}>\n            إعادة تعيين الإحصائيات\n          </button>\n        </section>\n\n        {/* Actions */}\n        <section className=\"settings-section\">\n          <h2>إجراءات</h2>\n          <div className=\"settings-actions\">\n            <button className=\"reset-button\" onClick={resetSettings}>\n              إعادة تعيين جميع الإعدادات\n            </button>\n          </div>\n        </section>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "names": ["Settings", "settings", "updateSettings", "resetSettings", "theme", "setTheme", "stats", "resetStats", "useAppStore", "handleSettingChange", "key", "value", "handleThemeChange", "newTheme", "_jsx", "className", "children", "_jsxs", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "quality", "onChange", "e", "target", "type", "checked", "autoplay", "min", "max", "volume", "parseInt", "onClick", "hideControls", "adBlock", "saveHistory", "videosWatched", "adsBlocked", "Math", "floor", "timeSpent"], "sourceRoot": ""}