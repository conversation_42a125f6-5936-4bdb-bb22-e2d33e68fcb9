/*! For license information please see main.6b6bb0c2.js.LICENSE.txt */
(()=>{var e={75:(e,t,n)=>{"use strict";n.d(t,{B:()=>o,t:()=>i});var r=console;function i(){return r}function o(e){r=e}},293:(e,t,n)=>{"use strict";n.d(t,{E:()=>i});var r=n(5043);const i=n(4735).B?r.useLayoutEffect:r.useEffect},579:(e,t,n)=>{"use strict";e.exports=n(1153)},663:(e,t,n)=>{"use strict";n.d(t,{A:()=>se});var r=n(4548);function i(e,t){let n;try{n=e()}catch(r){return}return{getItem:e=>{var r;const i=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),o=null!=(r=n.getItem(e))?r:null;return o instanceof Promise?o.then(i):i(o)},setItem:(e,r)=>n.setItem(e,JSON.stringify(r,null==t?void 0:t.replacer)),removeItem:e=>n.removeItem(e)}}const o=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then:e=>o(e)(n),catch(e){return this}}}catch(n){return{then(e){return this},catch:e=>o(e)(n)}}},a=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?(console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),((e,t)=>(n,r,i)=>{let a={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},s=!1;const l=new Set,u=new Set;let c;try{c=a.getStorage()}catch(y){}if(!c)return e(function(){console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),n(...arguments)},r,i);const d=o(a.serialize),f=()=>{const e=a.partialize({...r()});let t;const n=d({state:e,version:a.version}).then(e=>c.setItem(a.name,e)).catch(e=>{t=e});if(t)throw t;return n},h=i.setState;i.setState=(e,t)=>{h(e,t),f()};const p=e(function(){n(...arguments),f()},r,i);let m;const v=()=>{var e;if(!c)return;s=!1,l.forEach(e=>e(r()));const t=(null==(e=a.onRehydrateStorage)?void 0:e.call(a,r()))||void 0;return o(c.getItem.bind(c))(a.name).then(e=>{if(e)return a.deserialize(e)}).then(e=>{if(e){if("number"!==typeof e.version||e.version===a.version)return e.state;if(a.migrate)return a.migrate(e.state,e.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}}).then(e=>{var t;return m=a.merge(e,null!=(t=r())?t:p),n(m,!0),f()}).then(()=>{null==t||t(m,void 0),s=!0,u.forEach(e=>e(m))}).catch(e=>{null==t||t(void 0,e)})};return i.persist={setOptions:e=>{a={...a,...e},e.getStorage&&(c=e.getStorage())},clearStorage:()=>{null==c||c.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>v(),hasHydrated:()=>s,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},v(),m||p})(e,t)):((e,t)=>(n,r,a)=>{let s={storage:i(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1;const u=new Set,c=new Set;let d=s.storage;if(!d)return e(function(){console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),n(...arguments)},r,a);const f=()=>{const e=s.partialize({...r()});return d.setItem(s.name,{state:e,version:s.version})},h=a.setState;a.setState=(e,t)=>{h(e,t),f()};const p=e(function(){n(...arguments),f()},r,a);let m;a.getInitialState=()=>p;const v=()=>{var e,t;if(!d)return;l=!1,u.forEach(e=>{var t;return e(null!=(t=r())?t:p)});const i=(null==(t=s.onRehydrateStorage)?void 0:t.call(s,null!=(e=r())?e:p))||void 0;return o(d.getItem.bind(d))(s.name).then(e=>{if(e){if("number"!==typeof e.version||e.version===s.version)return[!1,e.state];if(s.migrate)return[!0,s.migrate(e.state,e.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;const[i,o]=e;if(m=s.merge(o,null!=(t=r())?t:p),n(m,!0),i)return f()}).then(()=>{null==i||i(m,void 0),m=r(),l=!0,c.forEach(e=>e(m))}).catch(e=>{null==i||i(void 0,e)})};return a.persist={setOptions:e=>{s={...s,...e},e.storage&&(d=e.storage)},clearStorage:()=>{null==d||d.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>v(),hasHydrated:()=>l,onHydrate:e=>(u.add(e),()=>{u.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},s.skipHydration||v(),m||p})(e,t);function s(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map(function(e){return"'"+e+"'"}).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function l(e){return!!e&&!!e[G]}function u(e){var t;return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===Y}(e)||Array.isArray(e)||!!e[K]||!!(null===(t=e.constructor)||void 0===t?void 0:t[K])||v(e)||y(e))}function c(e,t,n){void 0===n&&(n=!1),0===d(e)?(n?Object.keys:X)(e).forEach(function(r){n&&"symbol"==typeof r||t(r,e[r],e)}):e.forEach(function(n,r){return t(r,n,e)})}function d(e){var t=e[G];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:v(e)?2:y(e)?3:0}function f(e,t){return 2===d(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function h(e,t){return 2===d(e)?e.get(t):e[t]}function p(e,t,n){var r=d(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function m(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function v(e){return $&&e instanceof Map}function y(e){return q&&e instanceof Set}function g(e){return e.o||e.t}function b(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=Z(e);delete t[G];for(var n=X(t),r=0;r<n.length;r++){var i=n[r],o=t[i];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(t[i]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[i]})}return Object.create(Object.getPrototypeOf(e),t)}function x(e,t){return void 0===t&&(t=!1),S(e)||l(e)||!u(e)||(d(e)>1&&(e.set=e.add=e.clear=e.delete=w),Object.freeze(e),t&&c(e,function(e,t){return x(t,!0)},!0)),e}function w(){s(2)}function S(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function k(e){var t=J[e];return t||s(18,e),t}function P(){return U}function C(e,t){t&&(k("Patches"),e.u=[],e.s=[],e.v=t)}function E(e){T(e),e.p.forEach(j),e.p=null}function T(e){e===U&&(U=e.l)}function A(e){return U={p:[],l:U,h:e,m:!0,_:0}}function j(e){var t=e[G];0===t.i||1===t.i?t.j():t.g=!0}function O(e,t){t._=t.p.length;var n=t.p[0],r=void 0!==e&&e!==n;return t.h.O||k("ES5").S(t,e,r),r?(n[G].P&&(E(t),s(4)),u(e)&&(e=M(t,e),t.l||N(t,e)),t.u&&k("Patches").M(n[G].t,e,t.u,t.s)):e=M(t,n,[]),E(t),t.u&&t.v(t.u,t.s),e!==Q?e:void 0}function M(e,t,n){if(S(t))return t;var r=t[G];if(!r)return c(t,function(i,o){return L(e,r,t,i,o,n)},!0),t;if(r.A!==e)return t;if(!r.P)return N(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var i=4===r.i||5===r.i?r.o=b(r.k):r.o,o=i,a=!1;3===r.i&&(o=new Set(i),i.clear(),a=!0),c(o,function(t,o){return L(e,r,i,t,o,n,a)}),N(e,i,!1),n&&e.u&&k("Patches").N(r,n,e.u,e.s)}return r.o}function L(e,t,n,r,i,o,a){if(l(i)){var s=M(e,i,o&&t&&3!==t.i&&!f(t.R,r)?o.concat(r):void 0);if(p(n,r,s),!l(s))return;e.m=!1}else a&&n.add(i);if(u(i)&&!S(i)){if(!e.h.D&&e._<1)return;M(e,i),t&&t.A.l||N(e,i)}}function N(e,t,n){void 0===n&&(n=!1),!e.l&&e.h.D&&e.m&&x(t,n)}function D(e,t){var n=e[G];return(n?g(n):e)[t]}function R(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function z(e){e.P||(e.P=!0,e.l&&z(e.l))}function F(e){e.o||(e.o=b(e.t))}function _(e,t,n){var r=v(t)?k("MapSet").F(t,n):y(t)?k("MapSet").T(t,n):e.O?function(e,t){var n=Array.isArray(e),r={i:n?1:0,A:t?t.A:P(),P:!1,I:!1,R:{},l:t,t:e,k:null,o:null,j:null,C:!1},i=r,o=ee;n&&(i=[r],o=te);var a=Proxy.revocable(i,o),s=a.revoke,l=a.proxy;return r.k=l,r.j=s,l}(t,n):k("ES5").J(t,n);return(n?n.A:P()).p.push(r),r}function I(e){return l(e)||s(22,e),function e(t){if(!u(t))return t;var n,r=t[G],i=d(t);if(r){if(!r.P&&(r.i<4||!k("ES5").K(r)))return r.t;r.I=!0,n=V(t,i),r.I=!1}else n=V(t,i);return c(n,function(t,i){r&&h(r.t,t)===i||p(n,t,e(i))}),3===i?new Set(n):n}(e)}function V(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return b(e)}var B,U,H="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),$="undefined"!=typeof Map,q="undefined"!=typeof Set,W="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,Q=H?Symbol.for("immer-nothing"):((B={})["immer-nothing"]=!0,B),K=H?Symbol.for("immer-draftable"):"__$immer_draftable",G=H?Symbol.for("immer-state"):"__$immer_state",Y=("undefined"!=typeof Symbol&&Symbol.iterator,""+Object.prototype.constructor),X="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,Z=Object.getOwnPropertyDescriptors||function(e){var t={};return X(e).forEach(function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)}),t},J={},ee={get:function(e,t){if(t===G)return e;var n=g(e);if(!f(n,t))return function(e,t,n){var r,i=R(t,n);return i?"value"in i?i.value:null===(r=i.get)||void 0===r?void 0:r.call(e.k):void 0}(e,n,t);var r=n[t];return e.I||!u(r)?r:r===D(e.t,t)?(F(e),e.o[t]=_(e.A.h,r,e)):r},has:function(e,t){return t in g(e)},ownKeys:function(e){return Reflect.ownKeys(g(e))},set:function(e,t,n){var r=R(g(e),t);if(null==r?void 0:r.set)return r.set.call(e.k,n),!0;if(!e.P){var i=D(g(e),t),o=null==i?void 0:i[G];if(o&&o.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(m(n,i)&&(void 0!==n||f(e.t,t)))return!0;F(e),z(e)}return e.o[t]===n&&(void 0!==n||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return void 0!==D(e.t,t)||t in e.t?(e.R[t]=!1,F(e),z(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=g(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty:function(){s(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){s(12)}},te={};c(ee,function(e,t){te[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),te.deleteProperty=function(e,t){return te.set.call(this,e,t,void 0)},te.set=function(e,t,n){return ee.set.call(this,e[0],t,n,e[0])};var ne=function(){function e(e){var t=this;this.O=W,this.D=!0,this.produce=function(e,n,r){if("function"==typeof e&&"function"!=typeof n){var i=n;n=e;var o=t;return function(e){var t=this;void 0===e&&(e=i);for(var r=arguments.length,a=Array(r>1?r-1:0),s=1;s<r;s++)a[s-1]=arguments[s];return o.produce(e,function(e){var r;return(r=n).call.apply(r,[t,e].concat(a))})}}var a;if("function"!=typeof n&&s(6),void 0!==r&&"function"!=typeof r&&s(7),u(e)){var l=A(t),c=_(t,e,void 0),d=!0;try{a=n(c),d=!1}finally{d?E(l):T(l)}return"undefined"!=typeof Promise&&a instanceof Promise?a.then(function(e){return C(l,r),O(e,l)},function(e){throw E(l),e}):(C(l,r),O(a,l))}if(!e||"object"!=typeof e){if(void 0===(a=n(e))&&(a=e),a===Q&&(a=void 0),t.D&&x(a,!0),r){var f=[],h=[];k("Patches").M(e,a,f,h),r(f,h)}return a}s(21,e)},this.produceWithPatches=function(e,n){if("function"==typeof e)return function(n){for(var r=arguments.length,i=Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];return t.produceWithPatches(n,function(t){return e.apply(void 0,[t].concat(i))})};var r,i,o=t.produce(e,n,function(e,t){r=e,i=t});return"undefined"!=typeof Promise&&o instanceof Promise?o.then(function(e){return[e,r,i]}):[o,r,i]},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var t=e.prototype;return t.createDraft=function(e){u(e)||s(8),l(e)&&(e=I(e));var t=A(this),n=_(this,e,void 0);return n[G].C=!0,T(t),n},t.finishDraft=function(e,t){var n=(e&&e[G]).A;return C(n,t),O(void 0,n)},t.setAutoFreeze=function(e){this.D=e},t.setUseProxies=function(e){e&&!W&&s(20),this.O=e},t.applyPatches=function(e,t){var n;for(n=t.length-1;n>=0;n--){var r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));var i=k("Patches").$;return l(e)?i(e,t):this.produce(e,function(e){return i(e,t)})},e}(),re=new ne,ie=re.produce;re.produceWithPatches.bind(re),re.setAutoFreeze.bind(re),re.setUseProxies.bind(re),re.applyPatches.bind(re),re.createDraft.bind(re),re.finishDraft.bind(re);const oe=e=>(t,n,r)=>(r.setState=function(e,n){const r="function"===typeof e?ie(e):e;for(var i=arguments.length,o=new Array(i>2?i-2:0),a=2;a<i;a++)o[a-2]=arguments[a];return t(r,n,...o)},e(r.setState,n,r)),ae=(0,r.vt)(a(oe((e,t)=>({isLoading:!1,error:null,theme:"dark",language:"ar",settings:{quality:"auto",autoplay:!1,volume:100,muted:!1,hideControls:!1,adBlock:!0,notifications:!0,saveHistory:!0,darkTheme:!0,rtlLayout:!0,fontSize:"medium",animationsEnabled:!0},currentVideo:{id:null,title:"",channel:"",duration:0,currentTime:0,isPlaying:!1,isPaused:!1,isBuffering:!1,quality:"auto",volume:100,muted:!1,fullscreen:!1},search:{query:"",results:[],isSearching:!1,history:[],suggestions:[]},sidebar:{isOpen:!0,activeTab:"search",width:300},stats:{videosWatched:0,adsBlocked:0,timeSpent:0,sessionsCount:0,lastSession:null},developer:{revenue:0,impressions:0,clicks:0,conversionRate:0},setLoading:t=>e(e=>{e.isLoading=t}),setError:t=>e(e=>{e.error=t}),clearError:()=>e(e=>{e.error=null}),setTheme:t=>e(e=>{e.theme=t,e.settings.darkTheme="dark"===t}),setLanguage:t=>e(e=>{e.language=t}),updateSettings:t=>e(e=>{e.settings={...e.settings,...t}}),resetSettings:()=>e(e=>{e.settings={quality:"auto",autoplay:!1,volume:100,muted:!1,hideControls:!1,adBlock:!0,notifications:!0,saveHistory:!0,darkTheme:!0,rtlLayout:!0,fontSize:"medium",animationsEnabled:!0}}),setCurrentVideo:t=>e(e=>{e.currentVideo={...e.currentVideo,...t}}),updateVideoState:t=>e(e=>{e.currentVideo={...e.currentVideo,...t}}),clearCurrentVideo:()=>e(e=>{e.currentVideo={id:null,title:"",channel:"",duration:0,currentTime:0,isPlaying:!1,isPaused:!1,isBuffering:!1,quality:"auto",volume:100,muted:!1,fullscreen:!1}}),setSearchQuery:t=>e(e=>{e.search.query=t}),setSearchResults:t=>e(e=>{e.search.results=t}),setSearching:t=>e(e=>{e.search.isSearching=t}),addToSearchHistory:t=>e(e=>{t&&!e.search.history.includes(t)&&(e.search.history.unshift(t),e.search.history.length>10&&(e.search.history=e.search.history.slice(0,10)))}),clearSearchHistory:()=>e(e=>{e.search.history=[]}),toggleSidebar:()=>e(e=>{e.sidebar.isOpen=!e.sidebar.isOpen}),setSidebarTab:t=>e(e=>{e.sidebar.activeTab=t}),setSidebarWidth:t=>e(e=>{e.sidebar.width=t}),incrementVideosWatched:()=>e(e=>{e.stats.videosWatched+=1}),incrementAdsBlocked:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;return e(e=>{e.stats.adsBlocked+=t})},addTimeSpent:t=>e(e=>{e.stats.timeSpent+=t}),incrementSessions:()=>e(e=>{e.stats.sessionsCount+=1,e.stats.lastSession=(new Date).toISOString()}),resetStats:()=>e(e=>{e.stats={videosWatched:0,adsBlocked:0,timeSpent:0,sessionsCount:0,lastSession:null}}),addRevenue:t=>e(e=>{e.developer.revenue+=t}),incrementImpressions:()=>e(e=>{e.developer.impressions+=1}),incrementClicks:()=>e(e=>{e.developer.clicks+=1,e.developer.conversionRate=e.developer.clicks/e.developer.impressions}),exportData:()=>{const e=t();return{settings:e.settings,stats:e.stats,searchHistory:e.search.history,exportDate:(new Date).toISOString()}},importData:t=>e(e=>{t.settings&&(e.settings={...e.settings,...t.settings}),t.stats&&(e.stats={...e.stats,...t.stats}),t.searchHistory&&(e.search.history=t.searchHistory)}),resetApp:()=>e(e=>{const t=ae.getState();Object.keys(t).forEach(n=>{"function"!==typeof t[n]&&(e[n]=t[n])})})})),{name:"youtube-player-pro-storage",storage:i(()=>{var e;return null!==(e=window.electronAPI)&&void 0!==e&&e.storage?{getItem:e=>window.electronAPI.storage.get(e),setItem:(e,t)=>window.electronAPI.storage.set(e,t),removeItem:e=>window.electronAPI.storage.remove(e)}:localStorage}),partialize:e=>({settings:e.settings,stats:e.stats,search:{history:e.search.history},sidebar:e.sidebar,theme:e.theme,language:e.language})})),se=ae},1153:(e,t,n)=>{"use strict";var r=n(5043),i=Symbol.for("react.element"),o=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,o={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)a.call(t,r)&&!l.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:i,type:e,key:u,ref:c,props:o,_owner:s.current}}t.Fragment=o,t.jsx=u,t.jsxs=u},1228:(e,t,n)=>{"use strict";n.d(t,{P:()=>Wo});var r=n(5043);const i=(0,r.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),o=(0,r.createContext)({});var a=n(9674),s=n(293);const l=(0,r.createContext)({strict:!1}),u=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),c="data-"+u("framerAppearId");function d(e){return e&&"object"===typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function f(e){return"string"===typeof e||Array.isArray(e)}function h(e){return null!==e&&"object"===typeof e&&"function"===typeof e.start}const p=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],m=["initial",...p];function v(e){return h(e.animate)||m.some(t=>f(e[t]))}function y(e){return Boolean(v(e)||e.variants)}function g(e){const{initial:t,animate:n}=function(e,t){if(v(e)){const{initial:t,animate:n}=e;return{initial:!1===t||f(t)?t:void 0,animate:f(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,r.useContext)(o));return(0,r.useMemo)(()=>({initial:t,animate:n}),[b(t),b(n)])}function b(e){return Array.isArray(e)?e.join(" "):e}const x={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},w={};for(const Ko in x)w[Ko]={isEnabled:e=>x[Ko].some(t=>!!e[t])};var S=n(4735),k=n(2190);const P=(0,r.createContext)({}),C=Symbol.for("motionComponentSymbol");function E(e){let{preloadedFeatures:t,createVisualElement:n,useRender:u,useVisualState:f,Component:h}=e;t&&function(e){for(const t in e)w[t]={...w[t],...e[t]}}(t);const p=(0,r.forwardRef)(function(e,p){let m;const v={...(0,r.useContext)(i),...e,layoutId:T(e)},{isStatic:y}=v,b=g(e),x=f(e,y);if(!y&&S.B){b.visualElement=function(e,t,n,u){const{visualElement:d}=(0,r.useContext)(o),f=(0,r.useContext)(l),h=(0,r.useContext)(a.t),p=(0,r.useContext)(i).reducedMotion,m=(0,r.useRef)();u=u||f.renderer,!m.current&&u&&(m.current=u(e,{visualState:t,parent:d,props:n,presenceContext:h,blockInitialAnimation:!!h&&!1===h.initial,reducedMotionConfig:p}));const v=m.current;(0,r.useInsertionEffect)(()=>{v&&v.update(n,h)});const y=(0,r.useRef)(Boolean(n[c]&&!window.HandoffComplete));return(0,s.E)(()=>{v&&(v.render(),y.current&&v.animationState&&v.animationState.animateChanges())}),(0,r.useEffect)(()=>{v&&(v.updateFeatures(),!y.current&&v.animationState&&v.animationState.animateChanges(),y.current&&(y.current=!1,window.HandoffComplete=!0))}),v}(h,x,v,n);const e=(0,r.useContext)(P),u=(0,r.useContext)(l).strict;b.visualElement&&(m=b.visualElement.loadFeatures(v,u,t,e))}return r.createElement(o.Provider,{value:b},m&&b.visualElement?r.createElement(m,{visualElement:b.visualElement,...v}):null,u(h,e,function(e,t,n){return(0,r.useCallback)(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&("function"===typeof n?n(r):d(n)&&(n.current=r))},[t])}(x,b.visualElement,p),x,y,b.visualElement))});return p[C]=h,p}function T(e){let{layoutId:t}=e;const n=(0,r.useContext)(k.L).id;return n&&void 0!==t?n+"-"+t:t}function A(e){function t(t){return E(e(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}))}if("undefined"===typeof Proxy)return t;const n=new Map;return new Proxy(t,{get:(e,r)=>(n.has(r)||n.set(r,t(r)),n.get(r))})}const j=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function O(e){return"string"===typeof e&&!e.includes("-")&&!!(j.indexOf(e)>-1||/[A-Z]/.test(e))}const M={};const L=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],N=new Set(L);function D(e,t){let{layout:n,layoutId:r}=t;return N.has(e)||e.startsWith("origin")||(n||void 0!==r)&&(!!M[e]||"opacity"===e)}const R=e=>Boolean(e&&e.getVelocity),z={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},F=L.length;const _=e=>t=>"string"===typeof t&&t.startsWith(e),I=_("--"),V=_("var(--"),B=(e,t)=>t&&"number"===typeof e?t.transform(e):e,U=(e,t,n)=>Math.min(Math.max(n,e),t),H={test:e=>"number"===typeof e,parse:parseFloat,transform:e=>e},$={...H,transform:e=>U(0,1,e)},q={...H,default:1},W=e=>Math.round(1e5*e)/1e5,Q=/(-)?([\d]*\.?[\d])+/g,K=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,G=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function Y(e){return"string"===typeof e}const X=e=>({test:t=>Y(t)&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),Z=X("deg"),J=X("%"),ee=X("px"),te=X("vh"),ne=X("vw"),re={...J,parse:e=>J.parse(e)/100,transform:e=>J.transform(100*e)},ie={...H,transform:Math.round},oe={borderWidth:ee,borderTopWidth:ee,borderRightWidth:ee,borderBottomWidth:ee,borderLeftWidth:ee,borderRadius:ee,radius:ee,borderTopLeftRadius:ee,borderTopRightRadius:ee,borderBottomRightRadius:ee,borderBottomLeftRadius:ee,width:ee,maxWidth:ee,height:ee,maxHeight:ee,size:ee,top:ee,right:ee,bottom:ee,left:ee,padding:ee,paddingTop:ee,paddingRight:ee,paddingBottom:ee,paddingLeft:ee,margin:ee,marginTop:ee,marginRight:ee,marginBottom:ee,marginLeft:ee,rotate:Z,rotateX:Z,rotateY:Z,rotateZ:Z,scale:q,scaleX:q,scaleY:q,scaleZ:q,skew:Z,skewX:Z,skewY:Z,distance:ee,translateX:ee,translateY:ee,translateZ:ee,x:ee,y:ee,z:ee,perspective:ee,transformPerspective:ee,opacity:$,originX:re,originY:re,originZ:ee,zIndex:ie,fillOpacity:$,strokeOpacity:$,numOctaves:ie};function ae(e,t,n,r){const{style:i,vars:o,transform:a,transformOrigin:s}=e;let l=!1,u=!1,c=!0;for(const d in t){const e=t[d];if(I(d)){o[d]=e;continue}const n=oe[d],r=B(e,n);if(N.has(d)){if(l=!0,a[d]=r,!c)continue;e!==(n.default||0)&&(c=!1)}else d.startsWith("origin")?(u=!0,s[d]=r):i[d]=r}if(t.transform||(l||r?i.transform=function(e,t,n,r){let{enableHardwareAcceleration:i=!0,allowTransformNone:o=!0}=t,a="";for(let s=0;s<F;s++){const t=L[s];void 0!==e[t]&&(a+=`${z[t]||t}(${e[t]}) `)}return i&&!e.z&&(a+="translateZ(0)"),a=a.trim(),r?a=r(e,n?"":a):o&&n&&(a="none"),a}(e.transform,n,c,r):i.transform&&(i.transform="none")),u){const{originX:e="50%",originY:t="50%",originZ:n=0}=s;i.transformOrigin=`${e} ${t} ${n}`}}const se=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function le(e,t,n){for(const r in t)R(t[r])||D(r,n)||(e[r]=t[r])}function ue(e,t,n){const i={};return le(i,e.style||{},e),Object.assign(i,function(e,t,n){let{transformTemplate:i}=e;return(0,r.useMemo)(()=>{const e={style:{},transform:{},transformOrigin:{},vars:{}};return ae(e,t,{enableHardwareAcceleration:!n},i),Object.assign({},e.vars,e.style)},[t])}(e,t,n)),e.transformValues?e.transformValues(i):i}function ce(e,t,n){const r={},i=ue(e,t,n);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===e.drag?"none":"pan-"+("x"===e.drag?"y":"x")),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const de=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function fe(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||de.has(e)}let he=e=>!fe(e);try{(pe=require("@emotion/is-prop-valid").default)&&(he=e=>e.startsWith("on")?!fe(e):pe(e))}catch(Qo){}var pe;function me(e,t,n){return"string"===typeof e?e:ee.transform(t+n*e)}const ve={offset:"stroke-dashoffset",array:"stroke-dasharray"},ye={offset:"strokeDashoffset",array:"strokeDasharray"};function ge(e,t,n,r,i){let{attrX:o,attrY:a,attrScale:s,originX:l,originY:u,pathLength:c,pathSpacing:d=1,pathOffset:f=0,...h}=t;if(ae(e,h,n,i),r)return void(e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox));e.attrs=e.style,e.style={};const{attrs:p,style:m,dimensions:v}=e;p.transform&&(v&&(m.transform=p.transform),delete p.transform),v&&(void 0!==l||void 0!==u||m.transform)&&(m.transformOrigin=function(e,t,n){return`${me(t,e.x,e.width)} ${me(n,e.y,e.height)}`}(v,void 0!==l?l:.5,void 0!==u?u:.5)),void 0!==o&&(p.x=o),void 0!==a&&(p.y=a),void 0!==s&&(p.scale=s),void 0!==c&&function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=!(arguments.length>4&&void 0!==arguments[4])||arguments[4];e.pathLength=1;const o=i?ve:ye;e[o.offset]=ee.transform(-r);const a=ee.transform(t),s=ee.transform(n);e[o.array]=`${a} ${s}`}(p,c,d,f,!1)}const be=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),xe=e=>"string"===typeof e&&"svg"===e.toLowerCase();function we(e,t,n,i){const o=(0,r.useMemo)(()=>{const n={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return ge(n,t,{enableHardwareAcceleration:!1},xe(i),e.transformTemplate),{...n.attrs,style:{...n.style}}},[t]);if(e.style){const t={};le(t,e.style,e),o.style={...t,...o.style}}return o}function Se(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return(t,n,i,o,a)=>{let{latestValues:s}=o;const l=(O(t)?we:ce)(n,s,a,t),u=function(e,t,n){const r={};for(const i in e)"values"===i&&"object"===typeof e.values||(he(i)||!0===n&&fe(i)||!t&&!fe(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"===typeof t,e),c={...u,...l,ref:i},{children:d}=n,f=(0,r.useMemo)(()=>R(d)?d.get():d,[d]);return(0,r.createElement)(t,{...c,children:f})}}function ke(e,t,n,r){let{style:i,vars:o}=t;Object.assign(e.style,i,r&&r.getProjectionStyles(n));for(const a in o)e.style.setProperty(a,o[a])}const Pe=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Ce(e,t,n,r){ke(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(Pe.has(i)?i:u(i),t.attrs[i])}function Ee(e,t){const{style:n}=e,r={};for(const i in n)(R(n[i])||t.style&&R(t.style[i])||D(i,e))&&(r[i]=n[i]);return r}function Te(e,t){const n=Ee(e,t);for(const r in e)if(R(e[r])||R(t[r])){n[-1!==L.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]}return n}function Ae(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{};return"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,i)),"string"===typeof t&&(t=e.variants&&e.variants[t]),"function"===typeof t&&(t=t(void 0!==n?n:e.custom,r,i)),t}var je=n(4930);const Oe=e=>Array.isArray(e),Me=e=>Oe(e)?e[e.length-1]||0:e;function Le(e){const t=R(e)?e.get():e;return n=t,Boolean(n&&"object"===typeof n&&n.mix&&n.toValue)?t.toValue():t;var n}const Ne=e=>(t,n)=>{const i=(0,r.useContext)(o),s=(0,r.useContext)(a.t),l=()=>function(e,t,n,r){let{scrapeMotionValuesFromProps:i,createRenderState:o,onMount:a}=e;const s={latestValues:De(t,n,r,i),renderState:o()};return a&&(s.mount=e=>a(t,e,s)),s}(e,t,i,s);return n?l():(0,je.M)(l)};function De(e,t,n,r){const i={},o=r(e,{});for(const f in o)i[f]=Le(o[f]);let{initial:a,animate:s}=e;const l=v(e),u=y(e);t&&u&&!l&&!1!==e.inherit&&(void 0===a&&(a=t.initial),void 0===s&&(s=t.animate));let c=!!n&&!1===n.initial;c=c||!1===a;const d=c?s:a;if(d&&"boolean"!==typeof d&&!h(d)){(Array.isArray(d)?d:[d]).forEach(t=>{const n=Ae(e,t);if(!n)return;const{transitionEnd:r,transition:o,...a}=n;for(const e in a){let t=a[e];if(Array.isArray(t)){t=t[c?t.length-1:0]}null!==t&&(i[e]=t)}for(const e in r)i[e]=r[e]})}return i}var Re=n(7065);const ze={useVisualState:Ne({scrapeMotionValuesFromProps:Te,createRenderState:be,onMount:(e,t,n)=>{let{renderState:r,latestValues:i}=n;Re.Gt.read(()=>{try{r.dimensions="function"===typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){r.dimensions={x:0,y:0,width:0,height:0}}}),Re.Gt.render(()=>{ge(r,i,{enableHardwareAcceleration:!1},xe(t.tagName),e.transformTemplate),Ce(t,r)})}})},Fe={useVisualState:Ne({scrapeMotionValuesFromProps:Ee,createRenderState:se})};function _e(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{passive:!0};return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const Ie=e=>"mouse"===e.pointerType?"number"!==typeof e.button||e.button<=0:!1!==e.isPrimary;function Ve(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"page";return{point:{x:e[t+"X"],y:e[t+"Y"]}}}function Be(e,t,n,r){return _e(e,t,(e=>t=>Ie(t)&&e(t,Ve(t)))(n),r)}const Ue=(e,t)=>n=>t(e(n)),He=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.reduce(Ue)};function $e(e){let t=null;return()=>{const n=()=>{t=null};return null===t&&(t=e,n)}}const qe=$e("dragHorizontal"),We=$e("dragVertical");function Qe(e){let t=!1;if("y"===e)t=We();else if("x"===e)t=qe();else{const e=qe(),n=We();e&&n?t=()=>{e(),n()}:(e&&e(),n&&n())}return t}function Ke(){const e=Qe(!0);return!e||(e(),!1)}class Ge{constructor(e){this.isMounted=!1,this.node=e}update(){}}function Ye(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End");return Be(e.current,n,(n,i)=>{if("touch"===n.pointerType||Ke())return;const o=e.getProps();e.animationState&&o.whileHover&&e.animationState.setActive("whileHover",t),o[r]&&Re.Gt.update(()=>o[r](n,i))},{passive:!e.getProps()[r]})}const Xe=(e,t)=>!!t&&(e===t||Xe(e,t.parentElement));var Ze=n(1892);function Je(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,Ve(n))}const et=new WeakMap,tt=new WeakMap,nt=e=>{const t=et.get(e.target);t&&t(e)},rt=e=>{e.forEach(nt)};function it(e,t,n){const r=function(e){let{root:t,...n}=e;const r=t||document;tt.has(r)||tt.set(r,{});const i=tt.get(r),o=JSON.stringify(n);return i[o]||(i[o]=new IntersectionObserver(rt,{root:t,...n})),i[o]}(t);return et.set(e,n),r.observe(e),()=>{et.delete(e),r.unobserve(e)}}const ot={some:0,all:1};const at={inView:{Feature:class extends Ge{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:n,threshold:"number"===typeof r?r:ot[r]};return it(this.node.current,o,e=>{const{isIntersecting:t}=e;if(this.isInView===t)return;if(this.isInView=t,i&&!t&&this.hasEnteredView)return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);const{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),o=t?n:r;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"===typeof IntersectionObserver)return;const{props:e,prevProps:t}=this.node,n=["amount","margin","root"].some(function(e){let{viewport:t={}}=e,{viewport:n={}}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e=>t[e]!==n[e]}(e,t));n&&this.startObserver()}unmount(){}}},tap:{Feature:class extends Ge{constructor(){super(...arguments),this.removeStartListeners=Ze.l,this.removeEndListeners=Ze.l,this.removeAccessibleListeners=Ze.l,this.startPointerPress=(e,t)=>{if(this.isPressing)return;this.removeEndListeners();const n=this.node.getProps(),r=Be(window,"pointerup",(e,t)=>{if(!this.checkPressEnd())return;const{onTap:n,onTapCancel:r,globalTapTarget:i}=this.node.getProps();Re.Gt.update(()=>{i||Xe(this.node.current,e.target)?n&&n(e,t):r&&r(e,t)})},{passive:!(n.onTap||n.onPointerUp)}),i=Be(window,"pointercancel",(e,t)=>this.cancelPress(e,t),{passive:!(n.onTapCancel||n.onPointerCancel)});this.removeEndListeners=He(r,i),this.startPress(e,t)},this.startAccessiblePress=()=>{const e=_e(this.node.current,"keydown",e=>{if("Enter"!==e.key||this.isPressing)return;this.removeEndListeners(),this.removeEndListeners=_e(this.node.current,"keyup",e=>{"Enter"===e.key&&this.checkPressEnd()&&Je("up",(e,t)=>{const{onTap:n}=this.node.getProps();n&&Re.Gt.update(()=>n(e,t))})}),Je("down",(e,t)=>{this.startPress(e,t)})}),t=_e(this.node.current,"blur",()=>{this.isPressing&&Je("cancel",(e,t)=>this.cancelPress(e,t))});this.removeAccessibleListeners=He(e,t)}}startPress(e,t){this.isPressing=!0;const{onTapStart:n,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),n&&Re.Gt.update(()=>n(e,t))}checkPressEnd(){this.removeEndListeners(),this.isPressing=!1;return this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!Ke()}cancelPress(e,t){if(!this.checkPressEnd())return;const{onTapCancel:n}=this.node.getProps();n&&Re.Gt.update(()=>n(e,t))}mount(){const e=this.node.getProps(),t=Be(e.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),n=_e(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=He(t,n)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}},focus:{Feature:class extends Ge{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=He(_e(this.node.current,"focus",()=>this.onFocus()),_e(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}},hover:{Feature:class extends Ge{mount(){this.unmount=He(Ye(this.node,!0),Ye(this.node,!1))}unmount(){}}}};function st(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function lt(e,t,n){const r=e.getProps();return Ae(r,t,void 0!==n?n:r.custom,function(e){const t={};return e.values.forEach((e,n)=>t[n]=e.get()),t}(e),function(e){const t={};return e.values.forEach((e,n)=>t[n]=e.getVelocity()),t}(e))}var ut=n(8129);const ct=e=>1e3*e,dt=e=>e/1e3,ft=!1,ht=e=>Array.isArray(e)&&"number"===typeof e[0];function pt(e){return Boolean(!e||"string"===typeof e&&vt[e]||ht(e)||Array.isArray(e)&&e.every(pt))}const mt=e=>{let[t,n,r,i]=e;return`cubic-bezier(${t}, ${n}, ${r}, ${i})`},vt={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:mt([0,.65,.55,1]),circOut:mt([.55,0,1,.45]),backIn:mt([.31,.01,.66,-.59]),backOut:mt([.33,1.53,.69,.99])};function yt(e){if(e)return ht(e)?mt(e):Array.isArray(e)?e.map(yt):vt[e]}const gt=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function bt(e,t,n,r){if(e===t&&n===r)return Ze.l;const i=t=>function(e,t,n,r,i){let o,a,s=0;do{a=t+(n-t)/2,o=gt(a,r,i)-e,o>0?n=a:t=a}while(Math.abs(o)>1e-7&&++s<12);return a}(t,0,1,e,n);return e=>0===e||1===e?e:gt(i(e),t,r)}const xt=bt(.42,0,1,1),wt=bt(0,0,.58,1),St=bt(.42,0,.58,1),kt=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Pt=e=>t=>1-e(1-t),Ct=e=>1-Math.sin(Math.acos(e)),Et=Pt(Ct),Tt=kt(Ct),At=bt(.33,1.53,.69,.99),jt=Pt(At),Ot=kt(jt),Mt={linear:Ze.l,easeIn:xt,easeInOut:St,easeOut:wt,circIn:Ct,circInOut:Tt,circOut:Et,backIn:jt,backInOut:Ot,backOut:At,anticipate:e=>(e*=2)<1?.5*jt(e):.5*(2-Math.pow(2,-10*(e-1)))},Lt=e=>{if(Array.isArray(e)){(0,ut.V)(4===e.length,"Cubic bezier arrays must contain four numerical values.");const[t,n,r,i]=e;return bt(t,n,r,i)}return"string"===typeof e?((0,ut.V)(void 0!==Mt[e],`Invalid easing type '${e}'`),Mt[e]):e},Nt=(e,t)=>n=>Boolean(Y(n)&&G.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Dt=(e,t,n)=>r=>{if(!Y(r))return r;const[i,o,a,s]=r.match(Q);return{[e]:parseFloat(i),[t]:parseFloat(o),[n]:parseFloat(a),alpha:void 0!==s?parseFloat(s):1}},Rt={...H,transform:e=>Math.round((e=>U(0,255,e))(e))},zt={test:Nt("rgb","red"),parse:Dt("red","green","blue"),transform:e=>{let{red:t,green:n,blue:r,alpha:i=1}=e;return"rgba("+Rt.transform(t)+", "+Rt.transform(n)+", "+Rt.transform(r)+", "+W($.transform(i))+")"}};const Ft={test:Nt("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:zt.transform},_t={test:Nt("hsl","hue"),parse:Dt("hue","saturation","lightness"),transform:e=>{let{hue:t,saturation:n,lightness:r,alpha:i=1}=e;return"hsla("+Math.round(t)+", "+J.transform(W(n))+", "+J.transform(W(r))+", "+W($.transform(i))+")"}},It={test:e=>zt.test(e)||Ft.test(e)||_t.test(e),parse:e=>zt.test(e)?zt.parse(e):_t.test(e)?_t.parse(e):Ft.parse(e),transform:e=>Y(e)?e:e.hasOwnProperty("red")?zt.transform(e):_t.transform(e)},Vt=(e,t,n)=>-n*e+n*t+e;function Bt(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+6*(t-e)*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}const Ut=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},Ht=[Ft,zt,_t];function $t(e){const t=(n=e,Ht.find(e=>e.test(n)));var n;(0,ut.V)(Boolean(t),`'${e}' is not an animatable color. Use the equivalent color code instead.`);let r=t.parse(e);return t===_t&&(r=function(e){let{hue:t,saturation:n,lightness:r,alpha:i}=e;t/=360,n/=100,r/=100;let o=0,a=0,s=0;if(n){const e=r<.5?r*(1+n):r+n-r*n,i=2*r-e;o=Bt(i,e,t+1/3),a=Bt(i,e,t),s=Bt(i,e,t-1/3)}else o=a=s=r;return{red:Math.round(255*o),green:Math.round(255*a),blue:Math.round(255*s),alpha:i}}(r)),r}const qt=(e,t)=>{const n=$t(e),r=$t(t),i={...n};return e=>(i.red=Ut(n.red,r.red,e),i.green=Ut(n.green,r.green,e),i.blue=Ut(n.blue,r.blue,e),i.alpha=Vt(n.alpha,r.alpha,e),zt.transform(i))};const Wt={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:Ze.l},Qt={regex:K,countKey:"Colors",token:"${c}",parse:It.parse},Kt={regex:Q,countKey:"Numbers",token:"${n}",parse:H.parse};function Gt(e,t){let{regex:n,countKey:r,token:i,parse:o}=t;const a=e.tokenised.match(n);a&&(e["num"+r]=a.length,e.tokenised=e.tokenised.replace(n,i),e.values.push(...a.map(o)))}function Yt(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&Gt(n,Wt),Gt(n,Qt),Gt(n,Kt),n}function Xt(e){return Yt(e).values}function Zt(e){const{values:t,numColors:n,numVars:r,tokenised:i}=Yt(e),o=t.length;return e=>{let t=i;for(let i=0;i<o;i++)t=i<r?t.replace(Wt.token,e[i]):i<r+n?t.replace(Qt.token,It.transform(e[i])):t.replace(Kt.token,W(e[i]));return t}}const Jt=e=>"number"===typeof e?0:e;const en={test:function(e){var t,n;return isNaN(e)&&Y(e)&&((null===(t=e.match(Q))||void 0===t?void 0:t.length)||0)+((null===(n=e.match(K))||void 0===n?void 0:n.length)||0)>0},parse:Xt,createTransformer:Zt,getAnimatableNone:function(e){const t=Xt(e);return Zt(e)(t.map(Jt))}},tn=(e,t)=>n=>`${n>0?t:e}`;function nn(e,t){return"number"===typeof e?n=>Vt(e,t,n):It.test(e)?qt(e,t):e.startsWith("var(")?tn(e,t):an(e,t)}const rn=(e,t)=>{const n=[...e],r=n.length,i=e.map((e,n)=>nn(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}},on=(e,t)=>{const n={...e,...t},r={};for(const i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=nn(e[i],t[i]));return e=>{for(const t in r)n[t]=r[t](e);return n}},an=(e,t)=>{const n=en.createTransformer(t),r=Yt(e),i=Yt(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?He(rn(r.values,i.values),n):((0,ut.$)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tn(e,t))},sn=(e,t,n)=>{const r=t-e;return 0===r?1:(n-e)/r},ln=(e,t)=>n=>Vt(e,t,n);function un(e,t,n){const r=[],i=n||("number"===typeof(o=e[0])?ln:"string"===typeof o?It.test(o)?qt:an:Array.isArray(o)?rn:"object"===typeof o?on:ln);var o;const a=e.length-1;for(let s=0;s<a;s++){let n=i(e[s],e[s+1]);if(t){const e=Array.isArray(t)?t[s]||Ze.l:t;n=He(e,n)}r.push(n)}return r}function cn(e,t){let{clamp:n=!0,ease:r,mixer:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const o=e.length;if((0,ut.V)(o===t.length,"Both input and output ranges must be the same length"),1===o)return()=>t[0];e[0]>e[o-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=un(t,r,i),s=a.length,l=t=>{let n=0;if(s>1)for(;n<e.length-2&&!(t<e[n+1]);n++);const r=sn(e[n],e[n+1],t);return a[n](r)};return n?t=>l(U(e[0],e[o-1],t)):l}function dn(e){const t=[0];return function(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=sn(0,t,r);e.push(Vt(n,1,i))}}(t,e.length-1),t}function fn(e){let{duration:t=300,keyframes:n,times:r,ease:i="easeInOut"}=e;const o=(e=>Array.isArray(e)&&"number"!==typeof e[0])(i)?i.map(Lt):Lt(i),a={done:!1,value:n[0]},s=function(e,t){return e.map(e=>e*t)}(r&&r.length===n.length?r:dn(n),t),l=cn(s,n,{ease:Array.isArray(o)?o:(u=n,c=o,u.map(()=>c||St).splice(0,u.length-1))});var u,c;return{calculatedDuration:t,next:e=>(a.value=l(e),a.done=e>=t,a)}}function hn(e,t){return t?e*(1e3/t):0}function pn(e,t,n){const r=Math.max(t-5,0);return hn(n-e(r),t-r)}const mn=.001;function vn(e){let t,n,{duration:r=800,bounce:i=.25,velocity:o=0,mass:a=1}=e;(0,ut.$)(r<=ct(10),"Spring duration must be 10 seconds or less");let s=1-i;s=U(.05,1,s),r=U(.01,10,dt(r)),s<1?(t=e=>{const t=e*s,n=t*r,i=t-o,a=gn(e,s),l=Math.exp(-n);return mn-i/a*l},n=e=>{const n=e*s*r,i=n*o+o,a=Math.pow(s,2)*Math.pow(e,2)*r,l=Math.exp(-n),u=gn(Math.pow(e,2),s);return(-t(e)+mn>0?-1:1)*((i-a)*l)/u}):(t=e=>Math.exp(-e*r)*((e-o)*r+1)-.001,n=e=>Math.exp(-e*r)*(r*r*(o-e)));const l=function(e,t,n){let r=n;for(let i=1;i<yn;i++)r-=e(r)/t(r);return r}(t,n,5/r);if(r=ct(r),isNaN(l))return{stiffness:100,damping:10,duration:r};{const e=Math.pow(l,2)*a;return{stiffness:e,damping:2*s*Math.sqrt(a*e),duration:r}}}const yn=12;function gn(e,t){return e*Math.sqrt(1-t*t)}const bn=["duration","bounce"],xn=["stiffness","damping","mass"];function wn(e,t){return t.some(t=>void 0!==e[t])}function Sn(e){let{keyframes:t,restDelta:n,restSpeed:r,...i}=e;const o=t[0],a=t[t.length-1],s={done:!1,value:o},{stiffness:l,damping:u,mass:c,duration:d,velocity:f,isResolvedFromDuration:h}=function(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!wn(e,xn)&&wn(e,bn)){const n=vn(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}({...i,velocity:-dt(i.velocity||0)}),p=f||0,m=u/(2*Math.sqrt(l*c)),v=a-o,y=dt(Math.sqrt(l/c)),g=Math.abs(v)<5;let b;if(r||(r=g?.01:2),n||(n=g?.005:.5),m<1){const e=gn(y,m);b=t=>{const n=Math.exp(-m*y*t);return a-n*((p+m*y*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}}else if(1===m)b=e=>a-Math.exp(-y*e)*(v+(p+y*v)*e);else{const e=y*Math.sqrt(m*m-1);b=t=>{const n=Math.exp(-m*y*t),r=Math.min(e*t,300);return a-n*((p+m*y*v)*Math.sinh(r)+e*v*Math.cosh(r))/e}}return{calculatedDuration:h&&d||null,next:e=>{const t=b(e);if(h)s.done=e>=d;else{let i=p;0!==e&&(i=m<1?pn(b,e,t):0);const o=Math.abs(i)<=r,l=Math.abs(a-t)<=n;s.done=o&&l}return s.value=s.done?a:t,s}}}function kn(e){let{keyframes:t,velocity:n=0,power:r=.8,timeConstant:i=325,bounceDamping:o=10,bounceStiffness:a=500,modifyTarget:s,min:l,max:u,restDelta:c=.5,restSpeed:d}=e;const f=t[0],h={done:!1,value:f},p=e=>void 0===l?u:void 0===u||Math.abs(l-e)<Math.abs(u-e)?l:u;let m=r*n;const v=f+m,y=void 0===s?v:s(v);y!==v&&(m=y-f);const g=e=>-m*Math.exp(-e/i),b=e=>y+g(e),x=e=>{const t=g(e),n=b(e);h.done=Math.abs(t)<=c,h.value=h.done?y:n};let w,S;const k=e=>{var t;(t=h.value,void 0!==l&&t<l||void 0!==u&&t>u)&&(w=e,S=Sn({keyframes:[h.value,p(h.value)],velocity:pn(b,e,h.value),damping:o,stiffness:a,restDelta:c,restSpeed:d}))};return k(0),{calculatedDuration:null,next:e=>{let t=!1;return S||void 0!==w||(t=!0,x(e),k(e)),void 0!==w&&e>w?S.next(e-w):(!t&&x(e),h)}}}const Pn=e=>{const t=t=>{let{timestamp:n}=t;return e(n)};return{start:()=>Re.Gt.update(t,!0),stop:()=>(0,Re.WG)(t),now:()=>Re.uv.isProcessing?Re.uv.timestamp:performance.now()}};function Cn(e){let t=0;let n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}const En={decay:kn,inertia:kn,tween:fn,keyframes:fn,spring:Sn};function Tn(e){let t,n,{autoplay:r=!0,delay:i=0,driver:o=Pn,keyframes:a,type:s="keyframes",repeat:l=0,repeatDelay:u=0,repeatType:c="loop",onPlay:d,onStop:f,onComplete:h,onUpdate:p,...m}=e,v=1,y=!1;const g=()=>{n=new Promise(e=>{t=e})};let b;g();const x=En[s]||fn;let w;x!==fn&&"number"!==typeof a[0]&&(w=cn([0,100],a,{clamp:!1}),a=[0,100]);const S=x({...m,keyframes:a});let k;"mirror"===c&&(k=x({...m,keyframes:[...a].reverse(),velocity:-(m.velocity||0)}));let P="idle",C=null,E=null,T=null;null===S.calculatedDuration&&l&&(S.calculatedDuration=Cn(S));const{calculatedDuration:A}=S;let j=1/0,O=1/0;null!==A&&(j=A+u,O=j*(l+1)-u);let M=0;const L=e=>{if(null===E)return;v>0&&(E=Math.min(E,e)),v<0&&(E=Math.min(e-O/v,E)),M=null!==C?C:Math.round(e-E)*v;const t=M-i*(v>=0?1:-1),n=v>=0?t<0:t>O;M=Math.max(t,0),"finished"===P&&null===C&&(M=O);let r=M,o=S;if(l){const e=Math.min(M,O)/j;let t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,t=Math.min(t,l+1);Boolean(t%2)&&("reverse"===c?(n=1-n,u&&(n-=u/j)):"mirror"===c&&(o=k)),r=U(0,1,n)*j}const s=n?{done:!1,value:a[0]}:o.next(r);w&&(s.value=w(s.value));let{done:d}=s;n||null===A||(d=v>=0?M>=O:M<=0);const f=null===C&&("finished"===P||"running"===P&&d);return p&&p(s.value),f&&R(),s},N=()=>{b&&b.stop(),b=void 0},D=()=>{P="idle",N(),t(),g(),E=T=null},R=()=>{P="finished",h&&h(),N(),t()},z=()=>{if(y)return;b||(b=o(L));const e=b.now();d&&d(),null!==C?E=e-C:E&&"finished"!==P||(E=e),"finished"===P&&g(),T=E,C=null,P="running",b.start()};r&&z();const F={then:(e,t)=>n.then(e,t),get time(){return dt(M)},set time(e){e=ct(e),M=e,null===C&&b&&0!==v?E=b.now()-e/v:C=e},get duration(){const e=null===S.calculatedDuration?Cn(S):S.calculatedDuration;return dt(e)},get speed(){return v},set speed(e){e!==v&&b&&(v=e,F.time=dt(M))},get state(){return P},play:z,pause:()=>{P="paused",C=M},stop:()=>{y=!0,"idle"!==P&&(P="idle",f&&f(),D())},cancel:()=>{null!==T&&L(T),D()},complete:()=>{P="finished"},sample:e=>(E=0,L(e))};return F}const An=function(e){let t;return()=>(void 0===t&&(t=e()),t)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),jn=new Set(["opacity","clipPath","filter","transform","backgroundColor"]);function On(e,t,n){let{onUpdate:r,onComplete:i,...o}=n;if(!(An()&&jn.has(t)&&!o.repeatDelay&&"mirror"!==o.repeatType&&0!==o.damping&&"inertia"!==o.type))return!1;let a,s,l=!1,u=!1;const c=()=>{s=new Promise(e=>{a=e})};c();let{keyframes:d,duration:f=300,ease:h,times:p}=o;if(((e,t)=>"spring"===t.type||"backgroundColor"===e||!pt(t.ease))(t,o)){const e=Tn({...o,repeat:0,delay:0});let t={done:!1,value:d[0]};const n=[];let r=0;for(;!t.done&&r<2e4;)t=e.sample(r),n.push(t.value),r+=10;p=void 0,d=n,f=r-10,h="linear"}const m=function(e,t,n){let{delay:r=0,duration:i,repeat:o=0,repeatType:a="loop",ease:s,times:l}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const u={[t]:n};l&&(u.offset=l);const c=yt(s);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"})}(e.owner.current,t,d,{...o,duration:f,ease:h,times:p}),v=()=>{u=!1,m.cancel()},y=()=>{u=!0,Re.Gt.update(v),a(),c()};m.onfinish=()=>{u||(e.set(function(e,t){let{repeat:n,repeatType:r="loop"}=t;return e[n&&"loop"!==r&&n%2===1?0:e.length-1]}(d,o)),i&&i(),y())};return{then:(e,t)=>s.then(e,t),attachTimeline:e=>(m.timeline=e,m.onfinish=null,Ze.l),get time(){return dt(m.currentTime||0)},set time(e){m.currentTime=ct(e)},get speed(){return m.playbackRate},set speed(e){m.playbackRate=e},get duration(){return dt(f)},play:()=>{l||(m.play(),(0,Re.WG)(v))},pause:()=>m.pause(),stop:()=>{if(l=!0,"idle"===m.playState)return;const{currentTime:t}=m;if(t){const n=Tn({...o,autoplay:!1});e.setWithVelocity(n.sample(t-10).value,n.sample(t).value,10)}y()},complete:()=>{u||m.finish()},cancel:y}}const Mn={type:"spring",stiffness:500,damping:25,restSpeed:10},Ln={type:"keyframes",duration:.8},Nn={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Dn=(e,t)=>{let{keyframes:n}=t;return n.length>2?Ln:N.has(e)?e.startsWith("scale")?{type:"spring",stiffness:550,damping:0===n[1]?2*Math.sqrt(550):30,restSpeed:10}:Mn:Nn},Rn=(e,t)=>"zIndex"!==e&&(!("number"!==typeof t&&!Array.isArray(t))||!("string"!==typeof t||!en.test(t)&&"0"!==t||t.startsWith("url("))),zn=new Set(["brightness","contrast","saturate","opacity"]);function Fn(e){const[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;const[r]=n.match(Q)||[];if(!r)return e;const i=n.replace(r,"");let o=zn.has(t)?1:0;return r!==n&&(o*=100),t+"("+o+i+")"}const _n=/([a-z-]*)\(.*?\)/g,In={...en,getAnimatableNone:e=>{const t=e.match(_n);return t?t.map(Fn).join(" "):e}},Vn={...oe,color:It,backgroundColor:It,outlineColor:It,fill:It,stroke:It,borderColor:It,borderTopColor:It,borderRightColor:It,borderBottomColor:It,borderLeftColor:It,filter:In,WebkitFilter:In},Bn=e=>Vn[e];function Un(e,t){let n=Bn(e);return n!==In&&(n=en),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Hn=e=>/^0[^.\s]+$/.test(e);function $n(e){return"number"===typeof e?0===e:null!==e?"none"===e||"0"===e||Hn(e):void 0}function qn(e,t){return e[t]||e.default||e}const Wn=!1,Qn=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return i=>{const o=qn(r,e)||{},a=o.delay||r.delay||0;let{elapsed:s=0}=r;s-=ct(a);const l=function(e,t,n,r){const i=Rn(t,n);let o;o=Array.isArray(n)?[...n]:[null,n];const a=void 0!==r.from?r.from:e.get();let s;const l=[];for(let u=0;u<o.length;u++)null===o[u]&&(o[u]=0===u?a:o[u-1]),$n(o[u])&&l.push(u),"string"===typeof o[u]&&"none"!==o[u]&&"0"!==o[u]&&(s=o[u]);if(i&&l.length&&s)for(let u=0;u<l.length;u++)o[l[u]]=Un(t,s);return o}(t,e,n,o),u=l[0],c=l[l.length-1],d=Rn(e,u),f=Rn(e,c);(0,ut.$)(d===f,`You are trying to animate ${e} from "${u}" to "${c}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${c} via the \`style\` property.`);let h={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...o,delay:-s,onUpdate:e=>{t.set(e),o.onUpdate&&o.onUpdate(e)},onComplete:()=>{i(),o.onComplete&&o.onComplete()}};if(function(e){let{when:t,delay:n,delayChildren:r,staggerChildren:i,staggerDirection:o,repeat:a,repeatType:s,repeatDelay:l,from:u,elapsed:c,...d}=e;return!!Object.keys(d).length}(o)||(h={...h,...Dn(e,h)}),h.duration&&(h.duration=ct(h.duration)),h.repeatDelay&&(h.repeatDelay=ct(h.repeatDelay)),!d||!f||ft||!1===o.type||Wn)return function(e){let{keyframes:t,delay:n,onUpdate:r,onComplete:i}=e;const o=()=>(r&&r(t[t.length-1]),i&&i(),{time:0,speed:1,duration:0,play:Ze.l,pause:Ze.l,stop:Ze.l,then:e=>(e(),Promise.resolve()),cancel:Ze.l,complete:Ze.l});return n?Tn({keyframes:[0,1],duration:0,delay:n,onComplete:o}):o()}(ft?{...h,delay:0}:h);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const n=On(t,e,h);if(n)return n}return Tn(h)}};function Kn(e){return Boolean(R(e)&&e.add)}const Gn=e=>/^\-?\d*\.?\d+$/.test(e);function Yn(e,t){-1===e.indexOf(t)&&e.push(t)}function Xn(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class Zn{constructor(){this.subscriptions=[]}add(e){return Yn(this.subscriptions,e),()=>Xn(this.subscriptions,e)}notify(e,t,n){const r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){const r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Jn={current:void 0};class er{constructor(e){var t=this;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var r;this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=function(e){let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t.prev=t.current,t.current=e;const{delta:r,timestamp:i}=Re.uv;t.lastUpdated!==i&&(t.timeDelta=r,t.lastUpdated=i,Re.Gt.postRender(t.scheduleVelocityCheck)),t.prev!==t.current&&t.events.change&&t.events.change.notify(t.current),t.events.velocityChange&&t.events.velocityChange.notify(t.getVelocity()),n&&t.events.renderRequest&&t.events.renderRequest.notify(t.current)},this.scheduleVelocityCheck=()=>Re.Gt.postRender(this.velocityCheck),this.velocityCheck=e=>{let{timestamp:t}=e;t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=(r=this.current,!isNaN(parseFloat(r))),this.owner=n.owner}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new Zn);const n=this.events[e].add(t);return"change"===e?()=>{n(),Re.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=e,this.timeDelta=n}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return Jn.current&&Jn.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?hn(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function tr(e,t){return new er(e,t)}const nr=e=>t=>t.test(e),rr=[H,ee,J,Z,ne,te,{test:e=>"auto"===e,parse:e=>e}],ir=e=>rr.find(nr(e)),or=[...rr,It,en],ar=e=>or.find(nr(e));function sr(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,tr(n))}function lr(e,t){const n=lt(e,t);let{transitionEnd:r={},transition:i={},...o}=n?e.makeTargetAnimatable(n,!1):{};o={...o,...r};for(const a in o){sr(e,a,Me(o[a]))}}function ur(e,t){if(!t)return;return(t[e]||t.default||t).from}function cr(e,t){let{protectedKeys:n,needsAnimating:r}=e;const i=n.hasOwnProperty(t)&&!0!==r[t];return r[t]=!1,i}function dr(e,t){const n=e.get();if(!Array.isArray(t))return n!==t;for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}function fr(e,t){let{delay:n=0,transitionOverride:r,type:i}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{transition:o=e.getDefaultTransition(),transitionEnd:a,...s}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(o=r);const u=[],d=i&&e.animationState&&e.animationState.getState()[i];for(const f in s){const t=e.getValue(f),r=s[f];if(!t||void 0===r||d&&cr(d,f))continue;const i={delay:n,elapsed:0,...qn(o||{},f)};if(window.HandoffAppearAnimations){const n=e.getProps()[c];if(n){const e=window.HandoffAppearAnimations(n,f,t,Re.Gt);null!==e&&(i.elapsed=e,i.isHandoff=!0)}}let a=!i.isHandoff&&!dr(t,r);if("spring"===i.type&&(t.getVelocity()||i.velocity)&&(a=!1),t.animation&&(a=!1),a)continue;t.start(Qn(f,t,r,e.shouldReduceMotion&&N.has(f)?{type:!1}:i));const h=t.animation;Kn(l)&&(l.add(f),h.then(()=>l.remove(f))),u.push(h)}return a&&Promise.all(u).then(()=>{a&&lr(e,a)}),u}function hr(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=lt(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const o=r?()=>Promise.all(fr(e,r,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?function(){let r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const{delayChildren:o=0,staggerChildren:a,staggerDirection:s}=i;return function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1,o=arguments.length>5?arguments[5]:void 0;const a=[],s=(e.variantChildren.size-1)*r,l=1===i?function(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r}:function(){return s-(arguments.length>0&&void 0!==arguments[0]?arguments[0]:0)*r};return Array.from(e.variantChildren).sort(pr).forEach((e,r)=>{e.notify("AnimationStart",t),a.push(hr(e,t,{...o,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,o+r,a,s,n)}:()=>Promise.resolve(),{when:s}=i;if(s){const[e,t]="beforeChildren"===s?[o,a]:[a,o];return e().then(()=>t())}return Promise.all([o(),a(n.delay)])}function pr(e,t){return e.sortNodePosition(t)}const mr=[...p].reverse(),vr=p.length;function yr(e){return t=>Promise.all(t.map(t=>{let{animation:n,options:r}=t;return function(e,t){let n,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(e.notify("AnimationStart",t),Array.isArray(t)){const i=t.map(t=>hr(e,t,r));n=Promise.all(i)}else if("string"===typeof t)n=hr(e,t,r);else{const i="function"===typeof t?lt(e,t,r.custom):t;n=Promise.all(fr(e,i,r))}return n.then(()=>e.notify("AnimationComplete",t))}(e,n,r)}))}function gr(e){let t=yr(e);const n={animate:xr(!0),whileInView:xr(),whileHover:xr(),whileTap:xr(),whileDrag:xr(),whileFocus:xr(),exit:xr()};let r=!0;const i=(t,n)=>{const r=lt(e,n);if(r){const{transition:e,transitionEnd:n,...i}=r;t={...t,...i,...n}}return t};function o(o,a){const s=e.getProps(),l=e.getVariantContext(!0)||{},u=[],c=new Set;let d={},p=1/0;for(let t=0;t<vr;t++){const m=mr[t],v=n[m],y=void 0!==s[m]?s[m]:l[m],g=f(y),b=m===a?v.isActive:null;!1===b&&(p=t);let x=y===l[m]&&y!==s[m]&&g;if(x&&r&&e.manuallyAnimateOnMount&&(x=!1),v.protectedKeys={...d},!v.isActive&&null===b||!y&&!v.prevProp||h(y)||"boolean"===typeof y)continue;let w=br(v.prevProp,y)||m===a&&v.isActive&&!x&&g||t>p&&g,S=!1;const k=Array.isArray(y)?y:[y];let P=k.reduce(i,{});!1===b&&(P={});const{prevResolvedValues:C={}}=v,E={...C,...P},T=e=>{w=!0,c.has(e)&&(S=!0,c.delete(e)),v.needsAnimating[e]=!0};for(const e in E){const t=P[e],n=C[e];if(d.hasOwnProperty(e))continue;let r=!1;r=Oe(t)&&Oe(n)?!st(t,n):t!==n,r?void 0!==t?T(e):c.add(e):void 0!==t&&c.has(e)?T(e):v.protectedKeys[e]=!0}v.prevProp=y,v.prevResolvedValues=P,v.isActive&&(d={...d,...P}),r&&e.blockInitialAnimation&&(w=!1),!w||x&&!S||u.push(...k.map(e=>({animation:e,options:{type:m,...o}})))}if(c.size){const t={};c.forEach(n=>{const r=e.getBaseTarget(n);void 0!==r&&(t[n]=r)}),u.push({animation:t})}let m=Boolean(u.length);return!r||!1!==s.initial&&s.initial!==s.animate||e.manuallyAnimateOnMount||(m=!1),r=!1,m?t(u):Promise.resolve()}return{animateChanges:o,setActive:function(t,r,i){var a;if(n[t].isActive===r)return Promise.resolve();null===(a=e.variantChildren)||void 0===a||a.forEach(e=>{var n;return null===(n=e.animationState)||void 0===n?void 0:n.setActive(t,r)}),n[t].isActive=r;const s=o(i,t);for(const e in n)n[e].protectedKeys={};return s},setAnimateFunction:function(n){t=n(e)},getState:()=>n}}function br(e,t){return"string"===typeof t?t!==e:!!Array.isArray(t)&&!st(t,e)}function xr(){return{isActive:arguments.length>0&&void 0!==arguments[0]&&arguments[0],protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}let wr=0;const Sr={animation:{Feature:class extends Ge{constructor(e){super(e),e.animationState||(e.animationState=gr(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();this.unmount(),h(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){}}},exit:{Feature:class extends Ge{constructor(){super(...arguments),this.id=wr++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:t,custom:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;const i=this.node.animationState.setActive("exit",!e,{custom:null!==n&&void 0!==n?n:this.node.getProps().custom});t&&!e&&i.then(()=>t(this.id))}mount(){const{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}}},kr=(e,t)=>Math.abs(e-t);class Pr{constructor(e,t){let{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const e=Tr(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){const n=kr(e.x,t.x),r=kr(e.y,t.y);return Math.sqrt(n**2+r**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;const{point:r}=e,{timestamp:i}=Re.uv;this.history.push({...r,timestamp:i});const{onStart:o,onMove:a}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=Cr(t,this.transformPagePoint),Re.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();const{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Tr("pointercancel"===e.type?this.lastMoveEventInfo:Cr(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,o),r&&r(e,o)},!Ie(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;const o=Cr(Ve(e),this.transformPagePoint),{point:a}=o,{timestamp:s}=Re.uv;this.history=[{...a,timestamp:s}];const{onSessionStart:l}=t;l&&l(e,Tr(o,this.history)),this.removeListeners=He(Be(this.contextWindow,"pointermove",this.handlePointerMove),Be(this.contextWindow,"pointerup",this.handlePointerUp),Be(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,Re.WG)(this.updatePoint)}}function Cr(e,t){return t?{point:t(e.point)}:e}function Er(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Tr(e,t){let{point:n}=e;return{point:n,delta:Er(n,jr(t)),offset:Er(n,Ar(t)),velocity:Or(t,.1)}}function Ar(e){return e[0]}function jr(e){return e[e.length-1]}function Or(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=jr(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>ct(t)));)n--;if(!r)return{x:0,y:0};const o=dt(i.timestamp-r.timestamp);if(0===o)return{x:0,y:0};const a={x:(i.x-r.x)/o,y:(i.y-r.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}function Mr(e){return e.max-e.min}function Lr(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.01;return Math.abs(e-t)<=n}function Nr(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5;e.origin=r,e.originPoint=Vt(t.min,t.max,e.origin),e.scale=Mr(n)/Mr(t),(Lr(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=Vt(n.min,n.max,e.origin)-e.originPoint,(Lr(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Dr(e,t,n,r){Nr(e.x,t.x,n.x,r?r.originX:void 0),Nr(e.y,t.y,n.y,r?r.originY:void 0)}function Rr(e,t,n){e.min=n.min+t.min,e.max=e.min+Mr(t)}function zr(e,t,n){e.min=t.min-n.min,e.max=e.min+Mr(t)}function Fr(e,t,n){zr(e.x,t.x,n.x),zr(e.y,t.y,n.y)}function _r(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function Ir(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}const Vr=.35;function Br(e,t,n){return{min:Ur(e,t),max:Ur(e,n)}}function Ur(e,t){return"number"===typeof e?e:e[t]||0}const Hr=()=>({x:{min:0,max:0},y:{min:0,max:0}});function $r(e){return[e("x"),e("y")]}function qr(e){let{top:t,left:n,right:r,bottom:i}=e;return{x:{min:n,max:r},y:{min:t,max:i}}}function Wr(e){return void 0===e||1===e}function Qr(e){let{scale:t,scaleX:n,scaleY:r}=e;return!Wr(t)||!Wr(n)||!Wr(r)}function Kr(e){return Qr(e)||Gr(e)||e.z||e.rotate||e.rotateX||e.rotateY}function Gr(e){return Yr(e.x)||Yr(e.y)}function Yr(e){return e&&"0%"!==e}function Xr(e,t,n){return n+t*(e-n)}function Zr(e,t,n,r,i){return void 0!==i&&(e=Xr(e,i,r)),Xr(e,n,r)+t}function Jr(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;e.min=Zr(e.min,t,n,r,i),e.max=Zr(e.max,t,n,r,i)}function ei(e,t){let{x:n,y:r}=t;Jr(e.x,n.translate,n.scale,n.originPoint),Jr(e.y,r.translate,r.scale,r.originPoint)}function ti(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function ni(e,t){e.min=e.min+t,e.max=e.max+t}function ri(e,t,n){let[r,i,o]=n;const a=void 0!==t[o]?t[o]:.5,s=Vt(e.min,e.max,a);Jr(e,t[r],t[i],s,t.scale)}const ii=["x","scaleX","originX"],oi=["y","scaleY","originY"];function ai(e,t){ri(e.x,t,ii),ri(e.y,t,oi)}function si(e,t){return qr(function(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}const li=e=>{let{current:t}=e;return t?t.ownerDocument.defaultView:null},ui=new WeakMap;class ci{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=e}start(e){let{snapToCursor:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:r}=this.getProps();this.panSession=new Pr(e,{onSessionStart:e=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(Ve(e,"page").point)},onStart:(e,t)=>{const{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Qe(n),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),$r(e=>{let t=this.getAxisMotionValue(e).get()||0;if(J.test(t)){const{projection:n}=this.visualElement;if(n&&n.layout){const r=n.layout.layoutBox[e];if(r){t=Mr(r)*(parseFloat(t)/100)}}}this.originPoint[e]=t}),i&&Re.Gt.update(()=>i(e,t),!1,!0);const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{const{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:o}=this.getProps();if(!n&&!this.openGlobalLock)return;const{offset:a}=t;if(r&&null===this.currentDirection)return this.currentDirection=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,n=null;Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x");return n}(a),void(null!==this.currentDirection&&i&&i(this.currentDirection));this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>$r(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:li(this.visualElement)})}stop(e,t){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:r}=t;this.startAnimation(r);const{onDragEnd:i}=this.getProps();i&&Re.Gt.update(()=>i(e,t))}cancel(){this.isDragging=!1;const{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){const{drag:r}=this.getProps();if(!n||!di(e,r,this.currentDirection))return;const i=this.getAxisMotionValue(e);let o=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(o=function(e,t,n){let{min:r,max:i}=t;return void 0!==r&&e<r?e=n?Vt(r,e,n.min):Math.max(e,r):void 0!==i&&e>i&&(e=n?Vt(i,e,n.max):Math.min(e,i)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){var e;const{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&d(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!t||!r)&&function(e,t){let{top:n,left:r,bottom:i,right:o}=t;return{x:_r(e.x,r,o),y:_r(e.y,n,i)}}(r.layoutBox,t),this.elastic=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Vr;return!1===e?e=0:!0===e&&(e=Vr),{x:Br(e,"left","right"),y:Br(e,"top","bottom")}}(n),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&$r(e=>{this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){const n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:t}=this.getProps();if(!e||!d(e))return!1;const n=e.current;(0,ut.V)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");const{projection:r}=this.visualElement;if(!r||!r.layout)return!1;const i=function(e,t,n){const r=si(e,n),{scroll:i}=t;return i&&(ni(r.x,i.offset.x),ni(r.y,i.offset.y)),r}(n,r.root,this.visualElement.getTransformPagePoint());let o=function(e,t){return{x:Ir(e.x,t.x),y:Ir(e.y,t.y)}}(r.layout.layoutBox,i);if(t){const e=t(function(e){let{x:t,y:n}=e;return{top:n.min,right:t.max,bottom:n.max,left:t.min}}(o));this.hasMutatedConstraints=!!e,e&&(o=qr(e))}return o}startAnimation(e){const{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{},l=$r(a=>{if(!di(a,t,this.currentDirection))return;let l=s&&s[a]||{};o&&(l={min:0,max:0});const u=r?200:1e6,c=r?40:1e7,d={type:"inertia",velocity:n?e[a]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,d)});return Promise.all(l).then(a)}startAxisValueAnimation(e,t){const n=this.getAxisMotionValue(e);return n.start(Qn(e,n,0,t))}stopAnimation(){$r(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){$r(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){const t="_drag"+e.toUpperCase(),n=this.visualElement.getProps(),r=n[t];return r||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){$r(t=>{const{drag:n}=this.getProps();if(!di(t,n,this.currentDirection))return;const{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){const{min:n,max:o}=r.layout.layoutBox[t];i.set(e[t]-Vt(n,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!d(t)||!n||!this.constraints)return;this.stopAnimation();const r={x:0,y:0};$r(e=>{const t=this.getAxisMotionValue(e);if(t){const n=t.get();r[e]=function(e,t){let n=.5;const r=Mr(e),i=Mr(t);return i>r?n=sn(t.min,t.max-r,e.min):r>i&&(n=sn(e.min,e.max-i,t.min)),U(0,1,n)}({min:n,max:n},this.constraints[e])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),$r(t=>{if(!di(t,e,null))return;const n=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];n.set(Vt(i,o,r[t]))})}addListeners(){if(!this.visualElement.current)return;ui.set(this.visualElement,this);const e=Be(this.visualElement.current,"pointerdown",e=>{const{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{const{dragConstraints:e}=this.getProps();d(e)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),t();const i=_e(window,"resize",()=>this.scalePositionWithinConstraints()),o=n.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n}=e;this.isDragging&&n&&($r(e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),o&&o()}}getProps(){const e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:o=Vr,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function di(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}const fi=e=>(t,n)=>{e&&Re.Gt.update(()=>e(t,n))};const hi={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function pi(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const mi={correct:(e,t)=>{if(!t.target)return e;if("string"===typeof e){if(!ee.test(e))return e;e=parseFloat(e)}return`${pi(e,t.target.x)}% ${pi(e,t.target.y)}%`}},vi={correct:(e,t)=>{let{treeScale:n,projectionDelta:r}=t;const i=e,o=en.parse(e);if(o.length>5)return i;const a=en.createTransformer(e),s="number"!==typeof o[0]?1:0,l=r.x.scale*n.x,u=r.y.scale*n.y;o[0+s]/=l,o[1+s]/=u;const c=Vt(l,u,.5);return"number"===typeof o[2+s]&&(o[2+s]/=c),"number"===typeof o[3+s]&&(o[3+s]/=c),a(o)}};class yi extends r.Component{componentDidMount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;var o;o=bi,Object.assign(M,o),i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),hi.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,o=n.projection;return o?(o.isPresent=i,r||e.layoutDependency!==t||void 0===t?o.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?o.promote():o.relegate()||Re.Gt.postRender(()=>{const e=o.getStack();e&&e.members.length||this.safeToRemove()})),null):null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),queueMicrotask(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function gi(e){const[t,n]=function(){const e=(0,r.useContext)(a.t);if(null===e)return[!0,null];const{isPresent:t,onExitComplete:n,register:i}=e,o=(0,r.useId)();return(0,r.useEffect)(()=>i(o),[]),!t&&n?[!1,()=>n&&n(o)]:[!0]}(),i=(0,r.useContext)(k.L);return r.createElement(yi,{...e,layoutGroup:i,switchLayoutGroup:(0,r.useContext)(P),isPresent:t,safeToRemove:n})}const bi={borderRadius:{...mi,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:mi,borderTopRightRadius:mi,borderBottomLeftRadius:mi,borderBottomRightRadius:mi,boxShadow:vi},xi=["TopLeft","TopRight","BottomLeft","BottomRight"],wi=xi.length,Si=e=>"string"===typeof e?parseFloat(e):e,ki=e=>"number"===typeof e||ee.test(e);function Pi(e,t){return void 0!==e[t]?e[t]:e.borderRadius}const Ci=Ti(0,.5,Et),Ei=Ti(.5,.95,Ze.l);function Ti(e,t,n){return r=>r<e?0:r>t?1:n(sn(e,t,r))}function Ai(e,t){e.min=t.min,e.max=t.max}function ji(e,t){Ai(e.x,t.x),Ai(e.y,t.y)}function Oi(e,t,n,r,i){return e=Xr(e-=t,1/n,r),void 0!==i&&(e=Xr(e,1/i,r)),e}function Mi(e,t,n,r,i){let[o,a,s]=n;!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:.5,i=arguments.length>4?arguments[4]:void 0,o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:e,a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:e;J.test(t)&&(t=parseFloat(t),t=Vt(a.min,a.max,t/100)-a.min);if("number"!==typeof t)return;let s=Vt(o.min,o.max,r);e===o&&(s-=t),e.min=Oi(e.min,t,n,s,i),e.max=Oi(e.max,t,n,s,i)}(e,t[o],t[a],t[s],t.scale,r,i)}const Li=["x","scaleX","originX"],Ni=["y","scaleY","originY"];function Di(e,t,n,r){Mi(e.x,t,Li,n?n.x:void 0,r?r.x:void 0),Mi(e.y,t,Ni,n?n.y:void 0,r?r.y:void 0)}function Ri(e){return 0===e.translate&&1===e.scale}function zi(e){return Ri(e.x)&&Ri(e.y)}function Fi(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function _i(e){return Mr(e.x)/Mr(e.y)}class Ii{constructor(){this.members=[]}add(e){Yn(this.members,e),e.scheduleRender()}remove(e){if(Xn(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){const t=this.members.findIndex(t=>e===t);if(0===t)return!1;let n;for(let r=t;r>=0;r--){const e=this.members[r];if(!1!==e.isPresent){n=e;break}}return!!n&&(this.promote(n),!0)}promote(e,t){const n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Vi(e,t,n){let r="";const i=e.x.translate/t.x,o=e.y.translate/t.y;if((i||o)&&(r=`translate3d(${i}px, ${o}px, 0) `),1===t.x&&1===t.y||(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:e,rotateX:t,rotateY:i}=n;e&&(r+=`rotate(${e}deg) `),t&&(r+=`rotateX(${t}deg) `),i&&(r+=`rotateY(${i}deg) `)}const a=e.x.scale*t.x,s=e.y.scale*t.y;return 1===a&&1===s||(r+=`scale(${a}, ${s})`),r||"none"}const Bi=(e,t)=>e.depth-t.depth;class Ui{constructor(){this.children=[],this.isDirty=!1}add(e){Yn(this.children,e),this.isDirty=!0}remove(e){Xn(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Bi),this.isDirty=!1,this.children.forEach(e)}}const Hi=["","X","Y","Z"],$i={visibility:"hidden"};let qi=0;const Wi={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function Qi(e){let{attachResizeListener:t,defaultParent:n,measureScroll:r,checkIsScrollRoot:i,resetTransform:o}=e;return class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===n||void 0===n?void 0:n();this.id=qi++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{var e;this.projectionUpdateScheduled=!1,Wi.totalNodes=Wi.resolvedTargetDeltas=Wi.recalculatedProjection=0,this.nodes.forEach(Yi),this.nodes.forEach(ro),this.nodes.forEach(io),this.nodes.forEach(Xi),e=Wi,window.MotionDebug&&window.MotionDebug.record(e)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=t?t.root||t:this,this.path=t?[...t.path,t]:[],this.parent=t,this.depth=t?t.depth+1:0;for(let n=0;n<this.path.length;n++)this.path[n].shouldResetTransform=!0;this.root===this&&(this.nodes=new Ui)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new Zn),this.eventHandlers.get(e).add(t)}notifyListeners(e){const t=this.eventHandlers.get(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];t&&t.notify(...r)}hasListeners(e){return this.eventHandlers.has(e)}mount(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.root.hasTreeAnimated;if(this.instance)return;var r;this.isSVG=(r=e)instanceof SVGElement&&"svg"!==r.tagName,this.instance=e;const{layoutId:i,layout:o,visualElement:a}=this.options;if(a&&!a.current&&a.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(o||i)&&(this.isLayoutDirty=!0),t){let n;const r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){const n=performance.now(),r=i=>{let{timestamp:o}=i;const a=o-n;a>=t&&((0,Re.WG)(r),e(a-t))};return Re.Gt.read(r,!0),()=>(0,Re.WG)(r)}(r,250),hi.hasAnimatedSinceResize&&(hi.hasAnimatedSinceResize=!1,this.nodes.forEach(no))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&a&&(i||o)&&this.addEventListener("didUpdate",e=>{let{delta:t,hasLayoutChanged:n,hasRelativeTargetChanged:r,layout:i}=e;if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const o=this.options.transition||a.getDefaultTransition()||co,{onLayoutAnimationStart:s,onLayoutAnimationComplete:l}=a.getProps(),u=!this.targetLayout||!Fi(this.targetLayout,i)||r,c=!n&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||c||n&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,c);const e={...qn(o,"layout"),onPlay:s,onComplete:l};(a.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else n||no(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,Re.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(oo),this.animationId++)}getTransformTemplate(){const{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let i=0;i<this.path.length;i++){const e=this.path[i];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}const{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;const r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(Ji);this.isUpdating||this.nodes.forEach(eo),this.isUpdating=!1,this.nodes.forEach(to),this.nodes.forEach(Ki),this.nodes.forEach(Gi),this.clearAllSnapshots();const e=performance.now();Re.uv.delta=U(0,1e3/60,e-Re.uv.timestamp),Re.uv.timestamp=e,Re.uv.isProcessing=!0,Re.Ci.update.process(Re.uv),Re.Ci.preRender.process(Re.uv),Re.Ci.render.process(Re.uv),Re.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(Zi),this.sharedNodes.forEach(ao)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,Re.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){Re.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),(!this.options.alwaysMeasureLayout||!this.isLead())&&!this.isLayoutDirty)return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const e=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"measure",t=Boolean(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&(this.scroll={animationId:this.root.animationId,phase:e,isRoot:i(this.instance),offset:r(this.instance)})}resetTransform(){if(!o)return;const e=this.isLayoutDirty||this.shouldResetTransform,t=this.projectionDelta&&!zi(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,i=r!==this.prevTransformTemplateValue;e&&(t||Kr(this.latestValues)||i)&&(o(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const t=this.measurePageBox();let n=this.removeElementScroll(t);var r;return e&&(n=this.removeTransform(n)),po((r=n).x),po(r.y),{animationId:this.root.animationId,measuredBox:t,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const t=e.measureViewportBox(),{scroll:n}=this.root;return n&&(ni(t.x,n.offset.x),ni(t.y,n.offset.y)),t}removeElementScroll(e){const t={x:{min:0,max:0},y:{min:0,max:0}};ji(t,e);for(let n=0;n<this.path.length;n++){const r=this.path[n],{scroll:i,options:o}=r;if(r!==this.root&&i&&o.layoutScroll){if(i.isRoot){ji(t,e);const{scroll:n}=this.root;n&&(ni(t.x,-n.offset.x),ni(t.y,-n.offset.y))}ni(t.x,i.offset.x),ni(t.y,i.offset.y)}}return t}applyTransform(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n={x:{min:0,max:0},y:{min:0,max:0}};ji(n,e);for(let r=0;r<this.path.length;r++){const e=this.path[r];!t&&e.options.layoutScroll&&e.scroll&&e!==e.root&&ai(n,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),Kr(e.latestValues)&&ai(n,e.latestValues)}return Kr(this.latestValues)&&ai(n,this.latestValues),n}removeTransform(e){const t={x:{min:0,max:0},y:{min:0,max:0}};ji(t,e);for(let n=0;n<this.path.length;n++){const e=this.path[n];if(!e.instance)continue;if(!Kr(e.latestValues))continue;Qr(e.latestValues)&&e.updateSnapshot();const r=Hr();ji(r,e.measurePageBox()),Di(t,e.latestValues,e.snapshot?e.snapshot.layoutBox:void 0,r)}return Kr(this.latestValues)&&Di(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Re.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];var t;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const r=Boolean(this.resumingFrom)||this!==n;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;const{layout:i,layoutId:o}=this.options;if(this.layout&&(i||o)){if(this.resolvedRelativeTargetAt=Re.uv.timestamp,!this.targetDelta&&!this.relativeTarget){const e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Fr(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),ji(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var a,s,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),a=this.target,s=this.relativeTarget,l=this.relativeParent.target,Rr(a.x,s.x,l.x),Rr(a.y,s.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):ji(this.target,this.layout.layoutBox),ei(this.target,this.targetDelta)):ji(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const e=this.getClosestProjectingParent();e&&Boolean(e.resumingFrom)===Boolean(this.resumingFrom)&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Fr(this.relativeTargetOrigin,this.target,e.target),ji(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Wi.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!Qr(this.parent.latestValues)&&!Gr(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;const t=this.getLead(),n=Boolean(this.resumingFrom)||this!==t;let r=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===Re.uv.timestamp&&(r=!1),r)return;const{layout:i,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!i&&!o)return;ji(this.layoutCorrected,this.layout.layoutBox);const a=this.treeScale.x,s=this.treeScale.y;!function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const i=n.length;if(!i)return;let o,a;t.x=t.y=1;for(let s=0;s<i;s++){o=n[s],a=o.projectionDelta;const i=o.instance;i&&i.style&&"contents"===i.style.display||(r&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ai(e,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,ei(e,a)),r&&Kr(o.latestValues)&&ai(e,o.latestValues))}t.x=ti(t.x),t.y=ti(t.y)}(this.layoutCorrected,this.treeScale,this.path,n),!t.layout||t.target||1===this.treeScale.x&&1===this.treeScale.y||(t.target=t.layout.layoutBox);const{target:l}=t;if(!l)return void(this.projectionTransform&&(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionTransform="none",this.scheduleRender()));this.projectionDelta||(this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}});const u=this.projectionTransform;Dr(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=Vi(this.projectionDelta,this.treeScale),this.projectionTransform===u&&this.treeScale.x===a&&this.treeScale.y===s||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),Wi.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.options.scheduleRender&&this.options.scheduleRender(),e){const e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=this.snapshot,r=n?n.latestValues:{},i={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;const a={x:{min:0,max:0},y:{min:0,max:0}},s=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(s&&!u&&!0===this.options.crossfade&&!this.path.some(uo));let d;this.animationProgress=0,this.mixTargetDelta=t=>{const n=t/1e3;var l,f,h,p,m,v;so(o.x,e.x,n),so(o.y,e.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Fr(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,p=this.relativeTargetOrigin,m=a,v=n,lo(h.x,p.x,m.x,v),lo(h.y,p.y,m.y,v),d&&(l=this.relativeTarget,f=d,l.x.min===f.x.min&&l.x.max===f.x.max&&l.y.min===f.y.min&&l.y.max===f.y.max)&&(this.isProjectionDirty=!1),d||(d={x:{min:0,max:0},y:{min:0,max:0}}),ji(d,this.relativeTarget)),s&&(this.animationValues=i,function(e,t,n,r,i,o){i?(e.opacity=Vt(0,void 0!==n.opacity?n.opacity:1,Ci(r)),e.opacityExit=Vt(void 0!==t.opacity?t.opacity:1,0,Ei(r))):o&&(e.opacity=Vt(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,r));for(let a=0;a<wi;a++){const i=`border${xi[a]}Radius`;let o=Pi(t,i),s=Pi(n,i);void 0===o&&void 0===s||(o||(o=0),s||(s=0),0===o||0===s||ki(o)===ki(s)?(e[i]=Math.max(Vt(Si(o),Si(s),r),0),(J.test(s)||J.test(o))&&(e[i]+="%")):e[i]=s)}(t.rotate||n.rotate)&&(e.rotate=Vt(t.rotate||0,n.rotate||0,r))}(i,r,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,Re.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=Re.Gt.update(()=>{hi.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,n){const r=R(e)?e:tr(e);return r.start(Qn("",r,t,n)),r.animation}(0,1e3,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const e=this.getLead();let{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&mo(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const t=Mr(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;const r=Mr(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}ji(t,n),ai(t,i),Dr(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new Ii);this.sharedNodes.get(e).add(t);const n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){const e=this.getStack();return!e||e.lead===this}getLead(){var e;const{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;const{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){const{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote(){let{needsReset:e,transition:t,preserveFollowOpacity:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){const e=this.getStack();return!!e&&e.relegate(this)}resetRotation(){const{visualElement:e}=this.options;if(!e)return;let t=!1;const{latestValues:n}=e;if((n.rotate||n.rotateX||n.rotateY||n.rotateZ)&&(t=!0),!t)return;const r={};for(let i=0;i<Hi.length;i++){const t="rotate"+Hi[i];n[t]&&(r[t]=n[t],e.setStaticValue(t,0))}e.render();for(const i in r)e.setStaticValue(i,r[i]);e.scheduleRender()}getProjectionStyles(e){var t,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return $i;const r={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=Le(null===e||void 0===e?void 0:e.pointerEvents)||"",r.transform=i?i(this.latestValues,""):"none",r;const o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){const t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=Le(null===e||void 0===e?void 0:e.pointerEvents)||""),this.hasProjected&&!Kr(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}const a=o.animationValues||o.latestValues;this.applyTransformsToTarget(),r.transform=Vi(this.projectionDeltaWithTransform,this.treeScale,a),i&&(r.transform=i(a,r.transform));const{x:s,y:l}=this.projectionDelta;r.transformOrigin=`${100*s.origin}% ${100*l.origin}% 0`,o.animationValues?r.opacity=o===this?null!==(n=null!==(t=a.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:a.opacityExit:r.opacity=o===this?void 0!==a.opacity?a.opacity:"":void 0!==a.opacityExit?a.opacityExit:0;for(const u in M){if(void 0===a[u])continue;const{correct:e,applyTo:t}=M[u],n="none"===r.transform?a[u]:e(a[u],o);if(t){const e=t.length;for(let i=0;i<e;i++)r[t[i]]=n}else r[u]=n}return this.options.layoutId&&(r.pointerEvents=o===this?Le(null===e||void 0===e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(Ji),this.root.sharedNodes.clear()}}}function Ki(e){e.updateLayout()}function Gi(e){var t;const n=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:t,measuredBox:r}=e.layout,{animationType:i}=e.options,o=n.source!==e.layout.source;"size"===i?$r(e=>{const r=o?n.measuredBox[e]:n.layoutBox[e],i=Mr(r);r.min=t[e].min,r.max=r.min+i}):mo(i,n.layoutBox,t)&&$r(r=>{const i=o?n.measuredBox[r]:n.layoutBox[r],a=Mr(t[r]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+a)});const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Dr(a,t,n.layoutBox);const s={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Dr(s,e.applyTransform(r,!0),n.measuredBox):Dr(s,t,n.layoutBox);const l=!zi(a);let u=!1;if(!e.resumeFrom){const r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){const{snapshot:i,layout:o}=r;if(i&&o){const a={x:{min:0,max:0},y:{min:0,max:0}};Fr(a,n.layoutBox,i.layoutBox);const s={x:{min:0,max:0},y:{min:0,max:0}};Fr(s,t,o.layoutBox),Fi(a,s)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){const{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function Yi(e){Wi.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=Boolean(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function Xi(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function Zi(e){e.clearSnapshot()}function Ji(e){e.clearMeasurements()}function eo(e){e.isLayoutDirty=!1}function to(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function no(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ro(e){e.resolveTargetDelta()}function io(e){e.calcProjection()}function oo(e){e.resetRotation()}function ao(e){e.removeLeadSnapshot()}function so(e,t,n){e.translate=Vt(t.translate,0,n),e.scale=Vt(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function lo(e,t,n,r){e.min=Vt(t.min,n.min,r),e.max=Vt(t.max,n.max,r)}function uo(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}const co={duration:.45,ease:[.4,0,.1,1]},fo=e=>"undefined"!==typeof navigator&&navigator.userAgent.toLowerCase().includes(e),ho=fo("applewebkit/")&&!fo("chrome/")?Math.round:Ze.l;function po(e){e.min=ho(e.min),e.max=ho(e.max)}function mo(e,t,n){return"position"===e||"preserve-aspect"===e&&!Lr(_i(t),_i(n),.2)}const vo=Qi({attachResizeListener:(e,t)=>_e(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),yo={current:void 0},go=Qi({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!yo.current){const e=new vo({});e.mount(window),e.setOptions({layoutScroll:!0}),yo.current=e}return yo.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>Boolean("fixed"===window.getComputedStyle(e).position)}),bo={pan:{Feature:class extends Ge{constructor(){super(...arguments),this.removePointerDownListener=Ze.l}onPointerDown(e){this.session=new Pr(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:li(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:fi(e),onStart:fi(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&Re.Gt.update(()=>r(e,t))}}}mount(){this.removePointerDownListener=Be(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends Ge{constructor(e){super(e),this.removeGroupControls=Ze.l,this.removeListeners=Ze.l,this.controls=new ci(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Ze.l}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:go,MeasureLayout:gi}},xo=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function wo(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;(0,ut.V)(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);const[r,i]=function(e){const t=xo.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}(e);if(!r)return;const o=window.getComputedStyle(t).getPropertyValue(r);if(o){const e=o.trim();return Gn(e)?parseFloat(e):e}return V(i)?wo(i,t,n+1):i}const So=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),ko=e=>So.has(e),Po=e=>e===H||e===ee,Co=(e,t)=>parseFloat(e.split(", ")[t]),Eo=(e,t)=>(n,r)=>{let{transform:i}=r;if("none"===i||!i)return 0;const o=i.match(/^matrix3d\((.+)\)$/);if(o)return Co(o[1],t);{const t=i.match(/^matrix\((.+)\)$/);return t?Co(t[1],e):0}},To=new Set(["x","y","z"]),Ao=L.filter(e=>!To.has(e));const jo={width:(e,t)=>{let{x:n}=e,{paddingLeft:r="0",paddingRight:i="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(i)},height:(e,t)=>{let{y:n}=e,{paddingTop:r="0",paddingBottom:i="0"}=t;return n.max-n.min-parseFloat(r)-parseFloat(i)},top:(e,t)=>{let{top:n}=t;return parseFloat(n)},left:(e,t)=>{let{left:n}=t;return parseFloat(n)},bottom:(e,t)=>{let{y:n}=e,{top:r}=t;return parseFloat(r)+(n.max-n.min)},right:(e,t)=>{let{x:n}=e,{left:r}=t;return parseFloat(r)+(n.max-n.min)},x:Eo(4,13),y:Eo(5,14)};jo.translateX=jo.x,jo.translateY=jo.y;const Oo=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};t={...t},r={...r};const i=Object.keys(t).filter(ko);let o=[],a=!1;const s=[];if(i.forEach(i=>{const l=e.getValue(i);if(!e.hasValue(i))return;let u=n[i],c=ir(u);const d=t[i];let f;if(Oe(d)){const e=d.length,t=null===d[0]?1:0;u=d[t],c=ir(u);for(let n=t;n<e&&null!==d[n];n++)f?(0,ut.V)(ir(d[n])===f,"All keyframes must be of the same type"):(f=ir(d[n]),(0,ut.V)(f===c||Po(c)&&Po(f),"Keyframes must be of the same dimension as the current value"))}else f=ir(d);if(c!==f)if(Po(c)&&Po(f)){const e=l.get();"string"===typeof e&&l.set(parseFloat(e)),"string"===typeof d?t[i]=parseFloat(d):Array.isArray(d)&&f===ee&&(t[i]=d.map(parseFloat))}else(null===c||void 0===c?void 0:c.transform)&&(null===f||void 0===f?void 0:f.transform)&&(0===u||0===d)?0===u?l.set(f.transform(u)):t[i]=c.transform(d):(a||(o=function(e){const t=[];return Ao.forEach(n=>{const r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}(e),a=!0),s.push(i),r[i]=void 0!==r[i]?r[i]:t[i],l.jump(d))}),s.length){const n=s.indexOf("height")>=0?window.pageYOffset:null,i=((e,t,n)=>{const r=t.measureViewportBox(),i=t.current,o=getComputedStyle(i),{display:a}=o,s={};"none"===a&&t.setStaticValue("display",e.display||"block"),n.forEach(e=>{s[e]=jo[e](r,o)}),t.render();const l=t.measureViewportBox();return n.forEach(n=>{const r=t.getValue(n);r&&r.jump(s[n]),e[n]=jo[n](l,o)}),e})(t,e,s);return o.length&&o.forEach(t=>{let[n,r]=t;e.getValue(n).set(r)}),e.render(),S.B&&null!==n&&window.scrollTo({top:n}),{target:i,transitionEnd:r}}return{target:t,transitionEnd:r}};function Mo(e,t,n,r){return(e=>Object.keys(e).some(ko))(t)?Oo(e,t,n,r):{target:t,transitionEnd:r}}const Lo=(e,t,n,r)=>{const i=function(e,t,n){let{...r}=t;const i=e.current;if(!(i instanceof Element))return{target:r,transitionEnd:n};n&&(n={...n}),e.values.forEach(e=>{const t=e.get();if(!V(t))return;const n=wo(t,i);n&&e.set(n)});for(const o in r){const e=r[o];if(!V(e))continue;const t=wo(e,i);t&&(r[o]=t,n||(n={}),void 0===n[o]&&(n[o]=e))}return{target:r,transitionEnd:n}}(e,t,r);return Mo(e,t=i.target,n,r=i.transitionEnd)},No={current:null},Do={current:!1};const Ro=new WeakMap,zo=Object.keys(w),Fo=zo.length,_o=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Io=m.length;class Vo{constructor(e){let{parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:o}=e,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>Re.Gt.render(this.render,!1,!0);const{latestValues:s,renderState:l}=o;this.latestValues=s,this.baseTarget={...s},this.initialValues=n.initial?{...s}:{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.isControllingVariants=v(n),this.isVariantNode=y(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const d in c){const e=c[d];void 0!==s[d]&&R(e)&&(e.set(s[d],!1),Kn(u)&&u.add(d))}}scrapeMotionValuesFromProps(e,t){return{}}mount(e){this.current=e,Ro.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),Do.current||function(){if(Do.current=!0,S.B)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>No.current=e.matches;e.addListener(t),t()}else No.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||No.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Ro.delete(this.current),this.projection&&this.projection.unmount(),(0,Re.WG)(this.notifyUpdate),(0,Re.WG)(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,t){const n=N.has(e),r=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&Re.Gt.update(this.notifyUpdate,!1,!0),n&&this.projection&&(this.projection.isTransformDirty=!0)}),i=t.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{r(),i()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}loadFeatures(e,t,n,r){let i,o,{children:a,...s}=e;for(let l=0;l<Fo;l++){const e=zo[l],{isEnabled:t,Feature:n,ProjectionNode:r,MeasureLayout:a}=w[e];r&&(i=r),t(s)&&(!this.features[e]&&n&&(this.features[e]=new n(this)),a&&(o=a))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&i){this.projection=new i(this.latestValues,this.parent&&this.parent.projection);const{layoutId:e,layout:t,drag:n,dragConstraints:o,layoutScroll:a,layoutRoot:l}=s;this.projection.setOptions({layoutId:e,layout:t,alwaysMeasureLayout:Boolean(n)||o&&d(o),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"===typeof t?t:"both",initialPromotionConfig:r,layoutScroll:a,layoutRoot:l})}return o}updateFeatures(){for(const e in this.features){const t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}makeTargetAnimatable(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return this.makeTargetAnimatableFromInstance(e,this.props,t)}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let n=0;n<_o.length;n++){const t=_o[n];this.propEventSubscriptions[t]&&(this.propEventSubscriptions[t](),delete this.propEventSubscriptions[t]);const r=e["on"+t];r&&(this.propEventSubscriptions[t]=this.on(t,r))}this.prevMotionValues=function(e,t,n){const{willChange:r}=t;for(const i in t){const o=t[i],a=n[i];if(R(o))e.addValue(i,o),Kn(r)&&r.add(i);else if(R(a))e.addValue(i,tr(o,{owner:e})),Kn(r)&&r.remove(i);else if(a!==o)if(e.hasValue(i)){const t=e.getValue(i);!t.hasAnimated&&t.set(o)}else{const t=e.getStaticValue(i);e.addValue(i,tr(void 0!==t?t:o,{owner:e}))}}for(const i in n)void 0===t[i]&&e.removeValue(i);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(){if(arguments.length>0&&void 0!==arguments[0]&&arguments[0])return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const e=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(e.initial=this.props.initial),e}const e={};for(let t=0;t<Io;t++){const n=m[t],r=this.props[n];(f(r)||!1===r)&&(e[n]=r)}return e}addVariantChild(e){const t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){t!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,t)),this.values.set(e,t),this.latestValues[e]=t.get()}removeValue(e){this.values.delete(e);const t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=tr(t,{owner:this}),this.addValue(e,n)),n}readValue(e){var t;return void 0===this.latestValues[e]&&this.current?null!==(t=this.getBaseTargetFromProps(this.props,e))&&void 0!==t?t:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e]}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;const{initial:n}=this.props,r="string"===typeof n||"object"===typeof n?null===(t=Ae(this.props,n))||void 0===t?void 0:t[e]:void 0;if(n&&void 0!==r)return r;const i=this.getBaseTargetFromProps(this.props,e);return void 0===i||R(i)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new Zn),this.events[e].add(t)}notify(e){if(this.events[e]){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];this.events[e].notify(...n)}}}class Bo extends Vo{sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,t){let{vars:n,style:r}=t;delete n[e],delete r[e]}makeTargetAnimatableFromInstance(e,t,n){let{transition:r,transitionEnd:i,...o}=e,{transformValues:a}=t,s=function(e,t,n){const r={};for(const i in e){const e=ur(i,t);if(void 0!==e)r[i]=e;else{const e=n.getValue(i);e&&(r[i]=e.get())}}return r}(o,r||{},this);if(a&&(i&&(i=a(i)),o&&(o=a(o)),s&&(s=a(s))),n){!function(e,t,n){var r,i;const o=Object.keys(t).filter(t=>!e.hasValue(t)),a=o.length;if(a)for(let s=0;s<a;s++){const a=o[s],l=t[a];let u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(i=null!==(r=n[a])&&void 0!==r?r:e.readValue(a))&&void 0!==i?i:t[a]),void 0!==u&&null!==u&&("string"===typeof u&&(Gn(u)||Hn(u))?u=parseFloat(u):!ar(u)&&en.test(l)&&(u=Un(a,l)),e.addValue(a,tr(u,{owner:e})),void 0===n[a]&&(n[a]=u),null!==u&&e.setBaseTarget(a,u))}}(this,o,s);const e=Lo(this,o,s,i);i=e.transitionEnd,o=e.target}return{transition:r,transitionEnd:i,...o}}}class Uo extends Bo{constructor(){super(...arguments),this.type="html"}readValueFromInstance(e,t){if(N.has(t)){const e=Bn(t);return e&&e.default||0}{const r=(n=e,window.getComputedStyle(n)),i=(I(t)?r.getPropertyValue(t):r[t])||0;return"string"===typeof i?i.trim():i}var n}measureInstanceViewportBox(e,t){let{transformPagePoint:n}=t;return si(e,n)}build(e,t,n,r){ae(e,t,n,r.transformTemplate)}scrapeMotionValuesFromProps(e,t){return Ee(e,t)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;R(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}renderInstance(e,t,n,r){ke(e,t,n,r)}}class Ho extends Bo{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(N.has(t)){const e=Bn(t);return e&&e.default||0}return t=Pe.has(t)?t:u(t),e.getAttribute(t)}measureInstanceViewportBox(){return{x:{min:0,max:0},y:{min:0,max:0}}}scrapeMotionValuesFromProps(e,t){return Te(e,t)}build(e,t,n,r){ge(e,t,n,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,n,r){Ce(e,t,0,r)}mount(e){this.isSVGTag=xe(e.tagName),super.mount(e)}}const $o=(e,t)=>O(e)?new Ho(t,{enableHardwareAcceleration:!1}):new Uo(t,{enableHardwareAcceleration:!0}),qo={...Sr,...at,...bo,...{layout:{ProjectionNode:go,MeasureLayout:gi}}},Wo=A((e,t)=>function(e,t,n,r){let{forwardMotionProps:i=!1}=t;return{...O(e)?ze:Fe,preloadedFeatures:n,useRender:Se(i),createVisualElement:r,Component:e}}(e,t,qo,$o))},1497:(e,t,n)=>{"use strict";var r=n(3218);function i(){}function o(){}o.resetWarningCache=i,e.exports=function(){function e(e,t,n,i,o,a){if(a!==r){var s=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:o,resetWarningCache:i};return n.PropTypes=n,n}},1892:(e,t,n)=>{"use strict";n.d(t,{l:()=>r});const r=e=>e},1991:(e,t,n)=>{"use strict";n.d(t,{j:()=>i});var r=n(8870),i=new(function(){function e(){this.queue=[],this.transactions=0,this.notifyFn=function(e){e()},this.batchNotifyFn=function(e){e()}}var t=e.prototype;return t.batch=function(e){var t;this.transactions++;try{t=e()}finally{this.transactions--,this.transactions||this.flush()}return t},t.schedule=function(e){var t=this;this.transactions?this.queue.push(e):(0,r.G6)(function(){t.notifyFn(e)})},t.batchCalls=function(e){var t=this;return function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];t.schedule(function(){e.apply(void 0,r)})}},t.flush=function(){var e=this,t=this.queue;this.queue=[],t.length&&(0,r.G6)(function(){e.batchNotifyFn(function(){t.forEach(function(t){e.notifyFn(t)})})})},t.setNotifyFunction=function(e){this.notifyFn=e},t.setBatchNotifyFunction=function(e){this.batchNotifyFn=e},e}())},2190:(e,t,n)=>{"use strict";n.d(t,{L:()=>r});const r=(0,n(5043).createContext)({})},2330:(e,t,n)=>{"use strict";var r=n(5043);var i="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},o=r.useState,a=r.useEffect,s=r.useLayoutEffect,l=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!i(e,n)}catch(r){return!0}}var c="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),i=r[0].inst,c=r[1];return s(function(){i.value=n,i.getSnapshot=t,u(i)&&c({inst:i})},[e,n,t]),a(function(){return u(i)&&c({inst:i}),e(function(){u(i)&&c({inst:i})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},2730:(e,t,n)=>{"use strict";var r=n(5043),i=n(8853);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var a=new Set,s={};function l(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(s[e]=t,e=0;e<t.length;e++)a.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,h={},p={};function m(e,t,n,r,i,o,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=a}var v={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){v[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];v[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){v[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){v[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){v[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){v[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){v[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){v[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){v[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var y=/[\-:]([a-z])/g;function g(e){return e[1].toUpperCase()}function b(e,t,n,r){var i=v.hasOwnProperty(t)?v[t]:null;(null!==i?0!==i.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,i,r)&&(n=null),r||null===i?function(e){return!!d.call(p,e)||!d.call(h,e)&&(f.test(e)?p[e]=!0:(h[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=null===n?3!==i.type&&"":n:(t=i.attributeName,r=i.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(i=i.type)||4===i&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(y,g);v[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(y,g);v[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(y,g);v[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),v.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){v[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),S=Symbol.for("react.portal"),k=Symbol.for("react.fragment"),P=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),E=Symbol.for("react.provider"),T=Symbol.for("react.context"),A=Symbol.for("react.forward_ref"),j=Symbol.for("react.suspense"),O=Symbol.for("react.suspense_list"),M=Symbol.for("react.memo"),L=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var N=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var D=Symbol.iterator;function R(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=D&&e[D]||e["@@iterator"])?e:null}var z,F=Object.assign;function _(e){if(void 0===z)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);z=t&&t[1]||""}return"\n"+z+e}var I=!1;function V(e,t){if(!e||I)return"";I=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var i=u.stack.split("\n"),o=r.stack.split("\n"),a=i.length-1,s=o.length-1;1<=a&&0<=s&&i[a]!==o[s];)s--;for(;1<=a&&0<=s;a--,s--)if(i[a]!==o[s]){if(1!==a||1!==s)do{if(a--,0>--s||i[a]!==o[s]){var l="\n"+i[a].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=a&&0<=s);break}}}finally{I=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?_(e):""}function B(e){switch(e.tag){case 5:return _(e.type);case 16:return _("Lazy");case 13:return _("Suspense");case 19:return _("SuspenseList");case 0:case 2:case 15:return e=V(e.type,!1);case 11:return e=V(e.type.render,!1);case 1:return e=V(e.type,!0);default:return""}}function U(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case k:return"Fragment";case S:return"Portal";case C:return"Profiler";case P:return"StrictMode";case j:return"Suspense";case O:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case T:return(e.displayName||"Context")+".Consumer";case E:return(e._context.displayName||"Context")+".Provider";case A:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case M:return null!==(t=e.displayName||null)?t:U(e.type)||"Memo";case L:t=e._payload,e=e._init;try{return U(e(t))}catch(n){}}return null}function H(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return U(t);case 8:return t===P?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function $(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function q(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function W(e){e._valueTracker||(e._valueTracker=function(e){var t=q(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var i=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=q(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function G(e,t){var n=t.checked;return F({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Y(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=$(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function Z(e,t){X(e,t);var n=$(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,$(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function J(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+$(n),t=null,i=0;i<e.length;i++){if(e[i].value===n)return e[i].selected=!0,void(r&&(e[i].defaultSelected=!0));null!==t||e[i].disabled||(t=e[i])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return F({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ie(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:$(n)}}function oe(e,t){var n=$(t.value),r=$(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ae(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ce(e,t)})}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var he={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},pe=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||he.hasOwnProperty(e)&&he[e]?(""+t).trim():t+"px"}function ve(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),i=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}Object.keys(he).forEach(function(e){pe.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),he[t]=he[e]})});var ye=F({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ge(e,t){if(t){if(ye[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,ke=null,Pe=null;function Ce(e){if(e=bi(e)){if("function"!==typeof Se)throw Error(o(280));var t=e.stateNode;t&&(t=wi(t),Se(e.stateNode,e.type,t))}}function Ee(e){ke?Pe?Pe.push(e):Pe=[e]:ke=e}function Te(){if(ke){var e=ke,t=Pe;if(Pe=ke=null,Ce(e),t)for(e=0;e<t.length;e++)Ce(t[e])}}function Ae(e,t){return e(t)}function je(){}var Oe=!1;function Me(e,t,n){if(Oe)return e(t,n);Oe=!0;try{return Ae(e,t,n)}finally{Oe=!1,(null!==ke||null!==Pe)&&(je(),Te())}}function Le(e,t){var n=e.stateNode;if(null===n)return null;var r=wi(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var Ne=!1;if(c)try{var De={};Object.defineProperty(De,"passive",{get:function(){Ne=!0}}),window.addEventListener("test",De,De),window.removeEventListener("test",De,De)}catch(ce){Ne=!1}function Re(e,t,n,r,i,o,a,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var ze=!1,Fe=null,_e=!1,Ie=null,Ve={onError:function(e){ze=!0,Fe=e}};function Be(e,t,n,r,i,o,a,s,l){ze=!1,Fe=null,Re.apply(Ve,arguments)}function Ue(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function He(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function $e(e){if(Ue(e)!==e)throw Error(o(188))}function qe(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ue(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(null===i)break;var a=i.alternate;if(null===a){if(null!==(r=i.return)){n=r;continue}break}if(i.child===a.child){for(a=i.child;a;){if(a===n)return $e(i),e;if(a===r)return $e(i),t;a=a.sibling}throw Error(o(188))}if(n.return!==r.return)n=i,r=a;else{for(var s=!1,l=i.child;l;){if(l===n){s=!0,n=i,r=a;break}if(l===r){s=!0,r=i,n=a;break}l=l.sibling}if(!s){for(l=a.child;l;){if(l===n){s=!0,n=a,r=i;break}if(l===r){s=!0,r=a,n=i;break}l=l.sibling}if(!s)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e))?We(e):null}function We(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=We(e);if(null!==t)return t;e=e.sibling}return null}var Qe=i.unstable_scheduleCallback,Ke=i.unstable_cancelCallback,Ge=i.unstable_shouldYield,Ye=i.unstable_requestPaint,Xe=i.unstable_now,Ze=i.unstable_getCurrentPriorityLevel,Je=i.unstable_ImmediatePriority,et=i.unstable_UserBlockingPriority,tt=i.unstable_NormalPriority,nt=i.unstable_LowPriority,rt=i.unstable_IdlePriority,it=null,ot=null;var at=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,i=e.suspendedLanes,o=e.pingedLanes,a=268435455&n;if(0!==a){var s=a&~i;0!==s?r=dt(s):0!==(o&=a)&&(r=dt(o))}else 0!==(a=n&~i)?r=dt(a):0!==o&&(r=dt(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&i)&&((i=r&-r)>=(o=t&-t)||16===i&&0!==(4194240&o)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)i=1<<(n=31-at(t)),r|=e[n],t&=~i;return r}function ht(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function pt(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function vt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-at(t)]=n}function gt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-at(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,St,kt,Pt,Ct,Et=!1,Tt=[],At=null,jt=null,Ot=null,Mt=new Map,Lt=new Map,Nt=[],Dt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Rt(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":jt=null;break;case"mouseover":case"mouseout":Ot=null;break;case"pointerover":case"pointerout":Mt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Lt.delete(t.pointerId)}}function zt(e,t,n,r,i,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[i]},null!==t&&(null!==(t=bi(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==i&&-1===t.indexOf(i)&&t.push(i),e)}function Ft(e){var t=gi(e.target);if(null!==t){var n=Ue(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=He(n)))return e.blockedOn=t,void Ct(e.priority,function(){kt(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function _t(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=bi(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function It(e,t,n){_t(e)&&n.delete(t)}function Vt(){Et=!1,null!==At&&_t(At)&&(At=null),null!==jt&&_t(jt)&&(jt=null),null!==Ot&&_t(Ot)&&(Ot=null),Mt.forEach(It),Lt.forEach(It)}function Bt(e,t){e.blockedOn===t&&(e.blockedOn=null,Et||(Et=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Vt)))}function Ut(e){function t(t){return Bt(t,e)}if(0<Tt.length){Bt(Tt[0],e);for(var n=1;n<Tt.length;n++){var r=Tt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==At&&Bt(At,e),null!==jt&&Bt(jt,e),null!==Ot&&Bt(Ot,e),Mt.forEach(t),Lt.forEach(t),n=0;n<Nt.length;n++)(r=Nt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Nt.length&&null===(n=Nt[0]).blockedOn;)Ft(n),null===n.blockedOn&&Nt.shift()}var Ht=x.ReactCurrentBatchConfig,$t=!0;function qt(e,t,n,r){var i=bt,o=Ht.transition;Ht.transition=null;try{bt=1,Qt(e,t,n,r)}finally{bt=i,Ht.transition=o}}function Wt(e,t,n,r){var i=bt,o=Ht.transition;Ht.transition=null;try{bt=4,Qt(e,t,n,r)}finally{bt=i,Ht.transition=o}}function Qt(e,t,n,r){if($t){var i=Gt(e,t,n,r);if(null===i)$r(e,t,r,Kt,n),Rt(e,r);else if(function(e,t,n,r,i){switch(t){case"focusin":return At=zt(At,e,t,n,r,i),!0;case"dragenter":return jt=zt(jt,e,t,n,r,i),!0;case"mouseover":return Ot=zt(Ot,e,t,n,r,i),!0;case"pointerover":var o=i.pointerId;return Mt.set(o,zt(Mt.get(o)||null,e,t,n,r,i)),!0;case"gotpointercapture":return o=i.pointerId,Lt.set(o,zt(Lt.get(o)||null,e,t,n,r,i)),!0}return!1}(i,e,t,n,r))r.stopPropagation();else if(Rt(e,r),4&t&&-1<Dt.indexOf(e)){for(;null!==i;){var o=bi(i);if(null!==o&&wt(o),null===(o=Gt(e,t,n,r))&&$r(e,t,r,Kt,n),o===i)break;i=o}null!==i&&r.stopPropagation()}else $r(e,t,r,null,n)}}var Kt=null;function Gt(e,t,n,r){if(Kt=null,null!==(e=gi(e=we(r))))if(null===(t=Ue(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=He(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Yt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ze()){case Je:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Xt=null,Zt=null,Jt=null;function en(){if(Jt)return Jt;var e,t,n=Zt,r=n.length,i="value"in Xt?Xt.value:Xt.textContent,o=i.length;for(e=0;e<r&&n[e]===i[e];e++);var a=r-e;for(t=1;t<=a&&n[r-t]===i[o-t];t++);return Jt=i.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function on(e){function t(t,n,r,i,o){for(var a in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=i,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(a)&&(t=e[a],this[a]=t?t(i):i[a]);return this.isDefaultPrevented=(null!=i.defaultPrevented?i.defaultPrevented:!1===i.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return F(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var an,sn,ln,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=on(un),dn=F({},un,{view:0,detail:0}),fn=on(dn),hn=F({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(an=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=an=0,ln=e),an)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),pn=on(hn),mn=on(F({},hn,{dataTransfer:0})),vn=on(F({},dn,{relatedTarget:0})),yn=on(F({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),gn=F({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=on(gn),xn=on(F({},un,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Pn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=kn[e])&&!!t[e]}function Cn(){return Pn}var En=F({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Tn=on(En),An=on(F({},hn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),jn=on(F({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cn})),On=on(F({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),Mn=F({},hn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ln=on(Mn),Nn=[9,13,27,32],Dn=c&&"CompositionEvent"in window,Rn=null;c&&"documentMode"in document&&(Rn=document.documentMode);var zn=c&&"TextEvent"in window&&!Rn,Fn=c&&(!Dn||Rn&&8<Rn&&11>=Rn),_n=String.fromCharCode(32),In=!1;function Vn(e,t){switch(e){case"keyup":return-1!==Nn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Bn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Un=!1;var Hn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function $n(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Hn[e.type]:"textarea"===t}function qn(e,t,n,r){Ee(r),0<(t=Wr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Wn=null,Qn=null;function Kn(e){_r(e,0)}function Gn(e){if(Q(xi(e)))return e}function Yn(e,t){if("change"===e)return t}var Xn=!1;if(c){var Zn;if(c){var Jn="oninput"in document;if(!Jn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Jn="function"===typeof er.oninput}Zn=Jn}else Zn=!1;Xn=Zn&&(!document.documentMode||9<document.documentMode)}function tr(){Wn&&(Wn.detachEvent("onpropertychange",nr),Qn=Wn=null)}function nr(e){if("value"===e.propertyName&&Gn(Qn)){var t=[];qn(t,Qn,e,we(e)),Me(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,(Wn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ir(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Gn(Qn)}function or(e,t){if("click"===e)return Gn(t)}function ar(e,t){if("input"===e||"change"===e)return Gn(t)}var sr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(sr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!d.call(t,i)||!sr(e[i],t[i]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function pr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&hr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var i=n.textContent.length,o=Math.min(r.start,i);r=void 0===r.end?o:Math.min(r.end,i),!e.extend&&o>r&&(i=r,r=o,o=i),i=cr(n,o);var a=cr(n,r);i&&a&&(1!==e.rangeCount||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&((t=t.createRange()).setStart(i.node,i.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,vr=null,yr=null,gr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==vr||vr!==K(r)||("selectionStart"in(r=vr)&&hr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},gr&&lr(gr,r)||(gr=r,0<(r=Wr(yr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=vr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},kr={},Pr={};function Cr(e){if(kr[e])return kr[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Pr)return kr[e]=n[t];return e}c&&(Pr=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Er=Cr("animationend"),Tr=Cr("animationiteration"),Ar=Cr("animationstart"),jr=Cr("transitionend"),Or=new Map,Mr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Lr(e,t){Or.set(e,t),l(t,[e])}for(var Nr=0;Nr<Mr.length;Nr++){var Dr=Mr[Nr];Lr(Dr.toLowerCase(),"on"+(Dr[0].toUpperCase()+Dr.slice(1)))}Lr(Er,"onAnimationEnd"),Lr(Tr,"onAnimationIteration"),Lr(Ar,"onAnimationStart"),Lr("dblclick","onDoubleClick"),Lr("focusin","onFocus"),Lr("focusout","onBlur"),Lr(jr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Rr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Rr));function Fr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,i,a,s,l,u){if(Be.apply(this,arguments),ze){if(!ze)throw Error(o(198));var c=Fe;ze=!1,Fe=null,_e||(_e=!0,Ie=c)}}(r,t,void 0,e),e.currentTarget=null}function _r(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var a=r.length-1;0<=a;a--){var s=r[a],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==o&&i.isPropagationStopped())break e;Fr(i,s,u),o=l}else for(a=0;a<r.length;a++){if(l=(s=r[a]).instance,u=s.currentTarget,s=s.listener,l!==o&&i.isPropagationStopped())break e;Fr(i,s,u),o=l}}}if(_e)throw e=Ie,_e=!1,Ie=null,e}function Ir(e,t){var n=t[mi];void 0===n&&(n=t[mi]=new Set);var r=e+"__bubble";n.has(r)||(Hr(t,e,2,!1),n.add(r))}function Vr(e,t,n){var r=0;t&&(r|=4),Hr(n,e,r,t)}var Br="_reactListening"+Math.random().toString(36).slice(2);function Ur(e){if(!e[Br]){e[Br]=!0,a.forEach(function(t){"selectionchange"!==t&&(zr.has(t)||Vr(t,!1,e),Vr(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Br]||(t[Br]=!0,Vr("selectionchange",!1,t))}}function Hr(e,t,n,r){switch(Yt(t)){case 1:var i=qt;break;case 4:i=Wt;break;default:i=Qt}n=i.bind(null,t,n,e),i=void 0,!Ne||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(i=!0),r?void 0!==i?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):void 0!==i?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function $r(e,t,n,r,i){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var a=r.tag;if(3===a||4===a){var s=r.stateNode.containerInfo;if(s===i||8===s.nodeType&&s.parentNode===i)break;if(4===a)for(a=r.return;null!==a;){var l=a.tag;if((3===l||4===l)&&((l=a.stateNode.containerInfo)===i||8===l.nodeType&&l.parentNode===i))return;a=a.return}for(;null!==s;){if(null===(a=gi(s)))return;if(5===(l=a.tag)||6===l){r=o=a;continue e}s=s.parentNode}}r=r.return}Me(function(){var r=o,i=we(n),a=[];e:{var s=Or.get(e);if(void 0!==s){var l=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=Tn;break;case"focusin":u="focus",l=vn;break;case"focusout":u="blur",l=vn;break;case"beforeblur":case"afterblur":l=vn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=pn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=jn;break;case Er:case Tr:case Ar:l=yn;break;case jr:l=On;break;case"scroll":l=fn;break;case"wheel":l=Ln;break;case"copy":case"cut":case"paste":l=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=An}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==s?s+"Capture":null:s;c=[];for(var h,p=r;null!==p;){var m=(h=p).stateNode;if(5===h.tag&&null!==m&&(h=m,null!==f&&(null!=(m=Le(p,f))&&c.push(qr(p,m,h)))),d)break;p=p.return}0<c.length&&(s=new l(s,u,null,n,i),a.push({event:s,listeners:c}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===xe||!(u=n.relatedTarget||n.fromElement)||!gi(u)&&!u[pi])&&(l||s)&&(s=i.window===i?i:(s=i.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?gi(u):null)&&(u!==(d=Ue(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=pn,m="onMouseLeave",f="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(c=An,m="onPointerLeave",f="onPointerEnter",p="pointer"),d=null==l?s:xi(l),h=null==u?s:xi(u),(s=new c(m,p+"leave",l,n,i)).target=d,s.relatedTarget=h,m=null,gi(i)===r&&((c=new c(f,p+"enter",u,n,i)).target=h,c.relatedTarget=d,m=c),d=m,l&&u)e:{for(f=u,p=0,h=c=l;h;h=Qr(h))p++;for(h=0,m=f;m;m=Qr(m))h++;for(;0<p-h;)c=Qr(c),p--;for(;0<h-p;)f=Qr(f),h--;for(;p--;){if(c===f||null!==f&&c===f.alternate)break e;c=Qr(c),f=Qr(f)}c=null}else c=null;null!==l&&Kr(a,s,l,c,!1),null!==u&&null!==d&&Kr(a,d,u,c,!0)}if("select"===(l=(s=r?xi(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var v=Yn;else if($n(s))if(Xn)v=ar;else{v=ir;var y=rr}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(v=or);switch(v&&(v=v(e,r))?qn(a,v,n,i):(y&&y(e,s,r),"focusout"===e&&(y=s._wrapperState)&&y.controlled&&"number"===s.type&&ee(s,"number",s.value)),y=r?xi(r):window,e){case"focusin":($n(y)||"true"===y.contentEditable)&&(vr=y,yr=r,gr=null);break;case"focusout":gr=yr=vr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(a,n,i);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":xr(a,n,i)}var g;if(Dn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Un?Vn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Fn&&"ko"!==n.locale&&(Un||"onCompositionStart"!==b?"onCompositionEnd"===b&&Un&&(g=en()):(Zt="value"in(Xt=i)?Xt.value:Xt.textContent,Un=!0)),0<(y=Wr(r,b)).length&&(b=new xn(b,e,null,n,i),a.push({event:b,listeners:y}),g?b.data=g:null!==(g=Bn(n))&&(b.data=g))),(g=zn?function(e,t){switch(e){case"compositionend":return Bn(t);case"keypress":return 32!==t.which?null:(In=!0,_n);case"textInput":return(e=t.data)===_n&&In?null:e;default:return null}}(e,n):function(e,t){if(Un)return"compositionend"===e||!Dn&&Vn(e,t)?(e=en(),Jt=Zt=Xt=null,Un=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Wr(r,"onBeforeInput")).length&&(i=new xn("onBeforeInput","beforeinput",null,n,i),a.push({event:i,listeners:r}),i.data=g))}_r(a,t)})}function qr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Wr(e,t){for(var n=t+"Capture",r=[];null!==e;){var i=e,o=i.stateNode;5===i.tag&&null!==o&&(i=o,null!=(o=Le(e,n))&&r.unshift(qr(e,o,i)),null!=(o=Le(e,t))&&r.push(qr(e,o,i))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,i){for(var o=t._reactName,a=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==u&&(s=u,i?null!=(l=Le(n,o))&&a.unshift(qr(n,l,s)):i||null!=(l=Le(n,o))&&a.push(qr(n,l,s))),n=n.return}0!==a.length&&e.push({event:t,listeners:a})}var Gr=/\r\n?/g,Yr=/\u0000|\uFFFD/g;function Xr(e){return("string"===typeof e?e:""+e).replace(Gr,"\n").replace(Yr,"")}function Zr(e,t,n){if(t=Xr(t),Xr(e)!==t&&n)throw Error(o(425))}function Jr(){}var ei=null,ti=null;function ni(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ri="function"===typeof setTimeout?setTimeout:void 0,ii="function"===typeof clearTimeout?clearTimeout:void 0,oi="function"===typeof Promise?Promise:void 0,ai="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof oi?function(e){return oi.resolve(null).then(e).catch(si)}:ri;function si(e){setTimeout(function(){throw e})}function li(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&8===i.nodeType)if("/$"===(n=i.data)){if(0===r)return e.removeChild(i),void Ut(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=i}while(n);Ut(t)}function ui(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ci(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var di=Math.random().toString(36).slice(2),fi="__reactFiber$"+di,hi="__reactProps$"+di,pi="__reactContainer$"+di,mi="__reactEvents$"+di,vi="__reactListeners$"+di,yi="__reactHandles$"+di;function gi(e){var t=e[fi];if(t)return t;for(var n=e.parentNode;n;){if(t=n[pi]||n[fi]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ci(e);null!==e;){if(n=e[fi])return n;e=ci(e)}return t}n=(e=n).parentNode}return null}function bi(e){return!(e=e[fi]||e[pi])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xi(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function wi(e){return e[hi]||null}var Si=[],ki=-1;function Pi(e){return{current:e}}function Ci(e){0>ki||(e.current=Si[ki],Si[ki]=null,ki--)}function Ei(e,t){ki++,Si[ki]=e.current,e.current=t}var Ti={},Ai=Pi(Ti),ji=Pi(!1),Oi=Ti;function Mi(e,t){var n=e.type.contextTypes;if(!n)return Ti;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i,o={};for(i in n)o[i]=t[i];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Li(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ni(){Ci(ji),Ci(Ai)}function Di(e,t,n){if(Ai.current!==Ti)throw Error(o(168));Ei(Ai,t),Ei(ji,n)}function Ri(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var i in r=r.getChildContext())if(!(i in t))throw Error(o(108,H(e)||"Unknown",i));return F({},n,r)}function zi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ti,Oi=Ai.current,Ei(Ai,e),Ei(ji,ji.current),!0}function Fi(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=Ri(e,t,Oi),r.__reactInternalMemoizedMergedChildContext=e,Ci(ji),Ci(Ai),Ei(Ai,e)):Ci(ji),Ei(ji,n)}var _i=null,Ii=!1,Vi=!1;function Bi(e){null===_i?_i=[e]:_i.push(e)}function Ui(){if(!Vi&&null!==_i){Vi=!0;var e=0,t=bt;try{var n=_i;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}_i=null,Ii=!1}catch(i){throw null!==_i&&(_i=_i.slice(e+1)),Qe(Je,Ui),i}finally{bt=t,Vi=!1}}return null}var Hi=[],$i=0,qi=null,Wi=0,Qi=[],Ki=0,Gi=null,Yi=1,Xi="";function Zi(e,t){Hi[$i++]=Wi,Hi[$i++]=qi,qi=e,Wi=t}function Ji(e,t,n){Qi[Ki++]=Yi,Qi[Ki++]=Xi,Qi[Ki++]=Gi,Gi=e;var r=Yi;e=Xi;var i=32-at(r)-1;r&=~(1<<i),n+=1;var o=32-at(t)+i;if(30<o){var a=i-i%5;o=(r&(1<<a)-1).toString(32),r>>=a,i-=a,Yi=1<<32-at(t)+i|n<<i|r,Xi=o+e}else Yi=1<<o|n<<i|r,Xi=e}function eo(e){null!==e.return&&(Zi(e,1),Ji(e,1,0))}function to(e){for(;e===qi;)qi=Hi[--$i],Hi[$i]=null,Wi=Hi[--$i],Hi[$i]=null;for(;e===Gi;)Gi=Qi[--Ki],Qi[Ki]=null,Xi=Qi[--Ki],Qi[Ki]=null,Yi=Qi[--Ki],Qi[Ki]=null}var no=null,ro=null,io=!1,oo=null;function ao(e,t){var n=Mu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function so(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,no=e,ro=ui(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,no=e,ro=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Gi?{id:Yi,overflow:Xi}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Mu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,no=e,ro=null,!0);default:return!1}}function lo(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function uo(e){if(io){var t=ro;if(t){var n=t;if(!so(e,t)){if(lo(e))throw Error(o(418));t=ui(n.nextSibling);var r=no;t&&so(e,t)?ao(r,n):(e.flags=-4097&e.flags|2,io=!1,no=e)}}else{if(lo(e))throw Error(o(418));e.flags=-4097&e.flags|2,io=!1,no=e}}}function co(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;no=e}function fo(e){if(e!==no)return!1;if(!io)return co(e),io=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!ni(e.type,e.memoizedProps)),t&&(t=ro)){if(lo(e))throw ho(),Error(o(418));for(;t;)ao(e,t),t=ui(t.nextSibling)}if(co(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ro=ui(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ro=null}}else ro=no?ui(e.stateNode.nextSibling):null;return!0}function ho(){for(var e=ro;e;)e=ui(e.nextSibling)}function po(){ro=no=null,io=!1}function mo(e){null===oo?oo=[e]:oo.push(e)}var vo=x.ReactCurrentBatchConfig;function yo(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var i=r,a=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===a?t.ref:(t=function(e){var t=i.refs;null===e?delete t[a]:t[a]=e},t._stringRef=a,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function go(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bo(e){return(0,e._init)(e._payload)}function xo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function i(e,t){return(e=Nu(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Fu(n,e.mode,r)).return=e,t):((t=i(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===k?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===L&&bo(o)===t.type)?((r=i(t,n.props)).ref=yo(e,t,n),r.return=e,r):((r=Du(n.type,n.key,n.props,null,e.mode,r)).ref=yo(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=_u(n,e.mode,r)).return=e,t):((t=i(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Ru(n,e.mode,r,o)).return=e,t):((t=i(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Fu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Du(t.type,t.key,t.props,null,e.mode,n)).ref=yo(e,null,t),n.return=e,n;case S:return(t=_u(t,e.mode,n)).return=e,t;case L:return f(e,(0,t._init)(t._payload),n)}if(te(t)||R(t))return(t=Ru(t,e.mode,n,null)).return=e,t;go(e,t)}return null}function h(e,t,n,r){var i=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==i?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===i?u(e,t,n,r):null;case S:return n.key===i?c(e,t,n,r):null;case L:return h(e,t,(i=n._init)(n._payload),r)}if(te(n)||R(n))return null!==i?null:d(e,t,n,r,null);go(e,n)}return null}function p(e,t,n,r,i){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,i);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,i);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,i);case L:return p(e,t,n,(0,r._init)(r._payload),i)}if(te(r)||R(r))return d(t,e=e.get(n)||null,r,i,null);go(t,r)}return null}function m(i,o,s,l){for(var u=null,c=null,d=o,m=o=0,v=null;null!==d&&m<s.length;m++){d.index>m?(v=d,d=null):v=d.sibling;var y=h(i,d,s[m],l);if(null===y){null===d&&(d=v);break}e&&d&&null===y.alternate&&t(i,d),o=a(y,o,m),null===c?u=y:c.sibling=y,c=y,d=v}if(m===s.length)return n(i,d),io&&Zi(i,m),u;if(null===d){for(;m<s.length;m++)null!==(d=f(i,s[m],l))&&(o=a(d,o,m),null===c?u=d:c.sibling=d,c=d);return io&&Zi(i,m),u}for(d=r(i,d);m<s.length;m++)null!==(v=p(d,i,m,s[m],l))&&(e&&null!==v.alternate&&d.delete(null===v.key?m:v.key),o=a(v,o,m),null===c?u=v:c.sibling=v,c=v);return e&&d.forEach(function(e){return t(i,e)}),io&&Zi(i,m),u}function v(i,s,l,u){var c=R(l);if("function"!==typeof c)throw Error(o(150));if(null==(l=c.call(l)))throw Error(o(151));for(var d=c=null,m=s,v=s=0,y=null,g=l.next();null!==m&&!g.done;v++,g=l.next()){m.index>v?(y=m,m=null):y=m.sibling;var b=h(i,m,g.value,u);if(null===b){null===m&&(m=y);break}e&&m&&null===b.alternate&&t(i,m),s=a(b,s,v),null===d?c=b:d.sibling=b,d=b,m=y}if(g.done)return n(i,m),io&&Zi(i,v),c;if(null===m){for(;!g.done;v++,g=l.next())null!==(g=f(i,g.value,u))&&(s=a(g,s,v),null===d?c=g:d.sibling=g,d=g);return io&&Zi(i,v),c}for(m=r(i,m);!g.done;v++,g=l.next())null!==(g=p(m,i,v,g.value,u))&&(e&&null!==g.alternate&&m.delete(null===g.key?v:g.key),s=a(g,s,v),null===d?c=g:d.sibling=g,d=g);return e&&m.forEach(function(e){return t(i,e)}),io&&Zi(i,v),c}return function e(r,o,a,l){if("object"===typeof a&&null!==a&&a.type===k&&null===a.key&&(a=a.props.children),"object"===typeof a&&null!==a){switch(a.$$typeof){case w:e:{for(var u=a.key,c=o;null!==c;){if(c.key===u){if((u=a.type)===k){if(7===c.tag){n(r,c.sibling),(o=i(c,a.props.children)).return=r,r=o;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===L&&bo(u)===c.type){n(r,c.sibling),(o=i(c,a.props)).ref=yo(r,c,a),o.return=r,r=o;break e}n(r,c);break}t(r,c),c=c.sibling}a.type===k?((o=Ru(a.props.children,r.mode,l,a.key)).return=r,r=o):((l=Du(a.type,a.key,a.props,null,r.mode,l)).ref=yo(r,o,a),l.return=r,r=l)}return s(r);case S:e:{for(c=a.key;null!==o;){if(o.key===c){if(4===o.tag&&o.stateNode.containerInfo===a.containerInfo&&o.stateNode.implementation===a.implementation){n(r,o.sibling),(o=i(o,a.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=_u(a,r.mode,l)).return=r,r=o}return s(r);case L:return e(r,o,(c=a._init)(a._payload),l)}if(te(a))return m(r,o,a,l);if(R(a))return v(r,o,a,l);go(r,a)}return"string"===typeof a&&""!==a||"number"===typeof a?(a=""+a,null!==o&&6===o.tag?(n(r,o.sibling),(o=i(o,a)).return=r,r=o):(n(r,o),(o=Fu(a,r.mode,l)).return=r,r=o),s(r)):n(r,o)}}var wo=xo(!0),So=xo(!1),ko=Pi(null),Po=null,Co=null,Eo=null;function To(){Eo=Co=Po=null}function Ao(e){var t=ko.current;Ci(ko),e._currentValue=t}function jo(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Oo(e,t){Po=e,Eo=Co=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bs=!0),e.firstContext=null)}function Mo(e){var t=e._currentValue;if(Eo!==e)if(e={context:e,memoizedValue:t,next:null},null===Co){if(null===Po)throw Error(o(308));Co=e,Po.dependencies={lanes:0,firstContext:e}}else Co=Co.next=e;return t}var Lo=null;function No(e){null===Lo?Lo=[e]:Lo.push(e)}function Do(e,t,n,r){var i=t.interleaved;return null===i?(n.next=n,No(t)):(n.next=i.next,i.next=n),t.interleaved=n,Ro(e,r)}function Ro(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var zo=!1;function Fo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function _o(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Io(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Vo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Al)){var i=r.pending;return null===i?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Ro(e,n)}return null===(i=r.interleaved)?(t.next=t,No(r)):(t.next=i.next,i.next=t),r.interleaved=t,Ro(e,n)}function Bo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,gt(e,n)}}function Uo(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var i=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?i=o=a:o=o.next=a,n=n.next}while(null!==n);null===o?i=o=t:o=o.next=t}else i=o=t;return n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ho(e,t,n,r){var i=e.updateQueue;zo=!1;var o=i.firstBaseUpdate,a=i.lastBaseUpdate,s=i.shared.pending;if(null!==s){i.shared.pending=null;var l=s,u=l.next;l.next=null,null===a?o=u:a.next=u,a=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==a&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==o){var d=i.baseState;for(a=0,c=u=l=null,s=o;;){var f=s.lane,h=s.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:h,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var p=e,m=s;switch(f=t,h=n,m.tag){case 1:if("function"===typeof(p=m.payload)){d=p.call(h,d,f);break e}d=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null===(f="function"===typeof(p=m.payload)?p.call(h,d,f):p)||void 0===f)break e;d=F({},d,f);break e;case 2:zo=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=i.effects)?i.effects=[s]:f.push(s))}else h={eventTime:h,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=h,l=d):c=c.next=h,a|=f;if(null===(s=s.next)){if(null===(s=i.shared.pending))break;s=(f=s).next,f.next=null,i.lastBaseUpdate=f,i.shared.pending=null}}if(null===c&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,null!==(t=i.shared.interleaved)){i=t;do{a|=i.lane,i=i.next}while(i!==t)}else null===o&&(i.shared.lanes=0);zl|=a,e.lanes=a,e.memoizedState=d}}function $o(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(null!==i){if(r.callback=null,r=n,"function"!==typeof i)throw Error(o(191,i));i.call(r)}}}var qo={},Wo=Pi(qo),Qo=Pi(qo),Ko=Pi(qo);function Go(e){if(e===qo)throw Error(o(174));return e}function Yo(e,t){switch(Ei(Ko,t),Ei(Qo,e),Ei(Wo,qo),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ci(Wo),Ei(Wo,t)}function Xo(){Ci(Wo),Ci(Qo),Ci(Ko)}function Zo(e){Go(Ko.current);var t=Go(Wo.current),n=le(t,e.type);t!==n&&(Ei(Qo,e),Ei(Wo,n))}function Jo(e){Qo.current===e&&(Ci(Wo),Ci(Qo))}var ea=Pi(0);function ta(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var na=[];function ra(){for(var e=0;e<na.length;e++)na[e]._workInProgressVersionPrimary=null;na.length=0}var ia=x.ReactCurrentDispatcher,oa=x.ReactCurrentBatchConfig,aa=0,sa=null,la=null,ua=null,ca=!1,da=!1,fa=0,ha=0;function pa(){throw Error(o(321))}function ma(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function va(e,t,n,r,i,a){if(aa=a,sa=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ia.current=null===e||null===e.memoizedState?Ja:es,e=n(r,i),da){a=0;do{if(da=!1,fa=0,25<=a)throw Error(o(301));a+=1,ua=la=null,t.updateQueue=null,ia.current=ts,e=n(r,i)}while(da)}if(ia.current=Za,t=null!==la&&null!==la.next,aa=0,ua=la=sa=null,ca=!1,t)throw Error(o(300));return e}function ya(){var e=0!==fa;return fa=0,e}function ga(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ua?sa.memoizedState=ua=e:ua=ua.next=e,ua}function ba(){if(null===la){var e=sa.alternate;e=null!==e?e.memoizedState:null}else e=la.next;var t=null===ua?sa.memoizedState:ua.next;if(null!==t)ua=t,la=e;else{if(null===e)throw Error(o(310));e={memoizedState:(la=e).memoizedState,baseState:la.baseState,baseQueue:la.baseQueue,queue:la.queue,next:null},null===ua?sa.memoizedState=ua=e:ua=ua.next=e}return ua}function xa(e,t){return"function"===typeof t?t(e):t}function wa(e){var t=ba(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=la,i=r.baseQueue,a=n.pending;if(null!==a){if(null!==i){var s=i.next;i.next=a.next,a.next=s}r.baseQueue=i=a,n.pending=null}if(null!==i){a=i.next,r=r.baseState;var l=s=null,u=null,c=a;do{var d=c.lane;if((aa&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(l=u=f,s=r):u=u.next=f,sa.lanes|=d,zl|=d}c=c.next}while(null!==c&&c!==a);null===u?s=r:u.next=l,sr(r,t.memoizedState)||(bs=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){i=e;do{a=i.lane,sa.lanes|=a,zl|=a,i=i.next}while(i!==e)}else null===i&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Sa(e){var t=ba(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,a=t.memoizedState;if(null!==i){n.pending=null;var s=i=i.next;do{a=e(a,s.action),s=s.next}while(s!==i);sr(a,t.memoizedState)||(bs=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function ka(){}function Pa(e,t){var n=sa,r=ba(),i=t(),a=!sr(r.memoizedState,i);if(a&&(r.memoizedState=i,bs=!0),r=r.queue,za(Ta.bind(null,n,r,e),[e]),r.getSnapshot!==t||a||null!==ua&&1&ua.memoizedState.tag){if(n.flags|=2048,Ma(9,Ea.bind(null,n,r,i,t),void 0,null),null===jl)throw Error(o(349));0!==(30&aa)||Ca(n,t,i)}return i}function Ca(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=sa.updateQueue)?(t={lastEffect:null,stores:null},sa.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ea(e,t,n,r){t.value=n,t.getSnapshot=r,Aa(t)&&ja(e)}function Ta(e,t,n){return n(function(){Aa(t)&&ja(e)})}function Aa(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function ja(e){var t=Ro(e,1);null!==t&&nu(t,e,1,-1)}function Oa(e){var t=ga();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xa,lastRenderedState:e},t.queue=e,e=e.dispatch=Ka.bind(null,sa,e),[t.memoizedState,e]}function Ma(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=sa.updateQueue)?(t={lastEffect:null,stores:null},sa.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function La(){return ba().memoizedState}function Na(e,t,n,r){var i=ga();sa.flags|=e,i.memoizedState=Ma(1|t,n,void 0,void 0===r?null:r)}function Da(e,t,n,r){var i=ba();r=void 0===r?null:r;var o=void 0;if(null!==la){var a=la.memoizedState;if(o=a.destroy,null!==r&&ma(r,a.deps))return void(i.memoizedState=Ma(t,n,o,r))}sa.flags|=e,i.memoizedState=Ma(1|t,n,o,r)}function Ra(e,t){return Na(8390656,8,e,t)}function za(e,t){return Da(2048,8,e,t)}function Fa(e,t){return Da(4,2,e,t)}function _a(e,t){return Da(4,4,e,t)}function Ia(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Va(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Da(4,4,Ia.bind(null,t,e),n)}function Ba(){}function Ua(e,t){var n=ba();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ma(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ha(e,t){var n=ba();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ma(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function $a(e,t,n){return 0===(21&aa)?(e.baseState&&(e.baseState=!1,bs=!0),e.memoizedState=n):(sr(n,t)||(n=mt(),sa.lanes|=n,zl|=n,e.baseState=!0),t)}function qa(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=oa.transition;oa.transition={};try{e(!1),t()}finally{bt=n,oa.transition=r}}function Wa(){return ba().memoizedState}function Qa(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Ga(e))Ya(t,n);else if(null!==(n=Do(e,t,n,r))){nu(n,e,r,eu()),Xa(n,t,r)}}function Ka(e,t,n){var r=tu(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Ga(e))Ya(t,i);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var a=t.lastRenderedState,s=o(a,n);if(i.hasEagerState=!0,i.eagerState=s,sr(s,a)){var l=t.interleaved;return null===l?(i.next=i,No(t)):(i.next=l.next,l.next=i),void(t.interleaved=i)}}catch(u){}null!==(n=Do(e,t,i,r))&&(nu(n,e,r,i=eu()),Xa(n,t,r))}}function Ga(e){var t=e.alternate;return e===sa||null!==t&&t===sa}function Ya(e,t){da=ca=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xa(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,gt(e,n)}}var Za={readContext:Mo,useCallback:pa,useContext:pa,useEffect:pa,useImperativeHandle:pa,useInsertionEffect:pa,useLayoutEffect:pa,useMemo:pa,useReducer:pa,useRef:pa,useState:pa,useDebugValue:pa,useDeferredValue:pa,useTransition:pa,useMutableSource:pa,useSyncExternalStore:pa,useId:pa,unstable_isNewReconciler:!1},Ja={readContext:Mo,useCallback:function(e,t){return ga().memoizedState=[e,void 0===t?null:t],e},useContext:Mo,useEffect:Ra,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Na(4194308,4,Ia.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Na(4194308,4,e,t)},useInsertionEffect:function(e,t){return Na(4,2,e,t)},useMemo:function(e,t){var n=ga();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=ga();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qa.bind(null,sa,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},ga().memoizedState=e},useState:Oa,useDebugValue:Ba,useDeferredValue:function(e){return ga().memoizedState=e},useTransition:function(){var e=Oa(!1),t=e[0];return e=qa.bind(null,e[1]),ga().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=sa,i=ga();if(io){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===jl)throw Error(o(349));0!==(30&aa)||Ca(r,t,n)}i.memoizedState=n;var a={value:n,getSnapshot:t};return i.queue=a,Ra(Ta.bind(null,r,a,e),[e]),r.flags|=2048,Ma(9,Ea.bind(null,r,a,n,t),void 0,null),n},useId:function(){var e=ga(),t=jl.identifierPrefix;if(io){var n=Xi;t=":"+t+"R"+(n=(Yi&~(1<<32-at(Yi)-1)).toString(32)+n),0<(n=fa++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ha++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:Mo,useCallback:Ua,useContext:Mo,useEffect:za,useImperativeHandle:Va,useInsertionEffect:Fa,useLayoutEffect:_a,useMemo:Ha,useReducer:wa,useRef:La,useState:function(){return wa(xa)},useDebugValue:Ba,useDeferredValue:function(e){return $a(ba(),la.memoizedState,e)},useTransition:function(){return[wa(xa)[0],ba().memoizedState]},useMutableSource:ka,useSyncExternalStore:Pa,useId:Wa,unstable_isNewReconciler:!1},ts={readContext:Mo,useCallback:Ua,useContext:Mo,useEffect:za,useImperativeHandle:Va,useInsertionEffect:Fa,useLayoutEffect:_a,useMemo:Ha,useReducer:Sa,useRef:La,useState:function(){return Sa(xa)},useDebugValue:Ba,useDeferredValue:function(e){var t=ba();return null===la?t.memoizedState=e:$a(t,la.memoizedState,e)},useTransition:function(){return[Sa(xa)[0],ba().memoizedState]},useMutableSource:ka,useSyncExternalStore:Pa,useId:Wa,unstable_isNewReconciler:!1};function ns(e,t){if(e&&e.defaultProps){for(var n in t=F({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:F({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var is={isMounted:function(e){return!!(e=e._reactInternals)&&Ue(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),i=tu(e),o=Io(r,i);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Vo(e,o,i))&&(nu(t,e,i,r),Bo(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),i=tu(e),o=Io(r,i);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Vo(e,o,i))&&(nu(t,e,i,r),Bo(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),i=Io(n,r);i.tag=2,void 0!==t&&null!==t&&(i.callback=t),null!==(t=Vo(e,i,r))&&(nu(t,e,r,n),Bo(t,e,r))}};function os(e,t,n,r,i,o,a){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,a):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(i,o))}function as(e,t,n){var r=!1,i=Ti,o=t.contextType;return"object"===typeof o&&null!==o?o=Mo(o):(i=Li(t)?Oi:Ai.current,o=(r=null!==(r=t.contextTypes)&&void 0!==r)?Mi(e,i):Ti),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=is,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=o),t}function ss(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&is.enqueueReplaceState(t,t.state,null)}function ls(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},Fo(e);var o=t.contextType;"object"===typeof o&&null!==o?i.context=Mo(o):(o=Li(t)?Oi:Ai.current,i.context=Mi(e,o)),i.state=e.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(rs(e,t,o,n),i.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof i.getSnapshotBeforeUpdate||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||(t=i.state,"function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount(),t!==i.state&&is.enqueueReplaceState(i,i.state,null),Ho(e,n,i,r),i.state=e.memoizedState),"function"===typeof i.componentDidMount&&(e.flags|=4194308)}function us(e,t){try{var n="",r=t;do{n+=B(r),r=r.return}while(r);var i=n}catch(o){i="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:i,digest:null}}function cs(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fs="function"===typeof WeakMap?WeakMap:Map;function hs(e,t,n){(n=Io(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){$l||($l=!0,ql=r),ds(0,t)},n}function ps(e,t,n){(n=Io(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){ds(0,t)}}var o=e.stateNode;return null!==o&&"function"===typeof o.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!==typeof r&&(null===Wl?Wl=new Set([this]):Wl.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fs;var i=new Set;r.set(t,i)}else void 0===(i=r.get(t))&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=Cu.bind(null,e,t,n),t.then(e,e))}function vs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function ys(e,t,n,r,i){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Io(-1,1)).tag=2,Vo(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=i,e)}var gs=x.ReactCurrentOwner,bs=!1;function xs(e,t,n,r){t.child=null===e?So(t,null,n,r):wo(t,e.child,n,r)}function ws(e,t,n,r,i){n=n.render;var o=t.ref;return Oo(t,i),r=va(e,t,n,r,o,i),n=ya(),null===e||bs?(io&&n&&eo(t),t.flags|=1,xs(e,t,r,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,$s(e,t,i))}function Ss(e,t,n,r,i){if(null===e){var o=n.type;return"function"!==typeof o||Lu(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Du(n.type,null,r,t,t.mode,i)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,ks(e,t,o,r,i))}if(o=e.child,0===(e.lanes&i)){var a=o.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(a,r)&&e.ref===t.ref)return $s(e,t,i)}return t.flags|=1,(e=Nu(o,r)).ref=t.ref,e.return=t,t.child=e}function ks(e,t,n,r,i){if(null!==e){var o=e.memoizedProps;if(lr(o,r)&&e.ref===t.ref){if(bs=!1,t.pendingProps=r=o,0===(e.lanes&i))return t.lanes=e.lanes,$s(e,t,i);0!==(131072&e.flags)&&(bs=!0)}}return Es(e,t,n,r,i)}function Ps(e,t,n){var r=t.pendingProps,i=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ei(Nl,Ll),Ll|=n;else{if(0===(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ei(Nl,Ll),Ll|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,Ei(Nl,Ll),Ll|=r}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,Ei(Nl,Ll),Ll|=r;return xs(e,t,i,n),t.child}function Cs(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Es(e,t,n,r,i){var o=Li(n)?Oi:Ai.current;return o=Mi(t,o),Oo(t,i),n=va(e,t,n,r,o,i),r=ya(),null===e||bs?(io&&r&&eo(t),t.flags|=1,xs(e,t,n,i),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,$s(e,t,i))}function Ts(e,t,n,r,i){if(Li(n)){var o=!0;zi(t)}else o=!1;if(Oo(t,i),null===t.stateNode)Hs(e,t),as(t,n,r),ls(t,n,r,i),r=!0;else if(null===e){var a=t.stateNode,s=t.memoizedProps;a.props=s;var l=a.context,u=n.contextType;"object"===typeof u&&null!==u?u=Mo(u):u=Mi(t,u=Li(n)?Oi:Ai.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof a.getSnapshotBeforeUpdate;d||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(s!==r||l!==u)&&ss(t,a,r,u),zo=!1;var f=t.memoizedState;a.state=f,Ho(t,r,a,i),l=t.memoizedState,s!==r||f!==l||ji.current||zo?("function"===typeof c&&(rs(t,n,c,r),l=t.memoizedState),(s=zo||os(t,n,s,r,f,l,u))?(d||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(t.flags|=4194308)):("function"===typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),a.props=r,a.state=l,a.context=u,r=s):("function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,_o(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:ns(t.type,s),a.props=u,d=t.pendingProps,f=a.context,"object"===typeof(l=n.contextType)&&null!==l?l=Mo(l):l=Mi(t,l=Li(n)?Oi:Ai.current);var h=n.getDerivedStateFromProps;(c="function"===typeof h||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(s!==d||f!==l)&&ss(t,a,r,l),zo=!1,f=t.memoizedState,a.state=f,Ho(t,r,a,i);var p=t.memoizedState;s!==d||f!==p||ji.current||zo?("function"===typeof h&&(rs(t,n,h,r),p=t.memoizedState),(u=zo||os(t,n,u,r,f,p,l)||!1)?(c||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,l),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,l)),"function"===typeof a.componentDidUpdate&&(t.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof a.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=l,r=u):("function"!==typeof a.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return As(e,t,n,r,o,i)}function As(e,t,n,r,i,o){Cs(e,t);var a=0!==(128&t.flags);if(!r&&!a)return i&&Fi(t,n,!1),$s(e,t,o);r=t.stateNode,gs.current=t;var s=a&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&a?(t.child=wo(t,e.child,null,o),t.child=wo(t,null,s,o)):xs(e,t,s,o),t.memoizedState=r.state,i&&Fi(t,n,!0),t.child}function js(e){var t=e.stateNode;t.pendingContext?Di(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Di(0,t.context,!1),Yo(e,t.containerInfo)}function Os(e,t,n,r,i){return po(),mo(i),t.flags|=256,xs(e,t,n,r),t.child}var Ms,Ls,Ns,Ds,Rs={dehydrated:null,treeContext:null,retryLane:0};function zs(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fs(e,t,n){var r,i=t.pendingProps,a=ea.current,s=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&a)),r?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(a|=1),Ei(ea,1&a),null===e)return uo(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=i.children,e=i.fallback,s?(i=t.mode,s=t.child,l={mode:"hidden",children:l},0===(1&i)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=zu(l,i,0,null),e=Ru(e,i,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=zs(n),t.memoizedState=Rs,e):_s(t,l));if(null!==(a=e.memoizedState)&&null!==(r=a.dehydrated))return function(e,t,n,r,i,a,s){if(n)return 256&t.flags?(t.flags&=-257,Is(e,t,s,r=cs(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(a=r.fallback,i=t.mode,r=zu({mode:"visible",children:r.children},i,0,null),(a=Ru(a,i,s,null)).flags|=2,r.return=t,a.return=t,r.sibling=a,t.child=r,0!==(1&t.mode)&&wo(t,e.child,null,s),t.child.memoizedState=zs(s),t.memoizedState=Rs,a);if(0===(1&t.mode))return Is(e,t,s,null);if("$!"===i.data){if(r=i.nextSibling&&i.nextSibling.dataset)var l=r.dgst;return r=l,Is(e,t,s,r=cs(a=Error(o(419)),r,void 0))}if(l=0!==(s&e.childLanes),bs||l){if(null!==(r=jl)){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}0!==(i=0!==(i&(r.suspendedLanes|s))?0:i)&&i!==a.retryLane&&(a.retryLane=i,Ro(e,i),nu(r,e,i,-1))}return mu(),Is(e,t,s,r=cs(Error(o(421))))}return"$?"===i.data?(t.flags|=128,t.child=e.child,t=Tu.bind(null,e),i._reactRetry=t,null):(e=a.treeContext,ro=ui(i.nextSibling),no=t,io=!0,oo=null,null!==e&&(Qi[Ki++]=Yi,Qi[Ki++]=Xi,Qi[Ki++]=Gi,Yi=e.id,Xi=e.overflow,Gi=t),t=_s(t,r.children),t.flags|=4096,t)}(e,t,l,i,r,a,n);if(s){s=i.fallback,l=t.mode,r=(a=e.child).sibling;var u={mode:"hidden",children:i.children};return 0===(1&l)&&t.child!==a?((i=t.child).childLanes=0,i.pendingProps=u,t.deletions=null):(i=Nu(a,u)).subtreeFlags=14680064&a.subtreeFlags,null!==r?s=Nu(r,s):(s=Ru(s,l,n,null)).flags|=2,s.return=t,i.return=t,i.sibling=s,t.child=i,i=s,s=t.child,l=null===(l=e.child.memoizedState)?zs(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=Rs,i}return e=(s=e.child).sibling,i=Nu(s,{mode:"visible",children:i.children}),0===(1&t.mode)&&(i.lanes=n),i.return=t,i.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=i,t.memoizedState=null,i}function _s(e,t){return(t=zu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Is(e,t,n,r){return null!==r&&mo(r),wo(t,e.child,null,n),(e=_s(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Vs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),jo(e.return,t,n)}function Bs(e,t,n,r,i){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=i)}function Us(e,t,n){var r=t.pendingProps,i=r.revealOrder,o=r.tail;if(xs(e,t,r.children,n),0!==(2&(r=ea.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Vs(e,n,t);else if(19===e.tag)Vs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ei(ea,r),0===(1&t.mode))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;null!==n;)null!==(e=n.alternate)&&null===ta(e)&&(i=n),n=n.sibling;null===(n=i)?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Bs(t,!1,i,n,o);break;case"backwards":for(n=null,i=t.child,t.child=null;null!==i;){if(null!==(e=i.alternate)&&null===ta(e)){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Bs(t,!0,n,null,o);break;case"together":Bs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Hs(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function $s(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),zl|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Nu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Nu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function qs(e,t){if(!io)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ws(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=14680064&i.subtreeFlags,r|=14680064&i.flags,i.return=e,i=i.sibling;else for(i=e.child;null!==i;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qs(e,t,n){var r=t.pendingProps;switch(to(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ws(t),null;case 1:case 17:return Li(t.type)&&Ni(),Ws(t),null;case 3:return r=t.stateNode,Xo(),Ci(ji),Ci(Ai),ra(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==oo&&(au(oo),oo=null))),Ls(e,t),Ws(t),null;case 5:Jo(t);var i=Go(Ko.current);if(n=t.type,null!==e&&null!=t.stateNode)Ns(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return Ws(t),null}if(e=Go(Wo.current),fo(t)){r=t.stateNode,n=t.type;var a=t.memoizedProps;switch(r[fi]=t,r[hi]=a,e=0!==(1&t.mode),n){case"dialog":Ir("cancel",r),Ir("close",r);break;case"iframe":case"object":case"embed":Ir("load",r);break;case"video":case"audio":for(i=0;i<Rr.length;i++)Ir(Rr[i],r);break;case"source":Ir("error",r);break;case"img":case"image":case"link":Ir("error",r),Ir("load",r);break;case"details":Ir("toggle",r);break;case"input":Y(r,a),Ir("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!a.multiple},Ir("invalid",r);break;case"textarea":ie(r,a),Ir("invalid",r)}for(var l in ge(n,a),i=null,a)if(a.hasOwnProperty(l)){var u=a[l];"children"===l?"string"===typeof u?r.textContent!==u&&(!0!==a.suppressHydrationWarning&&Zr(r.textContent,u,e),i=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==a.suppressHydrationWarning&&Zr(r.textContent,u,e),i=["children",""+u]):s.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&Ir("scroll",r)}switch(n){case"input":W(r),J(r,a,!0);break;case"textarea":W(r),ae(r);break;case"select":case"option":break;default:"function"===typeof a.onClick&&(r.onclick=Jr)}r=i,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===i.nodeType?i:i.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[fi]=t,e[hi]=r,Ms(e,t,!1,!1),t.stateNode=e;e:{switch(l=be(n,r),n){case"dialog":Ir("cancel",e),Ir("close",e),i=r;break;case"iframe":case"object":case"embed":Ir("load",e),i=r;break;case"video":case"audio":for(i=0;i<Rr.length;i++)Ir(Rr[i],e);i=r;break;case"source":Ir("error",e),i=r;break;case"img":case"image":case"link":Ir("error",e),Ir("load",e),i=r;break;case"details":Ir("toggle",e),i=r;break;case"input":Y(e,r),i=G(e,r),Ir("invalid",e);break;case"option":default:i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=F({},r,{value:void 0}),Ir("invalid",e);break;case"textarea":ie(e,r),i=re(e,r),Ir("invalid",e)}for(a in ge(n,i),u=i)if(u.hasOwnProperty(a)){var c=u[a];"style"===a?ve(e,c):"dangerouslySetInnerHTML"===a?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===a?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==a&&"suppressHydrationWarning"!==a&&"autoFocus"!==a&&(s.hasOwnProperty(a)?null!=c&&"onScroll"===a&&Ir("scroll",e):null!=c&&b(e,a,c,l))}switch(n){case"input":W(e),J(e,r,!1);break;case"textarea":W(e),ae(e);break;case"option":null!=r.value&&e.setAttribute("value",""+$(r.value));break;case"select":e.multiple=!!r.multiple,null!=(a=r.value)?ne(e,!!r.multiple,a,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof i.onClick&&(e.onclick=Jr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Ws(t),null;case 6:if(e&&null!=t.stateNode)Ds(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(n=Go(Ko.current),Go(Wo.current),fo(t)){if(r=t.stateNode,n=t.memoizedProps,r[fi]=t,(a=r.nodeValue!==n)&&null!==(e=no))switch(e.tag){case 3:Zr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Zr(r.nodeValue,n,0!==(1&e.mode))}a&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fi]=t,t.stateNode=r}return Ws(t),null;case 13:if(Ci(ea),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(io&&null!==ro&&0!==(1&t.mode)&&0===(128&t.flags))ho(),po(),t.flags|=98560,a=!1;else if(a=fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(o(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(o(317));a[fi]=t}else po(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Ws(t),a=!1}else null!==oo&&(au(oo),oo=null),a=!0;if(!a)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&ea.current)?0===Dl&&(Dl=3):mu())),null!==t.updateQueue&&(t.flags|=4),Ws(t),null);case 4:return Xo(),Ls(e,t),null===e&&Ur(t.stateNode.containerInfo),Ws(t),null;case 10:return Ao(t.type._context),Ws(t),null;case 19:if(Ci(ea),null===(a=t.memoizedState))return Ws(t),null;if(r=0!==(128&t.flags),null===(l=a.rendering))if(r)qs(a,!1);else{if(0!==Dl||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=ta(e))){for(t.flags|=128,qs(a,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(a=n).flags&=14680066,null===(l=a.alternate)?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=l.childLanes,a.lanes=l.lanes,a.child=l.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=l.memoizedProps,a.memoizedState=l.memoizedState,a.updateQueue=l.updateQueue,a.type=l.type,e=l.dependencies,a.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ei(ea,1&ea.current|2),t.child}e=e.sibling}null!==a.tail&&Xe()>Ul&&(t.flags|=128,r=!0,qs(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ta(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),qs(a,!0),null===a.tail&&"hidden"===a.tailMode&&!l.alternate&&!io)return Ws(t),null}else 2*Xe()-a.renderingStartTime>Ul&&1073741824!==n&&(t.flags|=128,r=!0,qs(a,!1),t.lanes=4194304);a.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=a.last)?n.sibling=l:t.child=l,a.last=l)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=Xe(),t.sibling=null,n=ea.current,Ei(ea,r?1&n|2:1&n),t):(Ws(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Ll)&&(Ws(t),6&t.subtreeFlags&&(t.flags|=8192)):Ws(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function Ks(e,t){switch(to(t),t.tag){case 1:return Li(t.type)&&Ni(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xo(),Ci(ji),Ci(Ai),ra(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Jo(t),null;case 13:if(Ci(ea),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));po()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ci(ea),null;case 4:return Xo(),null;case 10:return Ao(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Ms=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ls=function(){},Ns=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Go(Wo.current);var o,a=null;switch(n){case"input":i=G(e,i),r=G(e,r),a=[];break;case"select":i=F({},i,{value:void 0}),r=F({},r,{value:void 0}),a=[];break;case"textarea":i=re(e,i),r=re(e,r),a=[];break;default:"function"!==typeof i.onClick&&"function"===typeof r.onClick&&(e.onclick=Jr)}for(c in ge(n,r),n=null,i)if(!r.hasOwnProperty(c)&&i.hasOwnProperty(c)&&null!=i[c])if("style"===c){var l=i[c];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(s.hasOwnProperty(c)?a||(a=[]):(a=a||[]).push(c,null));for(c in r){var u=r[c];if(l=null!=i?i[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(null!=u||null!=l))if("style"===c)if(l){for(o in l)!l.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&l[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(a||(a=[]),a.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(a=a||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(a=a||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(s.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Ir("scroll",e),a||l===u||(a=[])):(a=a||[]).push(c,u))}n&&(a=a||[]).push("style",n);var c=a;(t.updateQueue=c)&&(t.flags|=4)}},Ds=function(e,t,n,r){n!==r&&(t.flags|=4)};var Gs=!1,Ys=!1,Xs="function"===typeof WeakSet?WeakSet:Set,Zs=null;function Js(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Pu(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){Pu(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var i=r=r.next;do{if((i.tag&e)===e){var o=i.destroy;i.destroy=void 0,void 0!==o&&el(t,n,o)}i=i.next}while(i!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function il(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ol(e){var t=e.alternate;null!==t&&(e.alternate=null,ol(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fi],delete t[hi],delete t[mi],delete t[vi],delete t[yi])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function al(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||al(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Jr));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function ul(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ul(e,t,n),e=e.sibling;null!==e;)ul(e,t,n),e=e.sibling}var cl=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)hl(e,t,n),n=n.sibling}function hl(e,t,n){if(ot&&"function"===typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(it,n)}catch(s){}switch(n.tag){case 5:Ys||Js(n,t);case 6:var r=cl,i=dl;cl=null,fl(e,t,n),dl=i,null!==(cl=r)&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cl.removeChild(n.stateNode));break;case 18:null!==cl&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?li(e.parentNode,n):1===e.nodeType&&li(e,n),Ut(e)):li(cl,n.stateNode));break;case 4:r=cl,i=dl,cl=n.stateNode.containerInfo,dl=!0,fl(e,t,n),cl=r,dl=i;break;case 0:case 11:case 14:case 15:if(!Ys&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){i=r=r.next;do{var o=i,a=o.destroy;o=o.tag,void 0!==a&&(0!==(2&o)||0!==(4&o))&&el(n,t,a),i=i.next}while(i!==r)}fl(e,t,n);break;case 1:if(!Ys&&(Js(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Pu(n,t,s)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Ys=(r=Ys)||null!==n.memoizedState,fl(e,t,n),Ys=r):fl(e,t,n);break;default:fl(e,t,n)}}function pl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Xs),t.forEach(function(t){var r=Au.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ml(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var i=n[r];try{var a=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:cl=l.stateNode,dl=!1;break e;case 3:case 4:cl=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===cl)throw Error(o(160));hl(a,s,i),cl=null,dl=!1;var u=i.alternate;null!==u&&(u.return=null),i.return=null}catch(c){Pu(i,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)vl(t,e),t=t.sibling}function vl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),yl(e),4&r){try{nl(3,e,e.return),rl(3,e)}catch(v){Pu(e,e.return,v)}try{nl(5,e,e.return)}catch(v){Pu(e,e.return,v)}}break;case 1:ml(t,e),yl(e),512&r&&null!==n&&Js(n,n.return);break;case 5:if(ml(t,e),yl(e),512&r&&null!==n&&Js(n,n.return),32&e.flags){var i=e.stateNode;try{fe(i,"")}catch(v){Pu(e,e.return,v)}}if(4&r&&null!=(i=e.stateNode)){var a=e.memoizedProps,s=null!==n?n.memoizedProps:a,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===a.type&&null!=a.name&&X(i,a),be(l,s);var c=be(l,a);for(s=0;s<u.length;s+=2){var d=u[s],f=u[s+1];"style"===d?ve(i,f):"dangerouslySetInnerHTML"===d?de(i,f):"children"===d?fe(i,f):b(i,d,f,c)}switch(l){case"input":Z(i,a);break;case"textarea":oe(i,a);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!a.multiple;var p=a.value;null!=p?ne(i,!!a.multiple,p,!1):h!==!!a.multiple&&(null!=a.defaultValue?ne(i,!!a.multiple,a.defaultValue,!0):ne(i,!!a.multiple,a.multiple?[]:"",!1))}i[hi]=a}catch(v){Pu(e,e.return,v)}}break;case 6:if(ml(t,e),yl(e),4&r){if(null===e.stateNode)throw Error(o(162));i=e.stateNode,a=e.memoizedProps;try{i.nodeValue=a}catch(v){Pu(e,e.return,v)}}break;case 3:if(ml(t,e),yl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ut(t.containerInfo)}catch(v){Pu(e,e.return,v)}break;case 4:default:ml(t,e),yl(e);break;case 13:ml(t,e),yl(e),8192&(i=e.child).flags&&(a=null!==i.memoizedState,i.stateNode.isHidden=a,!a||null!==i.alternate&&null!==i.alternate.memoizedState||(Bl=Xe())),4&r&&pl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Ys=(c=Ys)||d,ml(t,e),Ys=c):ml(t,e),yl(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Zs=e,d=e.child;null!==d;){for(f=Zs=d;null!==Zs;){switch(p=(h=Zs).child,h.tag){case 0:case 11:case 14:case 15:nl(4,h,h.return);break;case 1:Js(h,h.return);var m=h.stateNode;if("function"===typeof m.componentWillUnmount){r=h,n=h.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(v){Pu(r,n,v)}}break;case 5:Js(h,h.return);break;case 22:if(null!==h.memoizedState){wl(f);continue}}null!==p?(p.return=h,Zs=p):wl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{i=f.stateNode,c?"function"===typeof(a=i.style).setProperty?a.setProperty("display","none","important"):a.display="none":(l=f.stateNode,s=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,l.style.display=me("display",s))}catch(v){Pu(e,e.return,v)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(v){Pu(e,e.return,v)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),yl(e),4&r&&pl(e);case 21:}}function yl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(al(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var i=r.stateNode;32&r.flags&&(fe(i,""),r.flags&=-33),ul(e,sl(e),i);break;case 3:case 4:var a=r.stateNode.containerInfo;ll(e,sl(e),a);break;default:throw Error(o(161))}}catch(s){Pu(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function gl(e,t,n){Zs=e,bl(e,t,n)}function bl(e,t,n){for(var r=0!==(1&e.mode);null!==Zs;){var i=Zs,o=i.child;if(22===i.tag&&r){var a=null!==i.memoizedState||Gs;if(!a){var s=i.alternate,l=null!==s&&null!==s.memoizedState||Ys;s=Gs;var u=Ys;if(Gs=a,(Ys=l)&&!u)for(Zs=i;null!==Zs;)l=(a=Zs).child,22===a.tag&&null!==a.memoizedState?Sl(i):null!==l?(l.return=a,Zs=l):Sl(i);for(;null!==o;)Zs=o,bl(o,t,n),o=o.sibling;Zs=i,Gs=s,Ys=u}xl(e)}else 0!==(8772&i.subtreeFlags)&&null!==o?(o.return=i,Zs=o):xl(e)}}function xl(e){for(;null!==Zs;){var t=Zs;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Ys||rl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Ys)if(null===n)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:ns(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;null!==a&&$o(t,a,r);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}$o(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Ut(f)}}}break;default:throw Error(o(163))}Ys||512&t.flags&&il(t)}catch(h){Pu(t,t.return,h)}}if(t===e){Zs=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zs=n;break}Zs=t.return}}function wl(e){for(;null!==Zs;){var t=Zs;if(t===e){Zs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zs=n;break}Zs=t.return}}function Sl(e){for(;null!==Zs;){var t=Zs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){Pu(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var i=t.return;try{r.componentDidMount()}catch(l){Pu(t,i,l)}}var o=t.return;try{il(t)}catch(l){Pu(t,o,l)}break;case 5:var a=t.return;try{il(t)}catch(l){Pu(t,a,l)}}}catch(l){Pu(t,t.return,l)}if(t===e){Zs=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Zs=s;break}Zs=t.return}}var kl,Pl=Math.ceil,Cl=x.ReactCurrentDispatcher,El=x.ReactCurrentOwner,Tl=x.ReactCurrentBatchConfig,Al=0,jl=null,Ol=null,Ml=0,Ll=0,Nl=Pi(0),Dl=0,Rl=null,zl=0,Fl=0,_l=0,Il=null,Vl=null,Bl=0,Ul=1/0,Hl=null,$l=!1,ql=null,Wl=null,Ql=!1,Kl=null,Gl=0,Yl=0,Xl=null,Zl=-1,Jl=0;function eu(){return 0!==(6&Al)?Xe():-1!==Zl?Zl:Zl=Xe()}function tu(e){return 0===(1&e.mode)?1:0!==(2&Al)&&0!==Ml?Ml&-Ml:null!==vo.transition?(0===Jl&&(Jl=mt()),Jl):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Yt(e.type)}function nu(e,t,n,r){if(50<Yl)throw Yl=0,Xl=null,Error(o(185));yt(e,n,r),0!==(2&Al)&&e===jl||(e===jl&&(0===(2&Al)&&(Fl|=n),4===Dl&&su(e,Ml)),ru(e,r),1===n&&0===Al&&0===(1&t.mode)&&(Ul=Xe()+500,Ii&&Ui()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,o=e.pendingLanes;0<o;){var a=31-at(o),s=1<<a,l=i[a];-1===l?0!==(s&n)&&0===(s&r)||(i[a]=ht(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}}(e,t);var r=ft(e,e===jl?Ml:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Ii=!0,Bi(e)}(lu.bind(null,e)):Bi(lu.bind(null,e)),ai(function(){0===(6&Al)&&Ui()}),n=null;else{switch(xt(r)){case 1:n=Je;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=ju(n,iu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function iu(e,t){if(Zl=-1,Jl=0,0!==(6&Al))throw Error(o(327));var n=e.callbackNode;if(Su()&&e.callbackNode!==n)return null;var r=ft(e,e===jl?Ml:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=vu(e,r);else{t=r;var i=Al;Al|=2;var a=pu();for(jl===e&&Ml===t||(Hl=null,Ul=Xe()+500,fu(e,t));;)try{gu();break}catch(l){hu(e,l)}To(),Cl.current=a,Al=i,null!==Ol?t=0:(jl=null,Ml=0,t=Dl)}if(0!==t){if(2===t&&(0!==(i=pt(e))&&(r=i,t=ou(e,i))),1===t)throw n=Rl,fu(e,0),su(e,r),ru(e,Xe()),n;if(6===t)su(e,r);else{if(i=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var i=n[r],o=i.getSnapshot;i=i.value;try{if(!sr(o(),i))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(i)&&(2===(t=vu(e,r))&&(0!==(a=pt(e))&&(r=a,t=ou(e,a))),1===t))throw n=Rl,fu(e,0),su(e,r),ru(e,Xe()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:wu(e,Vl,Hl);break;case 3:if(su(e,r),(130023424&r)===r&&10<(t=Bl+500-Xe())){if(0!==ft(e,0))break;if(((i=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=ri(wu.bind(null,e,Vl,Hl),t);break}wu(e,Vl,Hl);break;case 4:if(su(e,r),(4194240&r)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-at(r);a=1<<s,(s=t[s])>i&&(i=s),r&=~a}if(r=i,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Pl(r/1960))-r)){e.timeoutHandle=ri(wu.bind(null,e,Vl,Hl),r);break}wu(e,Vl,Hl);break;default:throw Error(o(329))}}}return ru(e,Xe()),e.callbackNode===n?iu.bind(null,e):null}function ou(e,t){var n=Il;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=vu(e,t))&&(t=Vl,Vl=n,null!==t&&au(t)),e}function au(e){null===Vl?Vl=e:Vl.push.apply(Vl,e)}function su(e,t){for(t&=~_l,t&=~Fl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-at(t),r=1<<n;e[n]=-1,t&=~r}}function lu(e){if(0!==(6&Al))throw Error(o(327));Su();var t=ft(e,0);if(0===(1&t))return ru(e,Xe()),null;var n=vu(e,t);if(0!==e.tag&&2===n){var r=pt(e);0!==r&&(t=r,n=ou(e,r))}if(1===n)throw n=Rl,fu(e,0),su(e,t),ru(e,Xe()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,Vl,Hl),ru(e,Xe()),null}function uu(e,t){var n=Al;Al|=1;try{return e(t)}finally{0===(Al=n)&&(Ul=Xe()+500,Ii&&Ui())}}function cu(e){null!==Kl&&0===Kl.tag&&0===(6&Al)&&Su();var t=Al;Al|=1;var n=Tl.transition,r=bt;try{if(Tl.transition=null,bt=1,e)return e()}finally{bt=r,Tl.transition=n,0===(6&(Al=t))&&Ui()}}function du(){Ll=Nl.current,Ci(Nl)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ii(n)),null!==Ol)for(n=Ol.return;null!==n;){var r=n;switch(to(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Ni();break;case 3:Xo(),Ci(ji),Ci(Ai),ra();break;case 5:Jo(r);break;case 4:Xo();break;case 13:case 19:Ci(ea);break;case 10:Ao(r.type._context);break;case 22:case 23:du()}n=n.return}if(jl=e,Ol=e=Nu(e.current,null),Ml=Ll=t,Dl=0,Rl=null,_l=Fl=zl=0,Vl=Il=null,null!==Lo){for(t=0;t<Lo.length;t++)if(null!==(r=(n=Lo[t]).interleaved)){n.interleaved=null;var i=r.next,o=n.pending;if(null!==o){var a=o.next;o.next=i,r.next=a}n.pending=r}Lo=null}return e}function hu(e,t){for(;;){var n=Ol;try{if(To(),ia.current=Za,ca){for(var r=sa.memoizedState;null!==r;){var i=r.queue;null!==i&&(i.pending=null),r=r.next}ca=!1}if(aa=0,ua=la=sa=null,da=!1,fa=0,El.current=null,null===n||null===n.return){Dl=1,Rl=t,Ol=null;break}e:{var a=e,s=n.return,l=n,u=t;if(t=Ml,l.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var p=vs(s);if(null!==p){p.flags&=-257,ys(p,s,l,0,t),1&p.mode&&ms(a,c,t),u=c;var m=(t=p).updateQueue;if(null===m){var v=new Set;v.add(u),t.updateQueue=v}else m.add(u);break e}if(0===(1&t)){ms(a,c,t),mu();break e}u=Error(o(426))}else if(io&&1&l.mode){var y=vs(s);if(null!==y){0===(65536&y.flags)&&(y.flags|=256),ys(y,s,l,0,t),mo(us(u,l));break e}}a=u=us(u,l),4!==Dl&&(Dl=2),null===Il?Il=[a]:Il.push(a),a=s;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t,Uo(a,hs(0,u,t));break e;case 1:l=u;var g=a.type,b=a.stateNode;if(0===(128&a.flags)&&("function"===typeof g.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Wl||!Wl.has(b)))){a.flags|=65536,t&=-t,a.lanes|=t,Uo(a,ps(a,l,t));break e}}a=a.return}while(null!==a)}xu(n)}catch(x){t=x,Ol===n&&null!==n&&(Ol=n=n.return);continue}break}}function pu(){var e=Cl.current;return Cl.current=Za,null===e?Za:e}function mu(){0!==Dl&&3!==Dl&&2!==Dl||(Dl=4),null===jl||0===(268435455&zl)&&0===(268435455&Fl)||su(jl,Ml)}function vu(e,t){var n=Al;Al|=2;var r=pu();for(jl===e&&Ml===t||(Hl=null,fu(e,t));;)try{yu();break}catch(i){hu(e,i)}if(To(),Al=n,Cl.current=r,null!==Ol)throw Error(o(261));return jl=null,Ml=0,Dl}function yu(){for(;null!==Ol;)bu(Ol)}function gu(){for(;null!==Ol&&!Ge();)bu(Ol)}function bu(e){var t=kl(e.alternate,e,Ll);e.memoizedProps=e.pendingProps,null===t?xu(e):Ol=t,El.current=null}function xu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Qs(n,t,Ll)))return void(Ol=n)}else{if(null!==(n=Ks(n,t)))return n.flags&=32767,void(Ol=n);if(null===e)return Dl=6,void(Ol=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Ol=t);Ol=t=e}while(null!==t);0===Dl&&(Dl=5)}function wu(e,t,n){var r=bt,i=Tl.transition;try{Tl.transition=null,bt=1,function(e,t,n,r){do{Su()}while(null!==Kl);if(0!==(6&Al))throw Error(o(327));n=e.finishedWork;var i=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var a=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-at(n),o=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~o}}(e,a),e===jl&&(Ol=jl=null,Ml=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Ql||(Ql=!0,ju(tt,function(){return Su(),null})),a=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||a){a=Tl.transition,Tl.transition=null;var s=bt;bt=1;var l=Al;Al|=4,El.current=null,function(e,t){if(ei=$t,hr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var i=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch(w){n=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,h=null;t:for(;;){for(var p;f!==n||0!==i&&3!==f.nodeType||(l=s+i),f!==a||0!==r&&3!==f.nodeType||(u=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(p=f.firstChild);)h=f,f=p;for(;;){if(f===e)break t;if(h===n&&++c===i&&(l=s),h===a&&++d===r&&(u=s),null!==(p=f.nextSibling))break;h=(f=h).parentNode}f=p}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ti={focusedElem:e,selectionRange:n},$t=!1,Zs=t;null!==Zs;)if(e=(t=Zs).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Zs=e;else for(;null!==Zs;){t=Zs;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var v=m.memoizedProps,y=m.memoizedState,g=t.stateNode,b=g.getSnapshotBeforeUpdate(t.elementType===t.type?v:ns(t.type,v),y);g.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(o(163))}}catch(w){Pu(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Zs=e;break}Zs=t.return}m=tl,tl=!1}(e,n),vl(n,e),pr(ti),$t=!!ei,ti=ei=null,e.current=n,gl(n,e,i),Ye(),Al=l,bt=s,Tl.transition=a}else e.current=n;if(Ql&&(Ql=!1,Kl=e,Gl=i),a=e.pendingLanes,0===a&&(Wl=null),function(e){if(ot&&"function"===typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(it,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if($l)throw $l=!1,e=ql,ql=null,e;0!==(1&Gl)&&0!==e.tag&&Su(),a=e.pendingLanes,0!==(1&a)?e===Xl?Yl++:(Yl=0,Xl=e):Yl=0,Ui()}(e,t,n,r)}finally{Tl.transition=i,bt=r}return null}function Su(){if(null!==Kl){var e=xt(Gl),t=Tl.transition,n=bt;try{if(Tl.transition=null,bt=16>e?16:e,null===Kl)var r=!1;else{if(e=Kl,Kl=null,Gl=0,0!==(6&Al))throw Error(o(331));var i=Al;for(Al|=4,Zs=e.current;null!==Zs;){var a=Zs,s=a.child;if(0!==(16&Zs.flags)){var l=a.deletions;if(null!==l){for(var u=0;u<l.length;u++){var c=l[u];for(Zs=c;null!==Zs;){var d=Zs;switch(d.tag){case 0:case 11:case 15:nl(8,d,a)}var f=d.child;if(null!==f)f.return=d,Zs=f;else for(;null!==Zs;){var h=(d=Zs).sibling,p=d.return;if(ol(d),d===c){Zs=null;break}if(null!==h){h.return=p,Zs=h;break}Zs=p}}}var m=a.alternate;if(null!==m){var v=m.child;if(null!==v){m.child=null;do{var y=v.sibling;v.sibling=null,v=y}while(null!==v)}}Zs=a}}if(0!==(2064&a.subtreeFlags)&&null!==s)s.return=a,Zs=s;else e:for(;null!==Zs;){if(0!==(2048&(a=Zs).flags))switch(a.tag){case 0:case 11:case 15:nl(9,a,a.return)}var g=a.sibling;if(null!==g){g.return=a.return,Zs=g;break e}Zs=a.return}}var b=e.current;for(Zs=b;null!==Zs;){var x=(s=Zs).child;if(0!==(2064&s.subtreeFlags)&&null!==x)x.return=s,Zs=x;else e:for(s=b;null!==Zs;){if(0!==(2048&(l=Zs).flags))try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(S){Pu(l,l.return,S)}if(l===s){Zs=null;break e}var w=l.sibling;if(null!==w){w.return=l.return,Zs=w;break e}Zs=l.return}}if(Al=i,Ui(),ot&&"function"===typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(it,e)}catch(S){}r=!0}return r}finally{bt=n,Tl.transition=t}}return!1}function ku(e,t,n){e=Vo(e,t=hs(0,t=us(n,t),1),1),t=eu(),null!==e&&(yt(e,1,t),ru(e,t))}function Pu(e,t,n){if(3===e.tag)ku(e,e,n);else for(;null!==t;){if(3===t.tag){ku(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Wl||!Wl.has(r))){t=Vo(t,e=ps(t,e=us(n,e),1),1),e=eu(),null!==t&&(yt(t,1,e),ru(t,e));break}}t=t.return}}function Cu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,jl===e&&(Ml&n)===n&&(4===Dl||3===Dl&&(130023424&Ml)===Ml&&500>Xe()-Bl?fu(e,0):_l|=n),ru(e,t)}function Eu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=Ro(e,t))&&(yt(e,t,n),ru(e,n))}function Tu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Eu(e,n)}function Au(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;null!==i&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),Eu(e,n)}function ju(e,t){return Qe(e,t)}function Ou(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Mu(e,t,n,r){return new Ou(e,t,n,r)}function Lu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Nu(e,t){var n=e.alternate;return null===n?((n=Mu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Du(e,t,n,r,i,a){var s=2;if(r=e,"function"===typeof e)Lu(e)&&(s=1);else if("string"===typeof e)s=5;else e:switch(e){case k:return Ru(n.children,i,a,t);case P:s=8,i|=8;break;case C:return(e=Mu(12,n,t,2|i)).elementType=C,e.lanes=a,e;case j:return(e=Mu(13,n,t,i)).elementType=j,e.lanes=a,e;case O:return(e=Mu(19,n,t,i)).elementType=O,e.lanes=a,e;case N:return zu(n,i,a,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case E:s=10;break e;case T:s=9;break e;case A:s=11;break e;case M:s=14;break e;case L:s=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Mu(s,n,t,i)).elementType=e,t.type=r,t.lanes=a,t}function Ru(e,t,n,r){return(e=Mu(7,e,r,t)).lanes=n,e}function zu(e,t,n,r){return(e=Mu(22,e,r,t)).elementType=N,e.lanes=n,e.stateNode={isHidden:!1},e}function Fu(e,t,n){return(e=Mu(6,e,null,t)).lanes=n,e}function _u(e,t,n){return(t=Mu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Iu(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=vt(0),this.expirationTimes=vt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vt(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Vu(e,t,n,r,i,o,a,s,l){return e=new Iu(e,t,n,s,l),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Mu(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fo(o),e}function Bu(e){if(!e)return Ti;e:{if(Ue(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Li(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(Li(n))return Ri(e,n,t)}return t}function Uu(e,t,n,r,i,o,a,s,l){return(e=Vu(n,r,!0,e,0,o,0,s,l)).context=Bu(null),n=e.current,(o=Io(r=eu(),i=tu(n))).callback=void 0!==t&&null!==t?t:null,Vo(n,o,i),e.current.lanes=i,yt(e,i,r),ru(e,r),e}function Hu(e,t,n,r){var i=t.current,o=eu(),a=tu(i);return n=Bu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Io(o,a)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Vo(i,t,a))&&(nu(e,i,a,o),Bo(e,i,a)),a}function $u(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function qu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Wu(e,t){qu(e,t),(e=e.alternate)&&qu(e,t)}kl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||ji.current)bs=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bs=!1,function(e,t,n){switch(t.tag){case 3:js(t),po();break;case 5:Zo(t);break;case 1:Li(t.type)&&zi(t);break;case 4:Yo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;Ei(ko,r._currentValue),r._currentValue=i;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ei(ea,1&ea.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Fs(e,t,n):(Ei(ea,1&ea.current),null!==(e=$s(e,t,n))?e.sibling:null);Ei(ea,1&ea.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Us(e,t,n);t.flags|=128}if(null!==(i=t.memoizedState)&&(i.rendering=null,i.tail=null,i.lastEffect=null),Ei(ea,ea.current),r)break;return null;case 22:case 23:return t.lanes=0,Ps(e,t,n)}return $s(e,t,n)}(e,t,n);bs=0!==(131072&e.flags)}else bs=!1,io&&0!==(1048576&t.flags)&&Ji(t,Wi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Hs(e,t),e=t.pendingProps;var i=Mi(t,Ai.current);Oo(t,n),i=va(null,t,r,e,i,n);var a=ya();return t.flags|=1,"object"===typeof i&&null!==i&&"function"===typeof i.render&&void 0===i.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Li(r)?(a=!0,zi(t)):a=!1,t.memoizedState=null!==i.state&&void 0!==i.state?i.state:null,Fo(t),i.updater=is,t.stateNode=i,i._reactInternals=t,ls(t,r,e,n),t=As(null,t,r,!0,a,n)):(t.tag=0,io&&a&&eo(t),xs(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Hs(e,t),e=t.pendingProps,r=(i=r._init)(r._payload),t.type=r,i=t.tag=function(e){if("function"===typeof e)return Lu(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===A)return 11;if(e===M)return 14}return 2}(r),e=ns(r,e),i){case 0:t=Es(null,t,r,e,n);break e;case 1:t=Ts(null,t,r,e,n);break e;case 11:t=ws(null,t,r,e,n);break e;case 14:t=Ss(null,t,r,ns(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,Es(e,t,r,i=t.elementType===r?i:ns(r,i),n);case 1:return r=t.type,i=t.pendingProps,Ts(e,t,r,i=t.elementType===r?i:ns(r,i),n);case 3:e:{if(js(t),null===e)throw Error(o(387));r=t.pendingProps,i=(a=t.memoizedState).element,_o(e,t),Ho(t,r,null,n);var s=t.memoizedState;if(r=s.element,a.isDehydrated){if(a={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=Os(e,t,r,n,i=us(Error(o(423)),t));break e}if(r!==i){t=Os(e,t,r,n,i=us(Error(o(424)),t));break e}for(ro=ui(t.stateNode.containerInfo.firstChild),no=t,io=!0,oo=null,n=So(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(po(),r===i){t=$s(e,t,n);break e}xs(e,t,r,n)}t=t.child}return t;case 5:return Zo(t),null===e&&uo(t),r=t.type,i=t.pendingProps,a=null!==e?e.memoizedProps:null,s=i.children,ni(r,i)?s=null:null!==a&&ni(r,a)&&(t.flags|=32),Cs(e,t),xs(e,t,s,n),t.child;case 6:return null===e&&uo(t),null;case 13:return Fs(e,t,n);case 4:return Yo(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=wo(t,null,r,n):xs(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,ws(e,t,r,i=t.elementType===r?i:ns(r,i),n);case 7:return xs(e,t,t.pendingProps,n),t.child;case 8:case 12:return xs(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,a=t.memoizedProps,s=i.value,Ei(ko,r._currentValue),r._currentValue=s,null!==a)if(sr(a.value,s)){if(a.children===i.children&&!ji.current){t=$s(e,t,n);break e}}else for(null!==(a=t.child)&&(a.return=t);null!==a;){var l=a.dependencies;if(null!==l){s=a.child;for(var u=l.firstContext;null!==u;){if(u.context===r){if(1===a.tag){(u=Io(-1,n&-n)).tag=2;var c=a.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}a.lanes|=n,null!==(u=a.alternate)&&(u.lanes|=n),jo(a.return,n,t),l.lanes|=n;break}u=u.next}}else if(10===a.tag)s=a.type===t.type?null:a.child;else if(18===a.tag){if(null===(s=a.return))throw Error(o(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),jo(s,n,t),s=a.sibling}else s=a.child;if(null!==s)s.return=a;else for(s=a;null!==s;){if(s===t){s=null;break}if(null!==(a=s.sibling)){a.return=s.return,s=a;break}s=s.return}a=s}xs(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Oo(t,n),r=r(i=Mo(i)),t.flags|=1,xs(e,t,r,n),t.child;case 14:return i=ns(r=t.type,t.pendingProps),Ss(e,t,r,i=ns(r.type,i),n);case 15:return ks(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:ns(r,i),Hs(e,t),t.tag=1,Li(r)?(e=!0,zi(t)):e=!1,Oo(t,n),as(t,r,i),ls(t,r,i,n),As(null,t,r,!0,e,n);case 19:return Us(e,t,n);case 22:return Ps(e,t,n)}throw Error(o(156,t.tag))};var Qu="function"===typeof reportError?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}function Gu(e){this._internalRoot=e}function Yu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zu(){}function Ju(e,t,n,r,i){var o=n._reactRootContainer;if(o){var a=o;if("function"===typeof i){var s=i;i=function(){var e=$u(a);s.call(e)}}Hu(t,a,e,i)}else a=function(e,t,n,r,i){if(i){if("function"===typeof r){var o=r;r=function(){var e=$u(a);o.call(e)}}var a=Uu(t,r,e,0,null,!1,0,"",Zu);return e._reactRootContainer=a,e[pi]=a.current,Ur(8===e.nodeType?e.parentNode:e),cu(),a}for(;i=e.lastChild;)e.removeChild(i);if("function"===typeof r){var s=r;r=function(){var e=$u(l);s.call(e)}}var l=Vu(e,0,!1,null,0,!1,0,"",Zu);return e._reactRootContainer=l,e[pi]=l.current,Ur(8===e.nodeType?e.parentNode:e),cu(function(){Hu(t,l,n,r)}),l}(n,t,e,i,r);return $u(a)}Gu.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Hu(e,t,null,null)},Gu.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu(function(){Hu(null,e,null,null)}),t[pi]=null}},Gu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Pt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Nt.length&&0!==t&&t<Nt[n].priority;n++);Nt.splice(n,0,e),0===n&&Ft(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(gt(t,1|n),ru(t,Xe()),0===(6&Al)&&(Ul=Xe()+500,Ui()))}break;case 13:cu(function(){var t=Ro(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}}),Wu(e,1)}},St=function(e){if(13===e.tag){var t=Ro(e,134217728);if(null!==t)nu(t,e,134217728,eu());Wu(e,134217728)}},kt=function(e){if(13===e.tag){var t=tu(e),n=Ro(e,t);if(null!==n)nu(n,e,t,eu());Wu(e,t)}},Pt=function(){return bt},Ct=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(Z(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=wi(r);if(!i)throw Error(o(90));Q(r),Z(r,i)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Ae=uu,je=cu;var ec={usingClientEntryPoint:!1,Events:[bi,xi,wi,Ee,Te,uu]},tc={findFiberByHostInstance:gi,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=qe(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{it=rc.inject(nc),ot=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Yu(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Yu(e))throw Error(o(299));var n=!1,r="",i=Qu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(i=t.onRecoverableError)),t=Vu(e,1,!1,null,0,n,0,r,i),e[pi]=t.current,Ur(8===e.nodeType?e.parentNode:e),new Ku(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=qe(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Xu(t))throw Error(o(200));return Ju(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Yu(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,i=!1,a="",s=Qu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(i=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=Uu(t,null,e,1,null!=n?n:null,i,0,a,s),e[pi]=t.current,Ur(e),r)for(e=0;e<r.length;e++)i=(i=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Gu(t)},t.render=function(e,t,n){if(!Xu(t))throw Error(o(200));return Ju(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Xu(e))throw Error(o(40));return!!e._reactRootContainer&&(cu(function(){Ju(null,null,e,!1,function(){e._reactRootContainer=null,e[pi]=null})}),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xu(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return Ju(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},2740:e=>{"use strict";e.exports=function(e,t,n,r,i,o,a,s){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[n,r,i,o,a,s],c=0;(l=new Error(t.replace(/%s/g,function(){return u[c++]}))).name="Invariant Violation"}throw l.framesToPop=1,l}}},2907:(e,t,n)=>{"use strict";n.d(t,{QueryClient:()=>r.QueryClient,QueryClientProvider:()=>i.QueryClientProvider});var r=n(5819);n.o(r,"QueryClientProvider")&&n.d(t,{QueryClientProvider:function(){return r.QueryClientProvider}});var i=n(6524)},3218:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},4089:e=>{e.exports={ReactQueryDevtools:function(){return null},ReactQueryDevtoolsPanel:function(){return null}}},4198:(e,t,n)=>{"use strict";n.d(t,{E:()=>k});var r=n(8168),i=n(8870);function o(e,t){return o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},o(e,t)}function a(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,o(e,t)}var s=n(1991),l=n(75),u=function(){function e(){this.listeners=[]}var t=e.prototype;return t.subscribe=function(e){var t=this,n=e||function(){};return this.listeners.push(n),this.onSubscribe(),function(){t.listeners=t.listeners.filter(function(e){return e!==n}),t.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},e}(),c=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!i.S$&&(null==(t=window)?void 0:t.addEventListener)){var n=function(){return e()};return window.addEventListener("visibilitychange",n,!1),window.addEventListener("focus",n,!1),function(){window.removeEventListener("visibilitychange",n),window.removeEventListener("focus",n)}}},t}a(t,e);var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},n.setEventListener=function(e){var t,n=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e(function(e){"boolean"===typeof e?n.setFocused(e):n.onFocus()})},n.setFocused=function(e){this.focused=e,e&&this.onFocus()},n.onFocus=function(){this.listeners.forEach(function(e){e()})},n.isFocused=function(){return"boolean"===typeof this.focused?this.focused:"undefined"===typeof document||[void 0,"visible","prerender"].includes(document.visibilityState)},t}(u)),d=new(function(e){function t(){var t;return(t=e.call(this)||this).setup=function(e){var t;if(!i.S$&&(null==(t=window)?void 0:t.addEventListener)){var n=function(){return e()};return window.addEventListener("online",n,!1),window.addEventListener("offline",n,!1),function(){window.removeEventListener("online",n),window.removeEventListener("offline",n)}}},t}a(t,e);var n=t.prototype;return n.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},n.onUnsubscribe=function(){var e;this.hasListeners()||(null==(e=this.cleanup)||e.call(this),this.cleanup=void 0)},n.setEventListener=function(e){var t,n=this;this.setup=e,null==(t=this.cleanup)||t.call(this),this.cleanup=e(function(e){"boolean"===typeof e?n.setOnline(e):n.onOnline()})},n.setOnline=function(e){this.online=e,e&&this.onOnline()},n.onOnline=function(){this.listeners.forEach(function(e){e()})},n.isOnline=function(){return"boolean"===typeof this.online?this.online:"undefined"===typeof navigator||"undefined"===typeof navigator.onLine||navigator.onLine},t}(u));function f(e){return Math.min(1e3*Math.pow(2,e),3e4)}function h(e){return"function"===typeof(null==e?void 0:e.cancel)}var p=function(e){this.revert=null==e?void 0:e.revert,this.silent=null==e?void 0:e.silent};function m(e){return e instanceof p}var v=function(e){var t,n,r,o,a=this,s=!1;this.abort=e.abort,this.cancel=function(e){return null==t?void 0:t(e)},this.cancelRetry=function(){s=!0},this.continueRetry=function(){s=!1},this.continue=function(){return null==n?void 0:n()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(e,t){r=e,o=t});var l=function(t){a.isResolved||(a.isResolved=!0,null==e.onSuccess||e.onSuccess(t),null==n||n(),r(t))},u=function(t){a.isResolved||(a.isResolved=!0,null==e.onError||e.onError(t),null==n||n(),o(t))};!function r(){if(!a.isResolved){var o;try{o=e.fn()}catch(m){o=Promise.reject(m)}t=function(e){if(!a.isResolved&&(u(new p(e)),null==a.abort||a.abort(),h(o)))try{o.cancel()}catch(t){}},a.isTransportCancelable=h(o),Promise.resolve(o).then(l).catch(function(t){var o,l;if(!a.isResolved){var h=null!=(o=e.retry)?o:3,p=null!=(l=e.retryDelay)?l:f,m="function"===typeof p?p(a.failureCount,t):p,v=!0===h||"number"===typeof h&&a.failureCount<h||"function"===typeof h&&h(a.failureCount,t);!s&&v?(a.failureCount++,null==e.onFail||e.onFail(a.failureCount,t),(0,i.yy)(m).then(function(){if(!c.isFocused()||!d.isOnline())return new Promise(function(t){n=t,a.isPaused=!0,null==e.onPause||e.onPause()}).then(function(){n=void 0,a.isPaused=!1,null==e.onContinue||e.onContinue()})}).then(function(){s?u(t):r()})):u(t)}})}}()},y=function(){function e(e){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.cache=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.initialState=e.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=e.meta,this.scheduleGc()}var t=e.prototype;return t.setOptions=function(e){var t;this.options=(0,r.A)({},this.defaultOptions,e),this.meta=null==e?void 0:e.meta,this.cacheTime=Math.max(this.cacheTime||0,null!=(t=this.options.cacheTime)?t:3e5)},t.setDefaultOptions=function(e){this.defaultOptions=e},t.scheduleGc=function(){var e=this;this.clearGcTimeout(),(0,i.gn)(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){e.optionalRemove()},this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(e,t){var n,r,o=this.state.data,a=(0,i.Zw)(e,o);return(null==(n=(r=this.options).isDataEqual)?void 0:n.call(r,o,a))?a=o:!1!==this.options.structuralSharing&&(a=(0,i.BH)(o,a)),this.dispatch({data:a,type:"success",dataUpdatedAt:null==t?void 0:t.updatedAt}),a},t.setState=function(e,t){this.dispatch({type:"setState",state:e,setStateOptions:t})},t.cancel=function(e){var t,n=this.promise;return null==(t=this.retryer)||t.cancel(e),n?n.then(i.lQ).catch(i.lQ):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some(function(e){return!1!==e.options.enabled})},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(e){return e.getCurrentResult().isStale})},t.isStaleByTime=function(e){return void 0===e&&(e=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!(0,i.j3)(this.state.dataUpdatedAt,e)},t.onFocus=function(){var e,t=this.observers.find(function(e){return e.shouldFetchOnWindowFocus()});t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.onOnline=function(){var e,t=this.observers.find(function(e){return e.shouldFetchOnReconnect()});t&&t.refetch(),null==(e=this.retryer)||e.continue()},t.addObserver=function(e){-1===this.observers.indexOf(e)&&(this.observers.push(e),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:e}))},t.removeObserver=function(e){-1!==this.observers.indexOf(e)&&(this.observers=this.observers.filter(function(t){return t!==e}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:e}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(e,t){var n,r,o,a=this;if(this.state.isFetching)if(this.state.dataUpdatedAt&&(null==t?void 0:t.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var s;return null==(s=this.retryer)||s.continueRetry(),this.promise}if(e&&this.setOptions(e),!this.options.queryFn){var u=this.observers.find(function(e){return e.options.queryFn});u&&this.setOptions(u.options)}var c=(0,i.HN)(this.queryKey),d=(0,i.jY)(),f={queryKey:c,pageParam:void 0,meta:this.meta};Object.defineProperty(f,"signal",{enumerable:!0,get:function(){if(d)return a.abortSignalConsumed=!0,d.signal}});var h,p,y={fetchOptions:t,options:this.options,queryKey:c,state:this.state,fetchFn:function(){return a.options.queryFn?(a.abortSignalConsumed=!1,a.options.queryFn(f)):Promise.reject("Missing queryFn")},meta:this.meta};(null==(n=this.options.behavior)?void 0:n.onFetch)&&(null==(h=this.options.behavior)||h.onFetch(y));(this.revertState=this.state,this.state.isFetching&&this.state.fetchMeta===(null==(r=y.fetchOptions)?void 0:r.meta))||this.dispatch({type:"fetch",meta:null==(p=y.fetchOptions)?void 0:p.meta});return this.retryer=new v({fn:y.fetchFn,abort:null==d||null==(o=d.abort)?void 0:o.bind(d),onSuccess:function(e){a.setData(e),null==a.cache.config.onSuccess||a.cache.config.onSuccess(e,a),0===a.cacheTime&&a.optionalRemove()},onError:function(e){m(e)&&e.silent||a.dispatch({type:"error",error:e}),m(e)||(null==a.cache.config.onError||a.cache.config.onError(e,a),(0,l.t)().error(e)),0===a.cacheTime&&a.optionalRemove()},onFail:function(){a.dispatch({type:"failed"})},onPause:function(){a.dispatch({type:"pause"})},onContinue:function(){a.dispatch({type:"continue"})},retry:y.options.retry,retryDelay:y.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(e){var t=this;this.state=this.reducer(this.state,e),s.j.batch(function(){t.observers.forEach(function(t){t.onQueryUpdate(e)}),t.cache.notify({query:t,type:"queryUpdated",action:e})})},t.getDefaultState=function(e){var t="function"===typeof e.initialData?e.initialData():e.initialData,n="undefined"!==typeof e.initialData?"function"===typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0,r="undefined"!==typeof t;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?null!=n?n:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:r?"success":"idle"}},t.reducer=function(e,t){var n,i;switch(t.type){case"failed":return(0,r.A)({},e,{fetchFailureCount:e.fetchFailureCount+1});case"pause":return(0,r.A)({},e,{isPaused:!0});case"continue":return(0,r.A)({},e,{isPaused:!1});case"fetch":return(0,r.A)({},e,{fetchFailureCount:0,fetchMeta:null!=(n=t.meta)?n:null,isFetching:!0,isPaused:!1},!e.dataUpdatedAt&&{error:null,status:"loading"});case"success":return(0,r.A)({},e,{data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:null!=(i=t.dataUpdatedAt)?i:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var o=t.error;return m(o)&&o.revert&&this.revertState?(0,r.A)({},this.revertState):(0,r.A)({},e,{error:o,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return(0,r.A)({},e,{isInvalidated:!0});case"setState":return(0,r.A)({},e,t.state);default:return e}},e}(),g=function(e){function t(t){var n;return(n=e.call(this)||this).config=t||{},n.queries=[],n.queriesMap={},n}a(t,e);var n=t.prototype;return n.build=function(e,t,n){var r,o=t.queryKey,a=null!=(r=t.queryHash)?r:(0,i.F$)(o,t),s=this.get(a);return s||(s=new y({cache:this,queryKey:o,queryHash:a,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(o),meta:t.meta}),this.add(s)),s},n.add=function(e){this.queriesMap[e.queryHash]||(this.queriesMap[e.queryHash]=e,this.queries.push(e),this.notify({type:"queryAdded",query:e}))},n.remove=function(e){var t=this.queriesMap[e.queryHash];t&&(e.destroy(),this.queries=this.queries.filter(function(t){return t!==e}),t===e&&delete this.queriesMap[e.queryHash],this.notify({type:"queryRemoved",query:e}))},n.clear=function(){var e=this;s.j.batch(function(){e.queries.forEach(function(t){e.remove(t)})})},n.get=function(e){return this.queriesMap[e]},n.getAll=function(){return this.queries},n.find=function(e,t){var n=(0,i.b_)(e,t)[0];return"undefined"===typeof n.exact&&(n.exact=!0),this.queries.find(function(e){return(0,i.MK)(n,e)})},n.findAll=function(e,t){var n=(0,i.b_)(e,t)[0];return Object.keys(n).length>0?this.queries.filter(function(e){return(0,i.MK)(n,e)}):this.queries},n.notify=function(e){var t=this;s.j.batch(function(){t.listeners.forEach(function(t){t(e)})})},n.onFocus=function(){var e=this;s.j.batch(function(){e.queries.forEach(function(e){e.onFocus()})})},n.onOnline=function(){var e=this;s.j.batch(function(){e.queries.forEach(function(e){e.onOnline()})})},t}(u),b=function(){function e(e){this.options=(0,r.A)({},e.defaultOptions,e.options),this.mutationId=e.mutationId,this.mutationCache=e.mutationCache,this.observers=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0},this.meta=e.meta}var t=e.prototype;return t.setState=function(e){this.dispatch({type:"setState",state:e})},t.addObserver=function(e){-1===this.observers.indexOf(e)&&this.observers.push(e)},t.removeObserver=function(e){this.observers=this.observers.filter(function(t){return t!==e})},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(i.lQ).catch(i.lQ)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var e,t=this,n="loading"===this.state.status,r=Promise.resolve();return n||(this.dispatch({type:"loading",variables:this.options.variables}),r=r.then(function(){null==t.mutationCache.config.onMutate||t.mutationCache.config.onMutate(t.state.variables,t)}).then(function(){return null==t.options.onMutate?void 0:t.options.onMutate(t.state.variables)}).then(function(e){e!==t.state.context&&t.dispatch({type:"loading",context:e,variables:t.state.variables})})),r.then(function(){return t.executeMutation()}).then(function(n){e=n,null==t.mutationCache.config.onSuccess||t.mutationCache.config.onSuccess(e,t.state.variables,t.state.context,t)}).then(function(){return null==t.options.onSuccess?void 0:t.options.onSuccess(e,t.state.variables,t.state.context)}).then(function(){return null==t.options.onSettled?void 0:t.options.onSettled(e,null,t.state.variables,t.state.context)}).then(function(){return t.dispatch({type:"success",data:e}),e}).catch(function(e){return null==t.mutationCache.config.onError||t.mutationCache.config.onError(e,t.state.variables,t.state.context,t),(0,l.t)().error(e),Promise.resolve().then(function(){return null==t.options.onError?void 0:t.options.onError(e,t.state.variables,t.state.context)}).then(function(){return null==t.options.onSettled?void 0:t.options.onSettled(void 0,e,t.state.variables,t.state.context)}).then(function(){throw t.dispatch({type:"error",error:e}),e})})},t.executeMutation=function(){var e,t=this;return this.retryer=new v({fn:function(){return t.options.mutationFn?t.options.mutationFn(t.state.variables):Promise.reject("No mutationFn found")},onFail:function(){t.dispatch({type:"failed"})},onPause:function(){t.dispatch({type:"pause"})},onContinue:function(){t.dispatch({type:"continue"})},retry:null!=(e=this.options.retry)?e:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(e){var t=this;this.state=function(e,t){switch(t.type){case"failed":return(0,r.A)({},e,{failureCount:e.failureCount+1});case"pause":return(0,r.A)({},e,{isPaused:!0});case"continue":return(0,r.A)({},e,{isPaused:!1});case"loading":return(0,r.A)({},e,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return(0,r.A)({},e,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return(0,r.A)({},e,{data:void 0,error:t.error,failureCount:e.failureCount+1,isPaused:!1,status:"error"});case"setState":return(0,r.A)({},e,t.state);default:return e}}(this.state,e),s.j.batch(function(){t.observers.forEach(function(t){t.onMutationUpdate(e)}),t.mutationCache.notify(t)})},e}();var x=function(e){function t(t){var n;return(n=e.call(this)||this).config=t||{},n.mutations=[],n.mutationId=0,n}a(t,e);var n=t.prototype;return n.build=function(e,t,n){var r=new b({mutationCache:this,mutationId:++this.mutationId,options:e.defaultMutationOptions(t),state:n,defaultOptions:t.mutationKey?e.getMutationDefaults(t.mutationKey):void 0,meta:t.meta});return this.add(r),r},n.add=function(e){this.mutations.push(e),this.notify(e)},n.remove=function(e){this.mutations=this.mutations.filter(function(t){return t!==e}),e.cancel(),this.notify(e)},n.clear=function(){var e=this;s.j.batch(function(){e.mutations.forEach(function(t){e.remove(t)})})},n.getAll=function(){return this.mutations},n.find=function(e){return"undefined"===typeof e.exact&&(e.exact=!0),this.mutations.find(function(t){return(0,i.nJ)(e,t)})},n.findAll=function(e){return this.mutations.filter(function(t){return(0,i.nJ)(e,t)})},n.notify=function(e){var t=this;s.j.batch(function(){t.listeners.forEach(function(t){t(e)})})},n.onFocus=function(){this.resumePausedMutations()},n.onOnline=function(){this.resumePausedMutations()},n.resumePausedMutations=function(){var e=this.mutations.filter(function(e){return e.state.isPaused});return s.j.batch(function(){return e.reduce(function(e,t){return e.then(function(){return t.continue().catch(i.lQ)})},Promise.resolve())})},t}(u);function w(e,t){return null==e.getNextPageParam?void 0:e.getNextPageParam(t[t.length-1],t)}function S(e,t){return null==e.getPreviousPageParam?void 0:e.getPreviousPageParam(t[0],t)}var k=function(){function e(e){void 0===e&&(e={}),this.queryCache=e.queryCache||new g,this.mutationCache=e.mutationCache||new x,this.defaultOptions=e.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=e.prototype;return t.mount=function(){var e=this;this.unsubscribeFocus=c.subscribe(function(){c.isFocused()&&d.isOnline()&&(e.mutationCache.onFocus(),e.queryCache.onFocus())}),this.unsubscribeOnline=d.subscribe(function(){c.isFocused()&&d.isOnline()&&(e.mutationCache.onOnline(),e.queryCache.onOnline())})},t.unmount=function(){var e,t;null==(e=this.unsubscribeFocus)||e.call(this),null==(t=this.unsubscribeOnline)||t.call(this)},t.isFetching=function(e,t){var n=(0,i.b_)(e,t)[0];return n.fetching=!0,this.queryCache.findAll(n).length},t.isMutating=function(e){return this.mutationCache.findAll((0,r.A)({},e,{fetching:!0})).length},t.getQueryData=function(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state.data},t.getQueriesData=function(e){return this.getQueryCache().findAll(e).map(function(e){return[e.queryKey,e.state.data]})},t.setQueryData=function(e,t,n){var r=(0,i.vh)(e),o=this.defaultQueryOptions(r);return this.queryCache.build(this,o).setData(t,n)},t.setQueriesData=function(e,t,n){var r=this;return s.j.batch(function(){return r.getQueryCache().findAll(e).map(function(e){var i=e.queryKey;return[i,r.setQueryData(i,t,n)]})})},t.getQueryState=function(e,t){var n;return null==(n=this.queryCache.find(e,t))?void 0:n.state},t.removeQueries=function(e,t){var n=(0,i.b_)(e,t)[0],r=this.queryCache;s.j.batch(function(){r.findAll(n).forEach(function(e){r.remove(e)})})},t.resetQueries=function(e,t,n){var o=this,a=(0,i.b_)(e,t,n),l=a[0],u=a[1],c=this.queryCache,d=(0,r.A)({},l,{active:!0});return s.j.batch(function(){return c.findAll(l).forEach(function(e){e.reset()}),o.refetchQueries(d,u)})},t.cancelQueries=function(e,t,n){var r=this,o=(0,i.b_)(e,t,n),a=o[0],l=o[1],u=void 0===l?{}:l;"undefined"===typeof u.revert&&(u.revert=!0);var c=s.j.batch(function(){return r.queryCache.findAll(a).map(function(e){return e.cancel(u)})});return Promise.all(c).then(i.lQ).catch(i.lQ)},t.invalidateQueries=function(e,t,n){var o,a,l,u=this,c=(0,i.b_)(e,t,n),d=c[0],f=c[1],h=(0,r.A)({},d,{active:null==(o=null!=(a=d.refetchActive)?a:d.active)||o,inactive:null!=(l=d.refetchInactive)&&l});return s.j.batch(function(){return u.queryCache.findAll(d).forEach(function(e){e.invalidate()}),u.refetchQueries(h,f)})},t.refetchQueries=function(e,t,n){var o=this,a=(0,i.b_)(e,t,n),l=a[0],u=a[1],c=s.j.batch(function(){return o.queryCache.findAll(l).map(function(e){return e.fetch(void 0,(0,r.A)({},u,{meta:{refetchPage:null==l?void 0:l.refetchPage}}))})}),d=Promise.all(c).then(i.lQ);return(null==u?void 0:u.throwOnError)||(d=d.catch(i.lQ)),d},t.fetchQuery=function(e,t,n){var r=(0,i.vh)(e,t,n),o=this.defaultQueryOptions(r);"undefined"===typeof o.retry&&(o.retry=!1);var a=this.queryCache.build(this,o);return a.isStaleByTime(o.staleTime)?a.fetch(o):Promise.resolve(a.state.data)},t.prefetchQuery=function(e,t,n){return this.fetchQuery(e,t,n).then(i.lQ).catch(i.lQ)},t.fetchInfiniteQuery=function(e,t,n){var r=(0,i.vh)(e,t,n);return r.behavior={onFetch:function(e){e.fetchFn=function(){var t,n,r,o,a,s,l,u=null==(t=e.fetchOptions)||null==(n=t.meta)?void 0:n.refetchPage,c=null==(r=e.fetchOptions)||null==(o=r.meta)?void 0:o.fetchMore,d=null==c?void 0:c.pageParam,f="forward"===(null==c?void 0:c.direction),p="backward"===(null==c?void 0:c.direction),m=(null==(a=e.state.data)?void 0:a.pages)||[],v=(null==(s=e.state.data)?void 0:s.pageParams)||[],y=(0,i.jY)(),g=null==y?void 0:y.signal,b=v,x=!1,k=e.options.queryFn||function(){return Promise.reject("Missing queryFn")},P=function(e,t,n,r){return b=r?[t].concat(b):[].concat(b,[t]),r?[n].concat(e):[].concat(e,[n])},C=function(t,n,r,i){if(x)return Promise.reject("Cancelled");if("undefined"===typeof r&&!n&&t.length)return Promise.resolve(t);var o={queryKey:e.queryKey,signal:g,pageParam:r,meta:e.meta},a=k(o),s=Promise.resolve(a).then(function(e){return P(t,r,e,i)});return h(a)&&(s.cancel=a.cancel),s};if(m.length)if(f){var E="undefined"!==typeof d,T=E?d:w(e.options,m);l=C(m,E,T)}else if(p){var A="undefined"!==typeof d,j=A?d:S(e.options,m);l=C(m,A,j,!0)}else!function(){b=[];var t="undefined"===typeof e.options.getNextPageParam,n=!u||!m[0]||u(m[0],0,m);l=n?C([],t,v[0]):Promise.resolve(P([],v[0],m[0]));for(var r=function(n){l=l.then(function(r){if(!u||!m[n]||u(m[n],n,m)){var i=t?v[n]:w(e.options,r);return C(r,t,i)}return Promise.resolve(P(r,v[n],m[n]))})},i=1;i<m.length;i++)r(i)}();else l=C([]);var O=l.then(function(e){return{pages:e,pageParams:b}});return O.cancel=function(){x=!0,null==y||y.abort(),h(l)&&l.cancel()},O}}},this.fetchQuery(r)},t.prefetchInfiniteQuery=function(e,t,n){return this.fetchInfiniteQuery(e,t,n).then(i.lQ).catch(i.lQ)},t.cancelMutations=function(){var e=this,t=s.j.batch(function(){return e.mutationCache.getAll().map(function(e){return e.cancel()})});return Promise.all(t).then(i.lQ).catch(i.lQ)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(e){return this.mutationCache.build(this,e).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(e){this.defaultOptions=e},t.setQueryDefaults=function(e,t){var n=this.queryDefaults.find(function(t){return(0,i.Od)(e)===(0,i.Od)(t.queryKey)});n?n.defaultOptions=t:this.queryDefaults.push({queryKey:e,defaultOptions:t})},t.getQueryDefaults=function(e){var t;return e?null==(t=this.queryDefaults.find(function(t){return(0,i.Cp)(e,t.queryKey)}))?void 0:t.defaultOptions:void 0},t.setMutationDefaults=function(e,t){var n=this.mutationDefaults.find(function(t){return(0,i.Od)(e)===(0,i.Od)(t.mutationKey)});n?n.defaultOptions=t:this.mutationDefaults.push({mutationKey:e,defaultOptions:t})},t.getMutationDefaults=function(e){var t;return e?null==(t=this.mutationDefaults.find(function(t){return(0,i.Cp)(e,t.mutationKey)}))?void 0:t.defaultOptions:void 0},t.defaultQueryOptions=function(e){if(null==e?void 0:e._defaulted)return e;var t=(0,r.A)({},this.defaultOptions.queries,this.getQueryDefaults(null==e?void 0:e.queryKey),e,{_defaulted:!0});return!t.queryHash&&t.queryKey&&(t.queryHash=(0,i.F$)(t.queryKey,t)),t},t.defaultQueryObserverOptions=function(e){return this.defaultQueryOptions(e)},t.defaultMutationOptions=function(e){return(null==e?void 0:e._defaulted)?e:(0,r.A)({},this.defaultOptions.mutations,this.getMutationDefaults(null==e?void 0:e.mutationKey),e,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},e}()},4202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),h=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,v={};function y(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||p}function g(){}function b(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||p}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},g.prototype=y.prototype;var x=b.prototype=new g;x.constructor=b,m(x,y.prototype),x.isPureReactComponent=!0;var w=Array.isArray,S=Object.prototype.hasOwnProperty,k={current:null},P={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var i,o={},a=null,s=null;if(null!=t)for(i in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)S.call(t,i)&&!P.hasOwnProperty(i)&&(o[i]=t[i]);var l=arguments.length-2;if(1===l)o.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(i in l=e.defaultProps)void 0===o[i]&&(o[i]=l[i]);return{$$typeof:n,type:e,key:a,ref:s,props:o,_owner:k.current}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var T=/\/+/g;function A(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function j(e,t,i,o,a){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return a=a(l=e),e=""===o?"."+A(l,0):o,w(a)?(i="",null!=e&&(i=e.replace(T,"$&/")+"/"),j(a,t,i,"",function(e){return e})):null!=a&&(E(a)&&(a=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,i+(!a.key||l&&l.key===a.key?"":(""+a.key).replace(T,"$&/")+"/")+e)),t.push(a)),1;if(l=0,o=""===o?".":o+":",w(e))for(var u=0;u<e.length;u++){var c=o+A(s=e[u],u);l+=j(s,t,i,c,a)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=h&&e[h]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(s=e.next()).done;)l+=j(s=s.value,t,i,c=o+A(s,u++),a);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function O(e,t,n){if(null==e)return e;var r=[],i=0;return j(e,r,"","",function(e){return t.call(n,e,i++)}),r}function M(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L={current:null},N={transition:null},D={ReactCurrentDispatcher:L,ReactCurrentBatchConfig:N,ReactCurrentOwner:k};function R(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:O,forEach:function(e,t,n){O(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return O(e,function(){t++}),t},toArray:function(e){return O(e,function(e){return e})||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=i,t.Profiler=a,t.PureComponent=b,t.StrictMode=o,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=D,t.act=R,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=m({},e.props),o=e.key,a=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(a=t.ref,s=k.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)S.call(t,u)&&!P.hasOwnProperty(u)&&(i[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)i.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];i.children=l}return{$$typeof:n,type:e.type,key:o,ref:a,props:i,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:M}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=N.transition;N.transition={};try{e()}finally{N.transition=t}},t.unstable_act=R,t.useCallback=function(e,t){return L.current.useCallback(e,t)},t.useContext=function(e){return L.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return L.current.useDeferredValue(e)},t.useEffect=function(e,t){return L.current.useEffect(e,t)},t.useId=function(){return L.current.useId()},t.useImperativeHandle=function(e,t,n){return L.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return L.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return L.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return L.current.useMemo(e,t)},t.useReducer=function(e,t,n){return L.current.useReducer(e,t,n)},t.useRef=function(e){return L.current.useRef(e)},t.useState=function(e){return L.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return L.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return L.current.useTransition()},t.version="18.3.1"},4298:()=>{},4391:(e,t,n)=>{"use strict";var r=n(7950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},4548:(e,t,n)=>{"use strict";n.d(t,{vt:()=>f});const r=e=>{let t;const n=new Set,r=(e,r)=>{const i="function"===typeof e?e(t):e;if(!Object.is(i,t)){const e=t;t=(null!=r?r:"object"!==typeof i||null===i)?i:Object.assign({},t,i),n.forEach(n=>n(t,e))}},i=()=>t,o={setState:r,getState:i,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e)),destroy:()=>{console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),n.clear()}},a=t=e(r,i,o);return o},i=e=>e?r(e):r;var o=n(5043),a=n(8443);const{useDebugValue:s}=o,{useSyncExternalStoreWithSelector:l}=a;let u=!1;const c=e=>e;const d=e=>{"function"!==typeof e&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t="function"===typeof e?i(e):e,n=(e,n)=>function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:c,n=arguments.length>2?arguments[2]:void 0;n&&!u&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),u=!0);const r=l(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return s(r),r}(t,e,n);return Object.assign(n,t),n},f=e=>e?d(e):d},4735:(e,t,n)=>{"use strict";n.d(t,{B:()=>r});const r="undefined"!==typeof document},4930:(e,t,n)=>{"use strict";n.d(t,{M:()=>i});var r=n(5043);function i(e){const t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},5043:(e,t,n)=>{"use strict";e.exports=n(4202)},5173:(e,t,n)=>{e.exports=n(1497)()},5819:(e,t,n)=>{"use strict";n.d(t,{QueryClient:()=>r.E});var r=n(4198),i=n(4298);n.o(i,"QueryClientProvider")&&n.d(t,{QueryClientProvider:function(){return i.QueryClientProvider}})},6366:e=>{var t="undefined"!==typeof Element,n="function"===typeof Map,r="function"===typeof Set,i="function"===typeof ArrayBuffer&&!!ArrayBuffer.isView;function o(e,a){if(e===a)return!0;if(e&&a&&"object"==typeof e&&"object"==typeof a){if(e.constructor!==a.constructor)return!1;var s,l,u,c;if(Array.isArray(e)){if((s=e.length)!=a.length)return!1;for(l=s;0!==l--;)if(!o(e[l],a[l]))return!1;return!0}if(n&&e instanceof Map&&a instanceof Map){if(e.size!==a.size)return!1;for(c=e.entries();!(l=c.next()).done;)if(!a.has(l.value[0]))return!1;for(c=e.entries();!(l=c.next()).done;)if(!o(l.value[1],a.get(l.value[0])))return!1;return!0}if(r&&e instanceof Set&&a instanceof Set){if(e.size!==a.size)return!1;for(c=e.entries();!(l=c.next()).done;)if(!a.has(l.value[0]))return!1;return!0}if(i&&ArrayBuffer.isView(e)&&ArrayBuffer.isView(a)){if((s=e.length)!=a.length)return!1;for(l=s;0!==l--;)if(e[l]!==a[l])return!1;return!0}if(e.constructor===RegExp)return e.source===a.source&&e.flags===a.flags;if(e.valueOf!==Object.prototype.valueOf&&"function"===typeof e.valueOf&&"function"===typeof a.valueOf)return e.valueOf()===a.valueOf();if(e.toString!==Object.prototype.toString&&"function"===typeof e.toString&&"function"===typeof a.toString)return e.toString()===a.toString();if((s=(u=Object.keys(e)).length)!==Object.keys(a).length)return!1;for(l=s;0!==l--;)if(!Object.prototype.hasOwnProperty.call(a,u[l]))return!1;if(t&&e instanceof Element)return!1;for(l=s;0!==l--;)if(("_owner"!==u[l]&&"__v"!==u[l]&&"__o"!==u[l]||!e.$$typeof)&&!o(e[u[l]],a[u[l]]))return!1;return!0}return e!==e&&a!==a}e.exports=function(e,t){try{return o(e,t)}catch(n){if((n.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw n}}},6481:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});n(5043);var r=n(1228),i=n(579);const o=e=>{let{size:t="medium",color:n="primary",text:o="\u062c\u0627\u0631\u064a \u0627\u0644\u062a\u062d\u0645\u064a\u0644...",showText:a=!0,className:s=""}=e;return(0,i.jsxs)("div",{className:`loading-spinner ${{small:"spinner-small",medium:"spinner-medium",large:"spinner-large"}[t]} ${{primary:"spinner-primary",secondary:"spinner-secondary",white:"spinner-white"}[n]} ${s}`,children:[(0,i.jsx)(r.P.div,{className:"spinner-circle",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:(0,i.jsx)("svg",{viewBox:"0 0 50 50",className:"spinner-svg",children:(0,i.jsx)(r.P.circle,{cx:"25",cy:"25",r:"20",fill:"none",strokeWidth:"4",strokeLinecap:"round",className:"spinner-path",initial:{pathLength:0},animate:{pathLength:1},transition:{duration:1.5,repeat:1/0,ease:"easeInOut"}})})}),a&&o&&(0,i.jsx)(r.P.p,{className:"spinner-text",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:o})]})}},6524:(e,t,n)=>{"use strict";n.d(t,{QueryClientProvider:()=>d});var r=n(1991),i=n(7950).unstable_batchedUpdates;r.j.setBatchNotifyFunction(i);var o=n(75),a=console;(0,o.B)(a);var s=n(5043),l=s.createContext(void 0),u=s.createContext(!1);function c(e){return e&&"undefined"!==typeof window?(window.ReactQueryClientContext||(window.ReactQueryClientContext=l),window.ReactQueryClientContext):l}var d=function(e){var t=e.client,n=e.contextSharing,r=void 0!==n&&n,i=e.children;s.useEffect(function(){return t.mount(),function(){t.unmount()}},[t]);var o=c(r);return s.createElement(u.Provider,{value:r},s.createElement(o.Provider,{value:t},i))}},7065:(e,t,n)=>{"use strict";n.d(t,{WG:()=>s,Gt:()=>a,uv:()=>l,Ci:()=>u});var r=n(1892);class i{constructor(){this.order=[],this.scheduled=new Set}add(e){if(!this.scheduled.has(e))return this.scheduled.add(e),this.order.push(e),!0}remove(e){const t=this.order.indexOf(e);-1!==t&&(this.order.splice(t,1),this.scheduled.delete(e))}clear(){this.order.length=0,this.scheduled.clear()}}const o=["prepare","read","update","preRender","render","postRender"];const{schedule:a,cancel:s,state:l,steps:u}=function(e,t){let n=!1,r=!0;const a={delta:0,timestamp:0,isProcessing:!1},s=o.reduce((e,t)=>(e[t]=function(e){let t=new i,n=new i,r=0,o=!1,a=!1;const s=new WeakSet,l={schedule:function(e){const i=arguments.length>2&&void 0!==arguments[2]&&arguments[2]&&o,a=i?t:n;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&s.add(e),a.add(e)&&i&&o&&(r=t.order.length),e},cancel:e=>{n.remove(e),s.delete(e)},process:i=>{if(o)a=!0;else{if(o=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let n=0;n<r;n++){const r=t.order[n];r(i),s.has(r)&&(l.schedule(r),e())}o=!1,a&&(a=!1,l.process(i))}}};return l}(()=>n=!0),e),{}),l=e=>s[e].process(a),u=()=>{const i=performance.now();n=!1,a.delta=r?1e3/60:Math.max(Math.min(i-a.timestamp,40),1),a.timestamp=i,a.isProcessing=!0,o.forEach(l),a.isProcessing=!1,n&&t&&(r=!1,e(u))},c=o.reduce((t,i)=>{const o=s[i];return t[i]=function(t){let i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return n||(n=!0,r=!0,a.isProcessing||e(u)),o.schedule(t,i,s)},t},{});return{schedule:c,cancel:e=>o.forEach(t=>s[t].cancel(e)),state:a,steps:s}}("undefined"!==typeof requestAnimationFrame?requestAnimationFrame:r.l,!0)},7234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,i=e[r];if(!(0<o(i,t)))break e;e[r]=t,e[n]=i,n=r}}function r(e){return 0===e.length?null:e[0]}function i(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,i=e.length,a=i>>>1;r<a;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>o(l,n))u<i&&0>o(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<i&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var a=performance;t.unstable_now=function(){return a.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],d=1,f=null,h=3,p=!1,m=!1,v=!1,y="function"===typeof setTimeout?setTimeout:null,g="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(c);null!==t;){if(null===t.callback)i(c);else{if(!(t.startTime<=e))break;i(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(v=!1,x(e),!m)if(null!==r(u))m=!0,N(S);else{var t=r(c);null!==t&&D(w,t.startTime-e)}}function S(e,n){m=!1,v&&(v=!1,g(E),E=-1),p=!0;var o=h;try{for(x(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!j());){var a=f.callback;if("function"===typeof a){f.callback=null,h=f.priorityLevel;var s=a(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?f.callback=s:f===r(u)&&i(u),x(n)}else i(u);f=r(u)}if(null!==f)var l=!0;else{var d=r(c);null!==d&&D(w,d.startTime-n),l=!1}return l}finally{f=null,h=o,p=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,P=!1,C=null,E=-1,T=5,A=-1;function j(){return!(t.unstable_now()-A<T)}function O(){if(null!==C){var e=t.unstable_now();A=e;var n=!0;try{n=C(!0,e)}finally{n?k():(P=!1,C=null)}}else P=!1}if("function"===typeof b)k=function(){b(O)};else if("undefined"!==typeof MessageChannel){var M=new MessageChannel,L=M.port2;M.port1.onmessage=O,k=function(){L.postMessage(null)}}else k=function(){y(O,0)};function N(e){C=e,P||(P=!0,k())}function D(e,n){E=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||p||(m=!0,N(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):T=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return h},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(h){case 1:case 2:case 3:var t=3;break;default:t=h}var n=h;h=t;try{return e()}finally{h=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=h;h=e;try{return t()}finally{h=n}},t.unstable_scheduleCallback=function(e,i,o){var a=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?a+o:a:o=a,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:i,priorityLevel:e,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>a?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(v?(g(E),E=-1):v=!0,D(w,o-a))):(e.sortIndex=s,n(u,e),m||p||(m=!0,N(S))),e},t.unstable_shouldYield=j,t.unstable_wrapCallback=function(e){var t=h;return function(){var n=h;h=t;try{return e.apply(this,arguments)}finally{h=n}}}},7324:e=>{e.exports=function(e,t,n,r){var i=n?n.call(r,e,t):void 0;if(void 0!==i)return!!i;if(e===t)return!0;if("object"!==typeof e||!e||"object"!==typeof t||!t)return!1;var o=Object.keys(e),a=Object.keys(t);if(o.length!==a.length)return!1;for(var s=Object.prototype.hasOwnProperty.bind(t),l=0;l<o.length;l++){var u=o[l];if(!s(u))return!1;var c=e[u],d=t[u];if(!1===(i=n?n.call(r,c,d,u):void 0)||void 0===i&&c!==d)return!1}return!0}},7950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(2730)},8129:(e,t,n)=>{"use strict";n.d(t,{$:()=>i,V:()=>o});var r=n(1892);let i=r.l,o=r.l},8168:(e,t,n)=>{"use strict";function r(){return r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},r.apply(null,arguments)}n.d(t,{A:()=>r})},8443:(e,t,n)=>{"use strict";e.exports=n(9717)},8680:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>i});const r=(0,n(4548).vt)((e,t)=>({currentRoute:"home",previousRoute:null,routeParams:{},routeHistory:["home"],routes:{home:{path:"/",component:"Home",title:"\u0627\u0644\u0631\u0626\u064a\u0633\u064a\u0629",icon:"home"},player:{path:"/player/:videoId?",component:"Player",title:"\u0627\u0644\u0645\u0634\u063a\u0644",icon:"play"},search:{path:"/search/:query?",component:"Search",title:"\u0627\u0644\u0628\u062d\u062b",icon:"search"},settings:{path:"/settings/:section?",component:"Settings",title:"\u0627\u0644\u0625\u0639\u062f\u0627\u062f\u0627\u062a",icon:"settings"},history:{path:"/history",component:"History",title:"\u0627\u0644\u0633\u062c\u0644",icon:"history"},favorites:{path:"/favorites",component:"Favorites",title:"\u0627\u0644\u0645\u0641\u0636\u0644\u0629",icon:"heart"},about:{path:"/about",component:"About",title:"\u062d\u0648\u0644 \u0627\u0644\u062a\u0637\u0628\u064a\u0642",icon:"info"}},navigate:function(n){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const i=t(),o=i.currentRoute;e(e=>({previousRoute:o,currentRoute:n,routeParams:r,routeHistory:[...e.routeHistory,n].slice(-10)}));const a=i.routes[n];a&&(document.title=`${a.title} - YouTube Player Pro`)},goBack:()=>{const n=t();if(n.routeHistory.length>1){const t=n.routeHistory[n.routeHistory.length-2];e(e=>({currentRoute:t,routeHistory:e.routeHistory.slice(0,-1)}))}},goHome:()=>{t().navigate("home")},getCurrentRoute:()=>{const e=t();return e.routes[e.currentRoute]},isCurrentRoute:e=>t().currentRoute===e,getRouteTitle:e=>{var n;return(null===(n=t().routes[e])||void 0===n?void 0:n.title)||e},buildUrl:function(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const r=t().routes[e];if(!r)return"/";let i=r.path;return Object.entries(n).forEach(e=>{let[t,n]=e;i=i.replace(`:${t}?`,n).replace(`:${t}`,n)}),i=i.replace(/\/:[^/]+\?/g,""),i},parseUrl:e=>{const n=t();for(const[t,r]of Object.entries(n.routes)){const n=r.path.replace(/:[^/]+\?/g,"([^/]*)").replace(/:[^/]+/g,"([^/]+)"),i=new RegExp(`^${n}$`),o=e.match(i);if(o){const e=(r.path.match(/:[^/]+/g)||[]).map(e=>e.slice(1).replace("?","")),n={};return e.forEach((e,t)=>{o[t+1]&&(n[e]=decodeURIComponent(o[t+1]))}),{route:t,params:n}}}return{route:"home",params:{}}}})),i=r},8853:(e,t,n)=>{"use strict";e.exports=n(7234)},8870:(e,t,n)=>{"use strict";n.d(t,{BH:()=>g,Cp:()=>v,F$:()=>p,G6:()=>k,HN:()=>l,MK:()=>f,Od:()=>m,S$:()=>i,Zw:()=>a,b_:()=>d,gn:()=>s,j3:()=>u,jY:()=>P,lQ:()=>o,nJ:()=>h,vh:()=>c,yy:()=>S});var r=n(8168),i="undefined"===typeof window;function o(){}function a(e,t){return"function"===typeof e?e(t):e}function s(e){return"number"===typeof e&&e>=0&&e!==1/0}function l(e){return Array.isArray(e)?e:[e]}function u(e,t){return Math.max(e+(t||0)-Date.now(),0)}function c(e,t,n){return w(e)?"function"===typeof t?(0,r.A)({},n,{queryKey:e,queryFn:t}):(0,r.A)({},t,{queryKey:e}):e}function d(e,t,n){return w(e)?[(0,r.A)({},t,{queryKey:e}),n]:[e||{},t]}function f(e,t){var n=e.active,r=e.exact,i=e.fetching,o=e.inactive,a=e.predicate,s=e.queryKey,l=e.stale;if(w(s))if(r){if(t.queryHash!==p(s,t.options))return!1}else if(!v(t.queryKey,s))return!1;var u=function(e,t){return!0===e&&!0===t||null==e&&null==t?"all":!1===e&&!1===t?"none":(null!=e?e:!t)?"active":"inactive"}(n,o);if("none"===u)return!1;if("all"!==u){var c=t.isActive();if("active"===u&&!c)return!1;if("inactive"===u&&c)return!1}return("boolean"!==typeof l||t.isStale()===l)&&(("boolean"!==typeof i||t.isFetching()===i)&&!(a&&!a(t)))}function h(e,t){var n=e.exact,r=e.fetching,i=e.predicate,o=e.mutationKey;if(w(o)){if(!t.options.mutationKey)return!1;if(n){if(m(t.options.mutationKey)!==m(o))return!1}else if(!v(t.options.mutationKey,o))return!1}return("boolean"!==typeof r||"loading"===t.state.status===r)&&!(i&&!i(t))}function p(e,t){return((null==t?void 0:t.queryKeyHashFn)||m)(e)}function m(e){var t,n=l(e);return t=n,JSON.stringify(t,function(e,t){return b(t)?Object.keys(t).sort().reduce(function(e,n){return e[n]=t[n],e},{}):t})}function v(e,t){return y(l(e),l(t))}function y(e,t){return e===t||typeof e===typeof t&&(!(!e||!t||"object"!==typeof e||"object"!==typeof t)&&!Object.keys(t).some(function(n){return!y(e[n],t[n])}))}function g(e,t){if(e===t)return e;var n=Array.isArray(e)&&Array.isArray(t);if(n||b(e)&&b(t)){for(var r=n?e.length:Object.keys(e).length,i=n?t:Object.keys(t),o=i.length,a=n?[]:{},s=0,l=0;l<o;l++){var u=n?l:i[l];a[u]=g(e[u],t[u]),a[u]===e[u]&&s++}return r===o&&s===r?e:a}return t}function b(e){if(!x(e))return!1;var t=e.constructor;if("undefined"===typeof t)return!0;var n=t.prototype;return!!x(n)&&!!n.hasOwnProperty("isPrototypeOf")}function x(e){return"[object Object]"===Object.prototype.toString.call(e)}function w(e){return"string"===typeof e||Array.isArray(e)}function S(e){return new Promise(function(t){setTimeout(t,e)})}function k(e){Promise.resolve().then(e).catch(function(e){return setTimeout(function(){throw e})})}function P(){if("function"===typeof AbortController)return new AbortController}},9461:(e,t,n)=>{"use strict";e.exports=n(2330)},9674:(e,t,n)=>{"use strict";n.d(t,{t:()=>r});const r=(0,n(5043).createContext)(null)},9717:(e,t,n)=>{"use strict";var r=n(5043),i=n(9461);var o="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},a=i.useSyncExternalStore,s=r.useRef,l=r.useEffect,u=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var d=s(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=u(function(){function e(e){if(!l){if(l=!0,a=e,e=r(e),void 0!==i&&f.hasValue){var t=f.value;if(i(t,e))return s=t}return s=e}if(t=s,o(a,e))return t;var n=r(e);return void 0!==i&&i(t,n)?(a=e,t):(a=e,s=n)}var a,s,l=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,n,r,i]);var h=a(e,d[0],d[1]);return l(function(){f.hasValue=!0,f.value=h},[h]),c(h),h}}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.m=e,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,r)=>(n.f[r](e,t),t),[])),n.u=e=>"static/js/"+e+"."+{49:"6bbb4c8f",411:"987e5a9d",474:"23431437",695:"b0120344",781:"e385b7d4",844:"783a36b7",906:"a9c171f0"}[e]+".chunk.js",n.miniCssF=e=>"static/css/"+e+"."+{49:"58449272",411:"7e8f59a8",474:"5a990703",695:"d882fe01",781:"9478d0a4",844:"746ac42b",906:"2ef1da58"}[e]+".chunk.css",n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="youtube-player-pro:";n.l=(r,i,o,a)=>{if(e[r])e[r].push(i);else{var s,l;if(void 0!==o)for(var u=document.getElementsByTagName("script"),c=0;c<u.length;c++){var d=u[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+o){s=d;break}}s||(l=!0,(s=document.createElement("script")).charset="utf-8",s.timeout=120,n.nc&&s.setAttribute("nonce",n.nc),s.setAttribute("data-webpack",t+o),s.src=r),e[r]=[i];var f=(t,n)=>{s.onerror=s.onload=null,clearTimeout(h);var i=e[r];if(delete e[r],s.parentNode&&s.parentNode.removeChild(s),i&&i.forEach(e=>e(n)),t)return t(n)},h=setTimeout(f.bind(null,void 0,{type:"timeout",target:s}),12e4);s.onerror=f.bind(null,s.onerror),s.onload=f.bind(null,s.onload),l&&document.head.appendChild(s)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="./",(()=>{if("undefined"!==typeof document){var e=e=>new Promise((t,r)=>{var i=n.miniCssF(e),o=n.p+i;if(((e,t)=>{for(var n=document.getElementsByTagName("link"),r=0;r<n.length;r++){var i=(a=n[r]).getAttribute("data-href")||a.getAttribute("href");if("stylesheet"===a.rel&&(i===e||i===t))return a}var o=document.getElementsByTagName("style");for(r=0;r<o.length;r++){var a;if((i=(a=o[r]).getAttribute("data-href"))===e||i===t)return a}})(i,o))return t();((e,t,r,i,o)=>{var a=document.createElement("link");a.rel="stylesheet",a.type="text/css",n.nc&&(a.nonce=n.nc),a.onerror=a.onload=n=>{if(a.onerror=a.onload=null,"load"===n.type)i();else{var r=n&&n.type,s=n&&n.target&&n.target.href||t,l=new Error("Loading CSS chunk "+e+" failed.\n("+r+": "+s+")");l.name="ChunkLoadError",l.code="CSS_CHUNK_LOAD_FAILED",l.type=r,l.request=s,a.parentNode&&a.parentNode.removeChild(a),o(l)}},a.href=t,r?r.parentNode.insertBefore(a,r.nextSibling):document.head.appendChild(a)})(e,o,null,t,r)}),t={792:0};n.f.miniCss=(n,r)=>{t[n]?r.push(t[n]):0!==t[n]&&{49:1,411:1,474:1,695:1,781:1,844:1,906:1}[n]&&r.push(t[n]=e(n).then(()=>{t[n]=0},e=>{throw delete t[n],e}))}}})(),(()=>{var e={792:0};n.f.j=(t,r)=>{var i=n.o(e,t)?e[t]:void 0;if(0!==i)if(i)r.push(i[2]);else{var o=new Promise((n,r)=>i=e[t]=[n,r]);r.push(i[2]=o);var a=n.p+n.u(t),s=new Error;n.l(a,r=>{if(n.o(e,t)&&(0!==(i=e[t])&&(e[t]=void 0),i)){var o=r&&("load"===r.type?"missing":r.type),a=r&&r.target&&r.target.src;s.message="Loading chunk "+t+" failed.\n("+o+": "+a+")",s.name="ChunkLoadError",s.type=o,s.request=a,i[1](s)}},"chunk-"+t,t)}};var t=(t,r)=>{var i,o,a=r[0],s=r[1],l=r[2],u=0;if(a.some(t=>0!==e[t])){for(i in s)n.o(s,i)&&(n.m[i]=s[i]);if(l)l(n)}for(t&&t(r);u<a.length;u++)o=a[u],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunkyoutube_player_pro=self.webpackChunkyoutube_player_pro||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})(),(()=>{"use strict";var e=n(5043),t=n(4391),r=n(2907),i=(n(4089),n(5173)),o=n.n(i),a=n(6366),s=n.n(a),l=n(2740),u=n.n(l),c=n(7324),d=n.n(c);function f(){return f=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},f.apply(this,arguments)}function h(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,p(e,t)}function p(e,t){return p=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},p(e,t)}function m(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)t.indexOf(n=o[r])>=0||(i[n]=e[n]);return i}var v={BASE:"base",BODY:"body",HEAD:"head",HTML:"html",LINK:"link",META:"meta",NOSCRIPT:"noscript",SCRIPT:"script",STYLE:"style",TITLE:"title",FRAGMENT:"Symbol(react.fragment)"},y={rel:["amphtml","canonical","alternate"]},g={type:["application/ld+json"]},b={charset:"",name:["robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]},x=Object.keys(v).map(function(e){return v[e]}),w={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},S=Object.keys(w).reduce(function(e,t){return e[w[t]]=t,e},{}),k=function(e,t){for(var n=e.length-1;n>=0;n-=1){var r=e[n];if(Object.prototype.hasOwnProperty.call(r,t))return r[t]}return null},P=function(e){var t=k(e,v.TITLE),n=k(e,"titleTemplate");if(Array.isArray(t)&&(t=t.join("")),n&&t)return n.replace(/%s/g,function(){return t});var r=k(e,"defaultTitle");return t||r||void 0},C=function(e){return k(e,"onChangeClientState")||function(){}},E=function(e,t){return t.filter(function(t){return void 0!==t[e]}).map(function(t){return t[e]}).reduce(function(e,t){return f({},e,t)},{})},T=function(e,t){return t.filter(function(e){return void 0!==e[v.BASE]}).map(function(e){return e[v.BASE]}).reverse().reduce(function(t,n){if(!t.length)for(var r=Object.keys(n),i=0;i<r.length;i+=1){var o=r[i].toLowerCase();if(-1!==e.indexOf(o)&&n[o])return t.concat(n)}return t},[])},A=function(e,t,n){var r={};return n.filter(function(t){return!!Array.isArray(t[e])||(void 0!==t[e]&&console&&"function"==typeof console.warn&&console.warn("Helmet: "+e+' should be of type "Array". Instead found type "'+typeof t[e]+'"'),!1)}).map(function(t){return t[e]}).reverse().reduce(function(e,n){var i={};n.filter(function(e){for(var n,o=Object.keys(e),a=0;a<o.length;a+=1){var s=o[a],l=s.toLowerCase();-1===t.indexOf(l)||"rel"===n&&"canonical"===e[n].toLowerCase()||"rel"===l&&"stylesheet"===e[l].toLowerCase()||(n=l),-1===t.indexOf(s)||"innerHTML"!==s&&"cssText"!==s&&"itemprop"!==s||(n=s)}if(!n||!e[n])return!1;var u=e[n].toLowerCase();return r[n]||(r[n]={}),i[n]||(i[n]={}),!r[n][u]&&(i[n][u]=!0,!0)}).reverse().forEach(function(t){return e.push(t)});for(var o=Object.keys(i),a=0;a<o.length;a+=1){var s=o[a],l=f({},r[s],i[s]);r[s]=l}return e},[]).reverse()},j=function(e,t){if(Array.isArray(e)&&e.length)for(var n=0;n<e.length;n+=1)if(e[n][t])return!0;return!1},O=function(e){return Array.isArray(e)?e.join(""):e},M=function(e,t){return Array.isArray(e)?e.reduce(function(e,n){return function(e,t){for(var n=Object.keys(e),r=0;r<n.length;r+=1)if(t[n[r]]&&t[n[r]].includes(e[n[r]]))return!0;return!1}(n,t)?e.priority.push(n):e.default.push(n),e},{priority:[],default:[]}):{default:e}},L=function(e,t){var n;return f({},e,((n={})[t]=void 0,n))},N=[v.NOSCRIPT,v.SCRIPT,v.STYLE],D=function(e,t){return void 0===t&&(t=!0),!1===t?String(e):String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")},R=function(e){return Object.keys(e).reduce(function(t,n){var r=void 0!==e[n]?n+'="'+e[n]+'"':""+n;return t?t+" "+r:r},"")},z=function(e,t){return void 0===t&&(t={}),Object.keys(e).reduce(function(t,n){return t[w[n]||n]=e[n],t},t)},F=function(t,n){return n.map(function(n,r){var i,o=((i={key:r})["data-rh"]=!0,i);return Object.keys(n).forEach(function(e){var t=w[e]||e;"innerHTML"===t||"cssText"===t?o.dangerouslySetInnerHTML={__html:n.innerHTML||n.cssText}:o[t]=n[e]}),e.createElement(t,o)})},_=function(t,n,r){switch(t){case v.TITLE:return{toComponent:function(){return r=n.titleAttributes,(i={key:t=n.title})["data-rh"]=!0,o=z(r,i),[e.createElement(v.TITLE,o,t)];var t,r,i,o},toString:function(){return function(e,t,n,r){var i=R(n),o=O(t);return i?"<"+e+' data-rh="true" '+i+">"+D(o,r)+"</"+e+">":"<"+e+' data-rh="true">'+D(o,r)+"</"+e+">"}(t,n.title,n.titleAttributes,r)}};case"bodyAttributes":case"htmlAttributes":return{toComponent:function(){return z(n)},toString:function(){return R(n)}};default:return{toComponent:function(){return F(t,n)},toString:function(){return function(e,t,n){return t.reduce(function(t,r){var i=Object.keys(r).filter(function(e){return!("innerHTML"===e||"cssText"===e)}).reduce(function(e,t){var i=void 0===r[t]?t:t+'="'+D(r[t],n)+'"';return e?e+" "+i:i},""),o=r.innerHTML||r.cssText||"",a=-1===N.indexOf(e);return t+"<"+e+' data-rh="true" '+i+(a?"/>":">"+o+"</"+e+">")},"")}(t,n,r)}}}},I=function(e){var t=e.baseTag,n=e.bodyAttributes,r=e.encode,i=e.htmlAttributes,o=e.noscriptTags,a=e.styleTags,s=e.title,l=void 0===s?"":s,u=e.titleAttributes,c=e.linkTags,d=e.metaTags,f=e.scriptTags,h={toComponent:function(){},toString:function(){return""}};if(e.prioritizeSeoTags){var p=function(e){var t=e.linkTags,n=e.scriptTags,r=e.encode,i=M(e.metaTags,b),o=M(t,y),a=M(n,g);return{priorityMethods:{toComponent:function(){return[].concat(F(v.META,i.priority),F(v.LINK,o.priority),F(v.SCRIPT,a.priority))},toString:function(){return _(v.META,i.priority,r)+" "+_(v.LINK,o.priority,r)+" "+_(v.SCRIPT,a.priority,r)}},metaTags:i.default,linkTags:o.default,scriptTags:a.default}}(e);h=p.priorityMethods,c=p.linkTags,d=p.metaTags,f=p.scriptTags}return{priority:h,base:_(v.BASE,t,r),bodyAttributes:_("bodyAttributes",n,r),htmlAttributes:_("htmlAttributes",i,r),link:_(v.LINK,c,r),meta:_(v.META,d,r),noscript:_(v.NOSCRIPT,o,r),script:_(v.SCRIPT,f,r),style:_(v.STYLE,a,r),title:_(v.TITLE,{title:l,titleAttributes:u},r)}},V=[],B=function(e,t){var n=this;void 0===t&&(t="undefined"!=typeof document),this.instances=[],this.value={setHelmet:function(e){n.context.helmet=e},helmetInstances:{get:function(){return n.canUseDOM?V:n.instances},add:function(e){(n.canUseDOM?V:n.instances).push(e)},remove:function(e){var t=(n.canUseDOM?V:n.instances).indexOf(e);(n.canUseDOM?V:n.instances).splice(t,1)}}},this.context=e,this.canUseDOM=t,t||(e.helmet=I({baseTag:[],bodyAttributes:{},encodeSpecialCharacters:!0,htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))},U=e.createContext({}),H=o().shape({setHelmet:o().func,helmetInstances:o().shape({get:o().func,add:o().func,remove:o().func})}),$="undefined"!=typeof document,q=function(t){function n(e){var r;return(r=t.call(this,e)||this).helmetData=new B(r.props.context,n.canUseDOM),r}return h(n,t),n.prototype.render=function(){return e.createElement(U.Provider,{value:this.helmetData.value},this.props.children)},n}(e.Component);q.canUseDOM=$,q.propTypes={context:o().shape({helmet:o().shape()}),children:o().node.isRequired},q.defaultProps={context:{}},q.displayName="HelmetProvider";var W=function(e,t){var n,r=document.head||document.querySelector(v.HEAD),i=r.querySelectorAll(e+"[data-rh]"),o=[].slice.call(i),a=[];return t&&t.length&&t.forEach(function(t){var r=document.createElement(e);for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&("innerHTML"===i?r.innerHTML=t.innerHTML:"cssText"===i?r.styleSheet?r.styleSheet.cssText=t.cssText:r.appendChild(document.createTextNode(t.cssText)):r.setAttribute(i,void 0===t[i]?"":t[i]));r.setAttribute("data-rh","true"),o.some(function(e,t){return n=t,r.isEqualNode(e)})?o.splice(n,1):a.push(r)}),o.forEach(function(e){return e.parentNode.removeChild(e)}),a.forEach(function(e){return r.appendChild(e)}),{oldTags:o,newTags:a}},Q=function(e,t){var n=document.getElementsByTagName(e)[0];if(n){for(var r=n.getAttribute("data-rh"),i=r?r.split(","):[],o=[].concat(i),a=Object.keys(t),s=0;s<a.length;s+=1){var l=a[s],u=t[l]||"";n.getAttribute(l)!==u&&n.setAttribute(l,u),-1===i.indexOf(l)&&i.push(l);var c=o.indexOf(l);-1!==c&&o.splice(c,1)}for(var d=o.length-1;d>=0;d-=1)n.removeAttribute(o[d]);i.length===o.length?n.removeAttribute("data-rh"):n.getAttribute("data-rh")!==a.join(",")&&n.setAttribute("data-rh",a.join(","))}},K=function(e,t){var n=e.baseTag,r=e.htmlAttributes,i=e.linkTags,o=e.metaTags,a=e.noscriptTags,s=e.onChangeClientState,l=e.scriptTags,u=e.styleTags,c=e.title,d=e.titleAttributes;Q(v.BODY,e.bodyAttributes),Q(v.HTML,r),function(e,t){void 0!==e&&document.title!==e&&(document.title=O(e)),Q(v.TITLE,t)}(c,d);var f={baseTag:W(v.BASE,n),linkTags:W(v.LINK,i),metaTags:W(v.META,o),noscriptTags:W(v.NOSCRIPT,a),scriptTags:W(v.SCRIPT,l),styleTags:W(v.STYLE,u)},h={},p={};Object.keys(f).forEach(function(e){var t=f[e],n=t.newTags,r=t.oldTags;n.length&&(h[e]=n),r.length&&(p[e]=f[e].oldTags)}),t&&t(),s(e,h,p)},G=null,Y=function(e){function t(){for(var t,n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return(t=e.call.apply(e,[this].concat(r))||this).rendered=!1,t}h(t,e);var n=t.prototype;return n.shouldComponentUpdate=function(e){return!d()(e,this.props)},n.componentDidUpdate=function(){this.emitChange()},n.componentWillUnmount=function(){this.props.context.helmetInstances.remove(this),this.emitChange()},n.emitChange=function(){var e,t,n=this.props.context,r=n.setHelmet,i=null,o=(e=n.helmetInstances.get().map(function(e){var t=f({},e.props);return delete t.context,t}),{baseTag:T(["href"],e),bodyAttributes:E("bodyAttributes",e),defer:k(e,"defer"),encode:k(e,"encodeSpecialCharacters"),htmlAttributes:E("htmlAttributes",e),linkTags:A(v.LINK,["rel","href"],e),metaTags:A(v.META,["name","charset","http-equiv","property","itemprop"],e),noscriptTags:A(v.NOSCRIPT,["innerHTML"],e),onChangeClientState:C(e),scriptTags:A(v.SCRIPT,["src","innerHTML"],e),styleTags:A(v.STYLE,["cssText"],e),title:P(e),titleAttributes:E("titleAttributes",e),prioritizeSeoTags:j(e,"prioritizeSeoTags")});q.canUseDOM?(t=o,G&&cancelAnimationFrame(G),t.defer?G=requestAnimationFrame(function(){K(t,function(){G=null})}):(K(t),G=null)):I&&(i=I(o)),r(i)},n.init=function(){this.rendered||(this.rendered=!0,this.props.context.helmetInstances.add(this),this.emitChange())},n.render=function(){return this.init(),null},t}(e.Component);Y.propTypes={context:H.isRequired},Y.displayName="HelmetDispatcher";var X=["children"],Z=["children"],J=function(t){function n(){return t.apply(this,arguments)||this}h(n,t);var r=n.prototype;return r.shouldComponentUpdate=function(e){return!s()(L(this.props,"helmetData"),L(e,"helmetData"))},r.mapNestedChildrenToProps=function(e,t){if(!t)return null;switch(e.type){case v.SCRIPT:case v.NOSCRIPT:return{innerHTML:t};case v.STYLE:return{cssText:t};default:throw new Error("<"+e.type+" /> elements are self-closing and can not contain children. Refer to our API for more information.")}},r.flattenArrayTypeChildren=function(e){var t,n=e.child,r=e.arrayTypeChildren;return f({},r,((t={})[n.type]=[].concat(r[n.type]||[],[f({},e.newChildProps,this.mapNestedChildrenToProps(n,e.nestedChildren))]),t))},r.mapObjectTypeChildren=function(e){var t,n,r=e.child,i=e.newProps,o=e.newChildProps,a=e.nestedChildren;switch(r.type){case v.TITLE:return f({},i,((t={})[r.type]=a,t.titleAttributes=f({},o),t));case v.BODY:return f({},i,{bodyAttributes:f({},o)});case v.HTML:return f({},i,{htmlAttributes:f({},o)});default:return f({},i,((n={})[r.type]=f({},o),n))}},r.mapArrayTypeChildrenToProps=function(e,t){var n=f({},t);return Object.keys(e).forEach(function(t){var r;n=f({},n,((r={})[t]=e[t],r))}),n},r.warnOnInvalidChildren=function(e,t){return u()(x.some(function(t){return e.type===t}),"function"==typeof e.type?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":"Only elements types "+x.join(", ")+" are allowed. Helmet does not support rendering <"+e.type+"> elements. Refer to our API for more information."),u()(!t||"string"==typeof t||Array.isArray(t)&&!t.some(function(e){return"string"!=typeof e}),"Helmet expects a string as a child of <"+e.type+">. Did you forget to wrap your children in braces? ( <"+e.type+">{``}</"+e.type+"> ) Refer to our API for more information."),!0},r.mapChildrenToProps=function(t,n){var r=this,i={};return e.Children.forEach(t,function(e){if(e&&e.props){var t=e.props,o=t.children,a=m(t,X),s=Object.keys(a).reduce(function(e,t){return e[S[t]||t]=a[t],e},{}),l=e.type;switch("symbol"==typeof l?l=l.toString():r.warnOnInvalidChildren(e,o),l){case v.FRAGMENT:n=r.mapChildrenToProps(o,n);break;case v.LINK:case v.META:case v.NOSCRIPT:case v.SCRIPT:case v.STYLE:i=r.flattenArrayTypeChildren({child:e,arrayTypeChildren:i,newChildProps:s,nestedChildren:o});break;default:n=r.mapObjectTypeChildren({child:e,newProps:n,newChildProps:s,nestedChildren:o})}}}),this.mapArrayTypeChildrenToProps(i,n)},r.render=function(){var t=this.props,n=t.children,r=m(t,Z),i=f({},r),o=r.helmetData;return n&&(i=this.mapChildrenToProps(n,i)),!o||o instanceof B||(o=new B(o.context,o.instances)),o?e.createElement(Y,f({},i,{context:o.value,helmetData:void 0})):e.createElement(U.Consumer,null,function(t){return e.createElement(Y,f({},i,{context:t}))})},n}(e.Component);J.propTypes={base:o().object,bodyAttributes:o().object,children:o().oneOfType([o().arrayOf(o().node),o().node]),defaultTitle:o().string,defer:o().bool,encodeSpecialCharacters:o().bool,htmlAttributes:o().object,link:o().arrayOf(o().object),meta:o().arrayOf(o().object),noscript:o().arrayOf(o().object),onChangeClientState:o().func,script:o().arrayOf(o().object),style:o().arrayOf(o().object),title:o().string,titleAttributes:o().object,titleTemplate:o().string,prioritizeSeoTags:o().bool,helmetData:o().object},J.defaultProps={defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1},J.displayName="Helmet";const ee=(0,e.createContext)(null),te={didCatch:!1,error:null};class ne extends e.Component{constructor(e){super(e),this.resetErrorBoundary=this.resetErrorBoundary.bind(this),this.state=te}static getDerivedStateFromError(e){return{didCatch:!0,error:e}}resetErrorBoundary(){const{error:e}=this.state;if(null!==e){for(var t,n,r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];null===(t=(n=this.props).onReset)||void 0===t||t.call(n,{args:i,reason:"imperative-api"}),this.setState(te)}}componentDidCatch(e,t){var n,r;null===(n=(r=this.props).onError)||void 0===n||n.call(r,e,t)}componentDidUpdate(e,t){const{didCatch:n}=this.state,{resetKeys:r}=this.props;var i,o;n&&null!==t.error&&function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return e.length!==t.length||e.some((e,n)=>!Object.is(e,t[n]))}(e.resetKeys,r)&&(null===(i=(o=this.props).onReset)||void 0===i||i.call(o,{next:r,prev:e.resetKeys,reason:"keys"}),this.setState(te))}render(){const{children:t,fallbackRender:n,FallbackComponent:r,fallback:i}=this.props,{didCatch:o,error:a}=this.state;let s=t;if(o){const t={error:a,resetErrorBoundary:this.resetErrorBoundary};if("function"===typeof n)s=n(t);else if(r)s=(0,e.createElement)(r,t);else{if(void 0===i)throw a;s=i}}return(0,e.createElement)(ee.Provider,{value:{didCatch:o,error:a,resetErrorBoundary:this.resetErrorBoundary}},s)}}let re={data:""},ie=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||re,oe=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,ae=/\/\*[^]*?\*\/|  +/g,se=/\n+/g,le=(e,t)=>{let n="",r="",i="";for(let o in e){let a=e[o];"@"==o[0]?"i"==o[1]?n=o+" "+a+";":r+="f"==o[1]?le(a,o):o+"{"+le(a,"k"==o[1]?"":t)+"}":"object"==typeof a?r+=le(a,t?t.replace(/([^,])+/g,e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):o):null!=a&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=le.p?le.p(o,a):o+":"+a+";")}return n+(t&&i?t+"{"+i+"}":i)+r},ue={},ce=e=>{if("object"==typeof e){let t="";for(let n in e)t+=n+ce(e[n]);return t}return e};function de(e){let t=this||{},n=e.call?e(t.p):e;return((e,t,n,r,i)=>{let o=ce(e),a=ue[o]||(ue[o]=(e=>{let t=0,n=11;for(;t<e.length;)n=101*n+e.charCodeAt(t++)>>>0;return"go"+n})(o));if(!ue[a]){let t=o!==e?e:(e=>{let t,n,r=[{}];for(;t=oe.exec(e.replace(ae,""));)t[4]?r.shift():t[3]?(n=t[3].replace(se," ").trim(),r.unshift(r[0][n]=r[0][n]||{})):r[0][t[1]]=t[2].replace(se," ").trim();return r[0]})(e);ue[a]=le(i?{["@keyframes "+a]:t}:t,n?"":"."+a)}let s=n&&ue.g?ue.g:null;return n&&(ue.g=ue[a]),((e,t,n,r)=>{r?t.data=t.data.replace(r,e):-1===t.data.indexOf(e)&&(t.data=n?e+t.data:t.data+e)})(ue[a],t,r,s),a})(n.unshift?n.raw?((e,t,n)=>e.reduce((e,r,i)=>{let o=t[i];if(o&&o.call){let e=o(n),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=t?"."+t:e&&"object"==typeof e?e.props?"":le(e,""):!1===e?"":e}return e+r+(null==o?"":o)},""))(n,[].slice.call(arguments,1),t.p):n.reduce((e,n)=>Object.assign(e,n&&n.call?n(t.p):n),{}):n,ie(t.target),t.g,t.o,t.k)}de.bind({g:1});let fe,he,pe,me=de.bind({k:1});function ve(e,t){let n=this||{};return function(){let r=arguments;function i(o,a){let s=Object.assign({},o),l=s.className||i.className;n.p=Object.assign({theme:he&&he()},s),n.o=/ *go\d+/.test(l),s.className=de.apply(n,r)+(l?" "+l:""),t&&(s.ref=a);let u=e;return e[0]&&(u=s.as||e,delete s.as),pe&&u[0]&&pe(s),fe(u,s)}return t?t(i):i}}var ye=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,ge=(()=>{let e=0;return()=>(++e).toString()})(),be=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),xe=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:n}=t;return xe(e,{type:e.toasts.find(e=>e.id===n.id)?1:0,toast:n});case 3:let{toastId:r}=t;return{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let i=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+i}))}}},we=[],Se={toasts:[],pausedAt:void 0},ke=e=>{Se=xe(Se,e),we.forEach(e=>{e(Se)})},Pe={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Ce=e=>(t,n)=>{let r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"blank",n=arguments.length>2?arguments[2]:void 0;return{createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...n,id:(null==n?void 0:n.id)||ge()}}(t,e,n);return ke({type:2,toast:r}),r.id},Ee=(e,t)=>Ce("blank")(e,t);Ee.error=Ce("error"),Ee.success=Ce("success"),Ee.loading=Ce("loading"),Ee.custom=Ce("custom"),Ee.dismiss=e=>{ke({type:3,toastId:e})},Ee.remove=e=>ke({type:4,toastId:e}),Ee.promise=(e,t,n)=>{let r=Ee.loading(t.loading,{...n,...null==n?void 0:n.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let i=t.success?ye(t.success,e):void 0;return i?Ee.success(i,{id:r,...n,...null==n?void 0:n.success}):Ee.dismiss(r),e}).catch(e=>{let i=t.error?ye(t.error,e):void 0;i?Ee.error(i,{id:r,...n,...null==n?void 0:n.error}):Ee.dismiss(r)}),e};var Te=(e,t)=>{ke({type:1,toast:{id:e,height:t}})},Ae=()=>{ke({type:5,time:Date.now()})},je=new Map,Oe=t=>{let{toasts:n,pausedAt:r}=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[n,r]=(0,e.useState)(Se),i=(0,e.useRef)(Se);(0,e.useEffect)(()=>(i.current!==Se&&r(Se),we.push(r),()=>{let e=we.indexOf(r);e>-1&&we.splice(e,1)}),[]);let o=n.toasts.map(e=>{var n,r,i;return{...t,...t[e.type],...e,removeDelay:e.removeDelay||(null==(n=t[e.type])?void 0:n.removeDelay)||(null==t?void 0:t.removeDelay),duration:e.duration||(null==(r=t[e.type])?void 0:r.duration)||(null==t?void 0:t.duration)||Pe[e.type],style:{...t.style,...null==(i=t[e.type])?void 0:i.style,...e.style}}});return{...n,toasts:o}}(t);(0,e.useEffect)(()=>{if(r)return;let e=Date.now(),t=n.map(t=>{if(t.duration===1/0)return;let n=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(n<0))return setTimeout(()=>Ee.dismiss(t.id),n);t.visible&&Ee.dismiss(t.id)});return()=>{t.forEach(e=>e&&clearTimeout(e))}},[n,r]);let i=(0,e.useCallback)(()=>{r&&ke({type:6,time:Date.now()})},[r]),o=(0,e.useCallback)((e,t)=>{let{reverseOrder:r=!1,gutter:i=8,defaultPosition:o}=t||{},a=n.filter(t=>(t.position||o)===(e.position||o)&&t.height),s=a.findIndex(t=>t.id===e.id),l=a.filter((e,t)=>t<s&&e.visible).length;return a.filter(e=>e.visible).slice(...r?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+i,0)},[n]);return(0,e.useEffect)(()=>{n.forEach(e=>{if(e.dismissed)!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;if(je.has(e))return;let n=setTimeout(()=>{je.delete(e),ke({type:4,toastId:e})},t);je.set(e,n)}(e.id,e.removeDelay);else{let t=je.get(e.id);t&&(clearTimeout(t),je.delete(e.id))}})},[n]),{toasts:n,handlers:{updateHeight:Te,startPause:Ae,endPause:i,calculateOffset:o}}},Me=me`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Le=me`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Ne=me`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,De=ve("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Me} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Le} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Ne} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Re=me`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,ze=ve("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Re} 1s linear infinite;
`,Fe=me`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,_e=me`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Ie=ve("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Fe} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${_e} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Ve=ve("div")`
  position: absolute;
`,Be=ve("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Ue=me`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,He=ve("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Ue} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,$e=t=>{let{toast:n}=t,{icon:r,type:i,iconTheme:o}=n;return void 0!==r?"string"==typeof r?e.createElement(He,null,r):r:"blank"===i?null:e.createElement(Be,null,e.createElement(ze,{...o}),"loading"!==i&&e.createElement(Ve,null,"error"===i?e.createElement(De,{...o}):e.createElement(Ie,{...o})))},qe=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,We=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,Qe=ve("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Ke=ve("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Ge=e.memo(t=>{let{toast:n,position:r,style:i,children:o}=t,a=n.height?((e,t)=>{let n=e.includes("top")?1:-1,[r,i]=be()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[qe(n),We(n)];return{animation:t?`${me(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${me(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(n.position||r||"top-center",n.visible):{opacity:0},s=e.createElement($e,{toast:n}),l=e.createElement(Ke,{...n.ariaProps},ye(n.message,n));return e.createElement(Qe,{className:n.className,style:{...a,...i,...n.style}},"function"==typeof o?o({icon:s,message:l}):e.createElement(e.Fragment,null,s,l))});!function(e,t,n,r){le.p=t,fe=e,he=n,pe=r}(e.createElement);var Ye=t=>{let{id:n,className:r,style:i,onHeightUpdate:o,children:a}=t,s=e.useCallback(e=>{if(e){let t=()=>{let t=e.getBoundingClientRect().height;o(n,t)};t(),new MutationObserver(t).observe(e,{subtree:!0,childList:!0,characterData:!0})}},[n,o]);return e.createElement("div",{ref:s,className:r,style:i},a)},Xe=de`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Ze=t=>{let{reverseOrder:n,position:r="top-center",toastOptions:i,gutter:o,children:a,containerStyle:s,containerClassName:l}=t,{toasts:u,handlers:c}=Oe(i);return e.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...s},className:l,onMouseEnter:c.startPause,onMouseLeave:c.endPause},u.map(t=>{let i=t.position||r,s=((e,t)=>{let n=e.includes("top"),r=n?{top:0}:{bottom:0},i=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:be()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(n?1:-1)}px)`,...r,...i}})(i,c.calculateOffset(t,{reverseOrder:n,gutter:o,defaultPosition:r}));return e.createElement(Ye,{id:t.id,key:t.id,onHeightUpdate:c.updateHeight,className:t.visible?Xe:"",style:s},"custom"===t.type?ye(t.message,t):a?a(t):e.createElement(Ge,{toast:t,position:i}))}))},Je=n(293);function et(){const t=(0,e.useRef)(!1);return(0,Je.E)(()=>(t.current=!0,()=>{t.current=!1}),[]),t}var tt=n(7065);var nt=n(9674),rt=n(4930);class it extends e.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function ot(t){let{children:n,isPresent:r}=t;const i=(0,e.useId)(),o=(0,e.useRef)(null),a=(0,e.useRef)({width:0,height:0,top:0,left:0});return(0,e.useInsertionEffect)(()=>{const{width:e,height:t,top:n,left:s}=a.current;if(r||!o.current||!e||!t)return;o.current.dataset.motionPopId=i;const l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`\n          [data-motion-pop-id="${i}"] {\n            position: absolute !important;\n            width: ${e}px !important;\n            height: ${t}px !important;\n            top: ${n}px !important;\n            left: ${s}px !important;\n          }\n        `),()=>{document.head.removeChild(l)}},[r]),e.createElement(it,{isPresent:r,childRef:o,sizeRef:a},e.cloneElement(n,{ref:o}))}const at=t=>{let{children:n,initial:r,isPresent:i,onExitComplete:o,custom:a,presenceAffectsLayout:s,mode:l}=t;const u=(0,rt.M)(st),c=(0,e.useId)(),d=(0,e.useMemo)(()=>({id:c,initial:r,isPresent:i,custom:a,onExitComplete:e=>{u.set(e,!0);for(const t of u.values())if(!t)return;o&&o()},register:e=>(u.set(e,!1),()=>u.delete(e))}),s?void 0:[i]);return(0,e.useMemo)(()=>{u.forEach((e,t)=>u.set(t,!1))},[i]),e.useEffect(()=>{!i&&!u.size&&o&&o()},[i]),"popLayout"===l&&(n=e.createElement(ot,{isPresent:i},n)),e.createElement(nt.t.Provider,{value:d},n)};function st(){return new Map}var lt=n(2190);var ut=n(8129);const ct=e=>e.key||"";const dt=t=>{let{children:n,custom:r,initial:i=!0,onExitComplete:o,exitBeforeEnter:a,presenceAffectsLayout:s=!0,mode:l="sync"}=t;(0,ut.V)(!a,"Replace exitBeforeEnter with mode='wait'");const u=(0,e.useContext)(lt.L).forceRender||function(){const t=et(),[n,r]=(0,e.useState)(0),i=(0,e.useCallback)(()=>{t.current&&r(n+1)},[n]);return[(0,e.useCallback)(()=>tt.Gt.postRender(i),[i]),n]}()[0],c=et(),d=function(t){const n=[];return e.Children.forEach(t,t=>{(0,e.isValidElement)(t)&&n.push(t)}),n}(n);let f=d;const h=(0,e.useRef)(new Map).current,p=(0,e.useRef)(f),m=(0,e.useRef)(new Map).current,v=(0,e.useRef)(!0);var y;if((0,Je.E)(()=>{v.current=!1,function(e,t){e.forEach(e=>{const n=ct(e);t.set(n,e)})}(d,m),p.current=f}),y=()=>{v.current=!0,m.clear(),h.clear()},(0,e.useEffect)(()=>()=>y(),[]),v.current)return e.createElement(e.Fragment,null,f.map(t=>e.createElement(at,{key:ct(t),isPresent:!0,initial:!!i&&void 0,presenceAffectsLayout:s,mode:l},t)));f=[...f];const g=p.current.map(ct),b=d.map(ct),x=g.length;for(let e=0;e<x;e++){const t=g[e];-1!==b.indexOf(t)||h.has(t)||h.set(t,void 0)}return"wait"===l&&h.size&&(f=[]),h.forEach((t,n)=>{if(-1!==b.indexOf(n))return;const i=m.get(n);if(!i)return;const a=g.indexOf(n);let v=t;if(!v){const t=()=>{h.delete(n);const e=Array.from(m.keys()).filter(e=>!b.includes(e));if(e.forEach(e=>m.delete(e)),p.current=d.filter(t=>{const r=ct(t);return r===n||e.includes(r)}),!h.size){if(!1===c.current)return;u(),o&&o()}};v=e.createElement(at,{key:ct(i),isPresent:!1,onExitComplete:t,custom:r,presenceAffectsLayout:s,mode:l},i),h.set(n,v)}f.splice(a,0,v)}),f=f.map(t=>{const n=t.key;return h.has(n)?t:e.createElement(at,{key:ct(t),isPresent:!0,presenceAffectsLayout:s,mode:l},t)}),e.createElement(e.Fragment,null,h.size?f:f.map(t=>(0,e.cloneElement)(t)))};var ft=n(1228),ht=n(663),pt=n(8680),mt=n(579);const vt=t=>{let{onSubmit:n,onFocus:r,onBlur:i,placeholder:o="\u0627\u0628\u062d\u062b \u0641\u064a \u064a\u0648\u062a\u064a\u0648\u0628...",autoFocus:a=!1}=t;const[s,l]=(0,e.useState)(""),[u,c]=(0,e.useState)(!1),[d,f]=(0,e.useState)(!1),h=(0,e.useRef)(null),{search:{history:p,suggestions:m},addToSearchHistory:v,clearSearchHistory:y}=(0,ht.A)();(0,e.useEffect)(()=>{a&&h.current&&h.current.focus()},[a]);const g=e=>{var t;l(e),v(e),null===n||void 0===n||n(e),f(!1),null===(t=h.current)||void 0===t||t.blur()},b=p.filter(e=>e.toLowerCase().includes(s.toLowerCase())),x=m.filter(e=>e.toLowerCase().includes(s.toLowerCase())&&!p.includes(e));return(0,mt.jsxs)("div",{className:"search-bar-container",children:[(0,mt.jsx)("form",{onSubmit:e=>{var t;(e.preventDefault(),s.trim())&&(v(s.trim()),null===n||void 0===n||n(s.trim()),f(!1),null===(t=h.current)||void 0===t||t.blur())},className:"search-form",children:(0,mt.jsxs)("div",{className:"search-input-container "+(u?"focused":""),children:[(0,mt.jsx)("button",{type:"submit",className:"search-button","aria-label":"\u0628\u062d\u062b",children:(0,mt.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"})})}),(0,mt.jsx)("input",{ref:h,type:"text",value:s,onChange:e=>l(e.target.value),onFocus:()=>{c(!0),f(!0),null===r||void 0===r||r()},onBlur:()=>{setTimeout(()=>{c(!1),f(!1),null===i||void 0===i||i()},150)},placeholder:o,className:"search-input",autoComplete:"off",spellCheck:"false"}),s&&(0,mt.jsx)(ft.P.button,{type:"button",className:"clear-button",onClick:()=>l(""),initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},"aria-label":"\u0645\u0633\u062d",children:(0,mt.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})})}),(0,mt.jsx)("button",{type:"button",className:"voice-button","aria-label":"\u0627\u0644\u0628\u062d\u062b \u0627\u0644\u0635\u0648\u062a\u064a",disabled:!0,children:(0,mt.jsx)("svg",{width:"18",height:"18",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"})})})]})}),(0,mt.jsx)(dt,{children:d&&(b.length>0||x.length>0)&&(0,mt.jsxs)(ft.P.div,{className:"suggestions-dropdown",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.2},children:[b.length>0&&(0,mt.jsxs)("div",{className:"suggestions-section",children:[(0,mt.jsxs)("div",{className:"suggestions-header",children:[(0,mt.jsx)("span",{children:"\u0627\u0644\u0628\u062d\u062b \u0627\u0644\u0633\u0627\u0628\u0642"}),(0,mt.jsx)("button",{className:"clear-history-button",onClick:()=>{y()},"aria-label":"\u0645\u0633\u062d \u0627\u0644\u0633\u062c\u0644",children:(0,mt.jsx)("svg",{width:"14",height:"14",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})})})]}),b.map((e,t)=>(0,mt.jsxs)(ft.P.button,{className:"suggestion-item history-item",onClick:()=>g(e),initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.05*t},children:[(0,mt.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"})}),(0,mt.jsx)("span",{children:e})]},`history-${t}`))]}),x.length>0&&(0,mt.jsxs)("div",{className:"suggestions-section",children:[(0,mt.jsx)("div",{className:"suggestions-header",children:(0,mt.jsx)("span",{children:"\u0627\u0642\u062a\u0631\u0627\u062d\u0627\u062a \u0627\u0644\u0628\u062d\u062b"})}),x.map((e,t)=>(0,mt.jsxs)(ft.P.button,{className:"suggestion-item",onClick:()=>g(e),initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.05*(b.length+t)},children:[(0,mt.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"})}),(0,mt.jsx)("span",{children:e})]},`suggestion-${t}`))]})]})})]})},yt=()=>{const{theme:e,setTheme:t}=(0,ht.A)(),n="dark"===e;return(0,mt.jsx)(ft.P.button,{className:"theme-toggle",onClick:()=>{t("dark"===e?"light":"dark")},whileHover:{scale:1.1},whileTap:{scale:.9},"aria-label":n?"\u062a\u0628\u062f\u064a\u0644 \u0625\u0644\u0649 \u0627\u0644\u062b\u064a\u0645 \u0627\u0644\u0641\u0627\u062a\u062d":"\u062a\u0628\u062f\u064a\u0644 \u0625\u0644\u0649 \u0627\u0644\u062b\u064a\u0645 \u0627\u0644\u062f\u0627\u0643\u0646",title:n?"\u062a\u0628\u062f\u064a\u0644 \u0625\u0644\u0649 \u0627\u0644\u062b\u064a\u0645 \u0627\u0644\u0641\u0627\u062a\u062d":"\u062a\u0628\u062f\u064a\u0644 \u0625\u0644\u0649 \u0627\u0644\u062b\u064a\u0645 \u0627\u0644\u062f\u0627\u0643\u0646",children:(0,mt.jsx)(ft.P.div,{className:"theme-toggle-container",animate:{rotate:n?0:180},transition:{duration:.3,ease:"easeInOut"},children:n?(0,mt.jsx)(ft.P.svg,{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:.2},children:(0,mt.jsx)("path",{d:"M12 2.25a.75.75 0 01.75.75v2.25a.75.75 0 01-1.5 0V3a.75.75 0 01.75-.75zM7.5 12a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM18.894 6.166a.75.75 0 00-1.06-1.06l-1.591 1.59a.75.75 0 101.06 1.061l1.591-1.59zM21.75 12a.75.75 0 01-.75.75h-2.25a.75.75 0 010-1.5H21a.75.75 0 01.75.75zM17.834 18.894a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 10-1.061 1.06l1.59 1.591zM12 18a.75.75 0 01.75.75V21a.75.75 0 01-1.5 0v-2.25A.75.75 0 0112 18zM7.758 17.303a.75.75 0 00-1.061-1.06l-1.591 1.59a.75.75 0 001.06 1.061l1.591-1.59zM6 12a.75.75 0 01-.75.75H3a.75.75 0 010-1.5h2.25A.75.75 0 016 12zM6.697 7.757a.75.75 0 001.06-1.06l-1.59-1.591a.75.75 0 00-1.061 1.06l1.59 1.591z"})}):(0,mt.jsx)(ft.P.svg,{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",initial:{opacity:0,scale:.5},animate:{opacity:1,scale:1},transition:{duration:.2},children:(0,mt.jsx)("path",{d:"M9.528 1.718a.75.75 0 01.162.819A8.97 8.97 0 009 6a9 9 0 009 9 8.97 8.97 0 003.463-.69.75.75 0 01.981.98 10.503 10.503 0 01-9.694 6.46c-5.799 0-10.5-4.701-10.5-10.5 0-4.368 2.667-8.112 6.46-9.694a.75.75 0 01.818.162z"})})})})},gt=()=>window.electronAPI?(0,mt.jsxs)("div",{className:"window-controls",children:[(0,mt.jsx)(ft.P.button,{className:"window-control minimize",onClick:async()=>{var e;null!==(e=window.electronAPI)&&void 0!==e&&e.minimizeWindow&&await window.electronAPI.minimizeWindow()},whileHover:{scale:1.1},whileTap:{scale:.9},"aria-label":"\u062a\u0635\u063a\u064a\u0631 \u0627\u0644\u0646\u0627\u0641\u0630\u0629",title:"\u062a\u0635\u063a\u064a\u0631 \u0627\u0644\u0646\u0627\u0641\u0630\u0629",children:(0,mt.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"currentColor",children:(0,mt.jsx)("rect",{x:"2",y:"5",width:"8",height:"2",rx:"1"})})}),(0,mt.jsx)(ft.P.button,{className:"window-control maximize",onClick:async()=>{var e;null!==(e=window.electronAPI)&&void 0!==e&&e.maximizeWindow&&await window.electronAPI.maximizeWindow()},whileHover:{scale:1.1},whileTap:{scale:.9},"aria-label":"\u062a\u0643\u0628\u064a\u0631 \u0627\u0644\u0646\u0627\u0641\u0630\u0629",title:"\u062a\u0643\u0628\u064a\u0631 \u0627\u0644\u0646\u0627\u0641\u0630\u0629",children:(0,mt.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"currentColor",children:(0,mt.jsx)("rect",{x:"2",y:"2",width:"8",height:"8",rx:"1",fill:"none",stroke:"currentColor",strokeWidth:"1.5"})})}),(0,mt.jsx)(ft.P.button,{className:"window-control close",onClick:async()=>{var e;null!==(e=window.electronAPI)&&void 0!==e&&e.closeWindow&&await window.electronAPI.closeWindow()},whileHover:{scale:1.1},whileTap:{scale:.9},"aria-label":"\u0625\u063a\u0644\u0627\u0642 \u0627\u0644\u0646\u0627\u0641\u0630\u0629",title:"\u0625\u063a\u0644\u0627\u0642 \u0627\u0644\u0646\u0627\u0641\u0630\u0629",children:(0,mt.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M2.5 2.5L9.5 9.5M9.5 2.5L2.5 9.5",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round"})})})]}):null,bt=()=>{const[t,n]=(0,e.useState)(!1),{currentVideo:r,sidebar:i,toggleSidebar:o}=(0,ht.A)(),{currentRoute:a,navigate:s}=(0,pt.Ay)();return(0,mt.jsxs)(ft.P.header,{className:"header",initial:{y:-60},animate:{y:0},transition:{duration:.3},children:[(0,mt.jsxs)("div",{className:"header-content",children:[(0,mt.jsxs)("div",{className:"header-left",children:[(0,mt.jsx)("button",{className:"sidebar-toggle",onClick:o,"aria-label":i.isOpen?"\u0625\u062e\u0641\u0627\u0621 \u0627\u0644\u0634\u0631\u064a\u0637 \u0627\u0644\u062c\u0627\u0646\u0628\u064a":"\u0625\u0638\u0647\u0627\u0631 \u0627\u0644\u0634\u0631\u064a\u0637 \u0627\u0644\u062c\u0627\u0646\u0628\u064a",children:(0,mt.jsx)(ft.P.div,{animate:{rotate:i.isOpen?0:180},transition:{duration:.2},children:(0,mt.jsx)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"})})})}),(0,mt.jsxs)(ft.P.div,{className:"logo",onClick:()=>{s("home")},whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,mt.jsx)("div",{className:"logo-icon",children:(0,mt.jsx)("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M8 5v14l11-7z"})})}),(0,mt.jsxs)("div",{className:"logo-text",children:[(0,mt.jsx)("span",{className:"logo-title",children:"YouTube Player"}),(0,mt.jsx)("span",{className:"logo-subtitle",children:"Pro"})]})]})]}),(0,mt.jsx)("div",{className:"header-center",children:(0,mt.jsx)(ft.P.div,{className:"search-container "+(t?"focused":""),animate:{width:t?"100%":"60%",maxWidth:t?"800px":"600px"},transition:{duration:.2},children:(0,mt.jsx)(vt,{onSubmit:e=>{e.trim()&&s("search",{query:e.trim()})},onFocus:()=>n(!0),onBlur:()=>n(!1),placeholder:"\u0627\u0628\u062d\u062b \u0641\u064a \u064a\u0648\u062a\u064a\u0648\u0628...",autoFocus:"search"===a})})}),(0,mt.jsxs)("div",{className:"header-right",children:[r.id&&(0,mt.jsxs)(ft.P.div,{className:"current-video-info",initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},children:[(0,mt.jsxs)("div",{className:"video-thumbnail",children:[(0,mt.jsx)("img",{src:`https://img.youtube.com/vi/${r.id}/mqdefault.jpg`,alt:r.title,loading:"lazy"}),(0,mt.jsx)("div",{className:"play-indicator",children:r.isPlaying?(0,mt.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M6 19h4V5H6v14zm8-14v14h4V5h-4z"})}):(0,mt.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M8 5v14l11-7z"})})})]}),(0,mt.jsxs)("div",{className:"video-details",children:[(0,mt.jsx)("div",{className:"video-title",title:r.title,children:r.title}),(0,mt.jsx)("div",{className:"video-channel",title:r.channel,children:r.channel})]})]}),(0,mt.jsx)(yt,{}),(0,mt.jsx)(ft.P.button,{className:"header-button settings-button",onClick:()=>s("settings"),whileHover:{scale:1.1},whileTap:{scale:.9},"aria-label":"\u0627\u0644\u0625\u0639\u062f\u0627\u062f\u0627\u062a",children:(0,mt.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"})})}),window.electronAPI&&(0,mt.jsx)(gt,{})]})]}),r.id&&r.duration>0&&(0,mt.jsx)(ft.P.div,{className:"video-progress-bar",initial:{scaleX:0},animate:{scaleX:1},style:{originX:0},children:(0,mt.jsx)(ft.P.div,{className:"progress-fill",animate:{width:r.currentTime/r.duration*100+"%"},transition:{duration:.1}})})]})},xt=()=>{const{sidebar:e,stats:t}=(0,ht.A)(),{currentRoute:n,navigate:r}=(0,pt.Ay)(),i=[{id:"home",label:"\u0627\u0644\u0631\u0626\u064a\u0633\u064a\u0629",icon:(0,mt.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"})}),route:"home"},{id:"search",label:"\u0627\u0644\u0628\u062d\u062b",icon:(0,mt.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"})}),route:"search"},{id:"history",label:"\u0627\u0644\u0633\u062c\u0644",icon:(0,mt.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"})}),route:"history",badge:t.videosWatched>0?t.videosWatched:null},{id:"favorites",label:"\u0627\u0644\u0645\u0641\u0636\u0644\u0629",icon:(0,mt.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"})}),route:"favorites"}],o=[{id:"settings",label:"\u0627\u0644\u0625\u0639\u062f\u0627\u062f\u0627\u062a",icon:(0,mt.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"})}),route:"settings"},{id:"about",label:"\u062d\u0648\u0644 \u0627\u0644\u062a\u0637\u0628\u064a\u0642",icon:(0,mt.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"})}),route:"about"}],a=(e,t)=>{const i=n===e.route;return(0,mt.jsxs)(ft.P.button,{className:"sidebar-menu-item "+(i?"active":""),onClick:()=>{return t=e.route,void r(t);var t},whileHover:{x:-4},whileTap:{scale:.98},initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.1*t},children:[(0,mt.jsx)("div",{className:"menu-item-icon",children:e.icon}),(0,mt.jsx)("span",{className:"menu-item-label",children:e.label}),e.badge&&(0,mt.jsx)(ft.P.span,{className:"menu-item-badge",initial:{scale:0},animate:{scale:1},transition:{delay:.3},children:e.badge>99?"99+":e.badge})]},e.id)};return(0,mt.jsx)(ft.P.aside,{className:"sidebar",initial:{x:300,opacity:0},animate:{x:0,opacity:1},exit:{x:300,opacity:0},transition:{duration:.3,ease:"easeInOut"},children:(0,mt.jsxs)("div",{className:"sidebar-content",children:[(0,mt.jsxs)("nav",{className:"sidebar-nav",children:[(0,mt.jsxs)("div",{className:"sidebar-section",children:[(0,mt.jsx)("h3",{className:"sidebar-section-title",children:"\u0627\u0644\u062a\u0646\u0642\u0644"}),(0,mt.jsx)("div",{className:"sidebar-menu",children:i.map((e,t)=>a(e,t))})]}),(0,mt.jsxs)("div",{className:"sidebar-section",children:[(0,mt.jsx)("h3",{className:"sidebar-section-title",children:"\u0627\u0644\u0625\u062d\u0635\u0627\u0626\u064a\u0627\u062a"}),(0,mt.jsxs)("div",{className:"sidebar-stats",children:[(0,mt.jsxs)("div",{className:"stat-item",children:[(0,mt.jsx)("div",{className:"stat-icon",children:(0,mt.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M8 5v14l11-7z"})})}),(0,mt.jsxs)("div",{className:"stat-content",children:[(0,mt.jsx)("span",{className:"stat-value",children:t.videosWatched}),(0,mt.jsx)("span",{className:"stat-label",children:"\u0641\u064a\u062f\u064a\u0648 \u0645\u0634\u0627\u0647\u062f"})]})]}),(0,mt.jsxs)("div",{className:"stat-item",children:[(0,mt.jsx)("div",{className:"stat-icon",children:(0,mt.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})})}),(0,mt.jsxs)("div",{className:"stat-content",children:[(0,mt.jsx)("span",{className:"stat-value",children:t.adsBlocked}),(0,mt.jsx)("span",{className:"stat-label",children:"\u0625\u0639\u0644\u0627\u0646 \u0645\u062d\u062c\u0648\u0628"})]})]}),(0,mt.jsxs)("div",{className:"stat-item",children:[(0,mt.jsx)("div",{className:"stat-icon",children:(0,mt.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:[(0,mt.jsx)("path",{d:"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),(0,mt.jsx)("path",{d:"M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"})]})}),(0,mt.jsxs)("div",{className:"stat-content",children:[(0,mt.jsx)("span",{className:"stat-value",children:Math.floor(t.timeSpent/60)}),(0,mt.jsx)("span",{className:"stat-label",children:"\u062f\u0642\u064a\u0642\u0629 \u0645\u0634\u0627\u0647\u062f\u0629"})]})]})]})]})]}),(0,mt.jsx)("div",{className:"sidebar-bottom",children:(0,mt.jsx)("div",{className:"sidebar-menu",children:o.map((e,t)=>a(e,t+i.length))})})]})})};var wt=n(6481);const St=(0,e.lazy)(()=>n.e(695).then(n.bind(n,3695))),kt={home:St,player:(0,e.lazy)(()=>n.e(906).then(n.bind(n,6906))),search:(0,e.lazy)(()=>n.e(781).then(n.bind(n,8781))),settings:(0,e.lazy)(()=>n.e(844).then(n.bind(n,844))),history:(0,e.lazy)(()=>n.e(474).then(n.bind(n,5474))),favorites:(0,e.lazy)(()=>n.e(49).then(n.bind(n,49))),about:(0,e.lazy)(()=>n.e(411).then(n.bind(n,7411)))};const Pt=function(){const{theme:t,language:n,sidebar:r,incrementSessions:i,incrementAdsBlocked:o,isLoading:a,error:s,clearError:l}=(0,ht.A)(),{currentRoute:u,routeParams:c}=(0,pt.Ay)();(0,e.useEffect)(()=>{var e;if((async()=>{try{if(i(),window.electronAPI)try{const e=await window.electronAPI.loadSettings();e&&console.log("Loaded settings:",e)}catch(s){console.warn("Failed to load settings:",s)}document.documentElement.setAttribute("data-theme",t),document.documentElement.setAttribute("lang",n),document.documentElement.setAttribute("dir","ar"===n?"rtl":"ltr"),console.log("App initialized successfully")}catch(s){console.error("Failed to initialize app:",s)}})(),null!==(e=window.electronAPI)&&void 0!==e&&e.onAdBlocked){return window.electronAPI.onAdBlocked((e,t)=>{o(),console.log("Ad blocked:",t)})}},[t,n,i,o]),(0,e.useEffect)(()=>{var e;document.documentElement.setAttribute("data-theme",t),null!==(e=window.electronAPI)&&void 0!==e&&e.storage?window.electronAPI.storage.set("theme",t):localStorage.setItem("youtube-player-pro-theme",t)},[t]),(0,e.useEffect)(()=>{document.documentElement.setAttribute("lang",n),document.documentElement.setAttribute("dir","ar"===n?"rtl":"ltr")},[n]);const d=kt[u]||St,f={initial:{opacity:0,x:"ar"===n?50:-50,scale:.98},in:{opacity:1,x:0,scale:1},out:{opacity:0,x:"ar"===n?-50:50,scale:.98}};return(0,mt.jsxs)("div",{className:"app",children:[(0,mt.jsxs)(J,{children:[(0,mt.jsx)("title",{children:"YouTube Player Pro - \u0645\u0634\u063a\u0644 \u064a\u0648\u062a\u064a\u0648\u0628 \u0627\u062d\u062a\u0631\u0627\u0641\u064a"}),(0,mt.jsx)("meta",{name:"description",content:"\u0645\u0634\u063a\u0644 \u064a\u0648\u062a\u064a\u0648\u0628 \u0627\u062d\u062a\u0631\u0627\u0641\u064a \u0645\u0639 \u0648\u0627\u062c\u0647\u0629 \u0639\u0631\u0628\u064a\u0629 \u0645\u062a\u0642\u062f\u0645\u0629 \u0648\u0645\u0627\u0646\u0639 \u0625\u0639\u0644\u0627\u0646\u0627\u062a \u0642\u0648\u064a"}),(0,mt.jsx)("meta",{name:"theme-color",content:"dark"===t?"#0f0f0f":"#ffffff"})]}),(0,mt.jsx)(bt,{}),(0,mt.jsxs)("div",{className:"app-body",children:[(0,mt.jsx)(dt,{children:r.isOpen&&(0,mt.jsx)(ft.P.div,{className:"sidebar-container",initial:{x:"ar"===n?300:-300,opacity:0},animate:{x:0,opacity:1},exit:{x:"ar"===n?300:-300,opacity:0},transition:{duration:.3,ease:"easeInOut"},children:(0,mt.jsx)(xt,{})})}),(0,mt.jsxs)("main",{className:"main-content "+(r.isOpen?"with-sidebar":"full-width"),children:[s&&(0,mt.jsx)(ft.P.div,{className:"error-banner",initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,y:-50},children:(0,mt.jsxs)("div",{className:"error-content",children:[(0,mt.jsx)("span",{className:"error-message",children:s}),(0,mt.jsx)("button",{className:"error-close",onClick:l,"aria-label":"\u0625\u063a\u0644\u0627\u0642 \u0631\u0633\u0627\u0644\u0629 \u0627\u0644\u062e\u0637\u0623",children:"\xd7"})]})}),a&&(0,mt.jsx)(ft.P.div,{className:"loading-overlay",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,mt.jsx)(wt.A,{})}),(0,mt.jsx)(dt,{mode:"wait",children:(0,mt.jsx)(ft.P.div,{className:"page-container",initial:"initial",animate:"in",exit:"out",variants:f,transition:{type:"tween",ease:"anticipate",duration:.4},children:(0,mt.jsx)(e.Suspense,{fallback:(0,mt.jsx)(wt.A,{}),children:(0,mt.jsx)(d,{routeParams:c})})},u)})]})]}),(0,mt.jsx)("div",{id:"modal-root"}),(0,mt.jsx)("div",{id:"tooltip-root"})]})},Ct=e=>{let{error:t,resetErrorBoundary:n}=e;return(0,mt.jsx)("div",{className:"error-fallback",children:(0,mt.jsxs)(ft.P.div,{className:"error-container",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,mt.jsx)("div",{className:"error-icon",children:(0,mt.jsx)("svg",{width:"64",height:"64",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}),(0,mt.jsx)("h1",{className:"error-title",children:"\u0639\u0630\u0631\u0627\u064b\u060c \u062d\u062f\u062b \u062e\u0637\u0623 \u063a\u064a\u0631 \u0645\u062a\u0648\u0642\u0639"}),(0,mt.jsx)("p",{className:"error-description",children:"\u0648\u0627\u062c\u0647 \u0627\u0644\u062a\u0637\u0628\u064a\u0642 \u0645\u0634\u0643\u0644\u0629 \u062a\u0642\u0646\u064a\u0629. \u0646\u0639\u062a\u0630\u0631 \u0639\u0646 \u0627\u0644\u0625\u0632\u0639\u0627\u062c \u0648\u0646\u0639\u0645\u0644 \u0639\u0644\u0649 \u062d\u0644 \u0647\u0630\u0647 \u0627\u0644\u0645\u0634\u0643\u0644\u0629."}),!1,(0,mt.jsxs)("div",{className:"error-actions",children:[(0,mt.jsxs)(ft.P.button,{className:"error-button primary",onClick:()=>{n()},whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,mt.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"})}),"\u0627\u0644\u0645\u062d\u0627\u0648\u0644\u0629 \u0645\u0631\u0629 \u0623\u062e\u0631\u0649"]}),(0,mt.jsxs)(ft.P.button,{className:"error-button secondary",onClick:()=>{window.location.reload()},whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,mt.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M19 8l-4 4h3c0 3.31-2.69 6-6 6-1.01 0-1.97-.25-2.8-.7l-1.46 1.46C8.97 19.54 10.43 20 12 20c4.42 0 8-3.58 8-8h3l-4-4zM6 12c0-3.31 2.69-6 6-6 1.01 0 1.97.25 2.8.7l1.46-1.46C15.03 4.46 13.57 4 12 4c-4.42 0-8 3.58-8 8H1l4 4 4-4H6z"})}),"\u0625\u0639\u0627\u062f\u0629 \u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u062a\u0637\u0628\u064a\u0642"]}),(0,mt.jsxs)(ft.P.button,{className:"error-button tertiary",onClick:()=>{console.error("Error reported:",t),window.electronAPI&&console.log("Error would be reported to error service")},whileHover:{scale:1.05},whileTap:{scale:.95},children:[(0,mt.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,mt.jsx)("path",{d:"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z"})}),"\u0627\u0644\u0625\u0628\u0644\u0627\u063a \u0639\u0646 \u0627\u0644\u0645\u0634\u0643\u0644\u0629"]})]}),(0,mt.jsxs)("div",{className:"error-tips",children:[(0,mt.jsx)("h3",{children:"\u0646\u0635\u0627\u0626\u062d \u0644\u062d\u0644 \u0627\u0644\u0645\u0634\u0643\u0644\u0629:"}),(0,mt.jsxs)("ul",{children:[(0,mt.jsx)("li",{children:"\u062a\u0623\u0643\u062f \u0645\u0646 \u0627\u062a\u0635\u0627\u0644\u0643 \u0628\u0627\u0644\u0625\u0646\u062a\u0631\u0646\u062a"}),(0,mt.jsx)("li",{children:"\u0623\u063a\u0644\u0642 \u0627\u0644\u062a\u0637\u0628\u064a\u0642 \u0648\u0623\u0639\u062f \u0641\u062a\u062d\u0647"}),(0,mt.jsx)("li",{children:"\u062a\u0623\u0643\u062f \u0645\u0646 \u0623\u0646 \u0644\u062f\u064a\u0643 \u0623\u062d\u062f\u062b \u0625\u0635\u062f\u0627\u0631 \u0645\u0646 \u0627\u0644\u062a\u0637\u0628\u064a\u0642"}),(0,mt.jsx)("li",{children:"\u0627\u0645\u0633\u062d \u0630\u0627\u0643\u0631\u0629 \u0627\u0644\u062a\u062e\u0632\u064a\u0646 \u0627\u0644\u0645\u0624\u0642\u062a \u0644\u0644\u062a\u0637\u0628\u064a\u0642"})]})]})]})})},Et=new r.QueryClient({defaultOptions:{queries:{retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),staleTime:3e5,cacheTime:6e5,refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1}}}),Tt=(e,t)=>{console.error("Application Error:",e),console.error("Error Info:",t),window.electronAPI&&console.error("Electron Error:",{error:e.message,stack:e.stack})},At={duration:4e3,position:"bottom-right",style:{background:"var(--surface-secondary)",color:"var(--text-primary)",border:"1px solid var(--border-primary)",borderRadius:"var(--radius-md)",fontFamily:"var(--font-family-primary)",fontSize:"var(--font-size-sm)",direction:"rtl",textAlign:"right"},success:{iconTheme:{primary:"var(--success-color)",secondary:"var(--surface-secondary)"}},error:{iconTheme:{primary:"var(--error-color)",secondary:"var(--surface-secondary)"}},loading:{iconTheme:{primary:"var(--primary-color)",secondary:"var(--surface-secondary)"}}},jt=()=>(0,mt.jsx)(ne,{FallbackComponent:Ct,onError:Tt,onReset:()=>window.location.reload(),children:(0,mt.jsx)(q,{children:(0,mt.jsxs)(r.QueryClientProvider,{client:Et,children:[(0,mt.jsx)(Pt,{}),(0,mt.jsx)(Ze,{toastOptions:At}),!1]})})}),Ot=t.createRoot(document.getElementById("root"));var Mt,Lt;((()=>{const e=void 0!==window.electronAPI,t=localStorage.getItem("youtube-player-pro-theme"),n=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light",r=t||n;document.documentElement.setAttribute("data-theme",r),document.documentElement.setAttribute("dir","rtl"),document.documentElement.setAttribute("lang","ar");const i=document.getElementById("loading");i&&(i.style.display="none"),console.log(`YouTube Player Pro initialized in ${e?"Electron":"Browser"} environment`)})(),Ot.render((0,mt.jsx)(e.StrictMode,{children:(0,mt.jsx)(jt,{})})),"serviceWorker"in navigator&&window.addEventListener("load",()=>{navigator.serviceWorker.register("/sw.js").then(e=>{console.log("SW registered: ",e)}).catch(e=>{console.log("SW registration failed: ",e)})}),window.electronAPI)&&(null===(Mt=(Lt=window.electronAPI).onWindowStateChange)||void 0===Mt||Mt.call(Lt,(e,t)=>{console.log("Window state changed:",t)}));window.addEventListener("error",e=>{console.error("Global error:",e.error)}),window.addEventListener("unhandledrejection",e=>{console.error("Unhandled promise rejection:",e.reason),e.preventDefault()}),window.addEventListener("beforeunload",()=>{console.log("App is closing...")})})()})();
//# sourceMappingURL=main.6b6bb0c2.js.map