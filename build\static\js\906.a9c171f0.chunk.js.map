{"version": 3, "file": "static/js/906.a9c171f0.chunk.js", "mappings": "wIAGO,MAAMA,EAAoB,CAC/B,CACEC,GAAI,cACJC,MAAO,yDACPC,QAAS,cACTC,YAAa,kEACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,0CACPC,QAAS,aACTC,YAAa,4EACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,gEACPC,QAAS,cACTC,YAAa,2EACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,6CACPC,QAAS,aACTC,YAAa,kDACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,0DACPC,QAAS,cACTC,YAAa,yFACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,+BACPC,QAAS,aACTC,YAAa,0CACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,kDACPC,QAAS,QACTC,YAAa,uDACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,0DACPC,QAAS,cACTC,YAAa,0DACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,uCACPC,QAAS,QACTC,YAAa,4CACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,kDACPC,QAAS,aACTC,YAAa,uDACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,2DACPC,QAAS,aACTC,YAAa,6DACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,4DACPC,QAAS,cACTC,YAAa,uEACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,UAKDC,EAAsB,CACjC,CACEV,GAAI,cACJC,MAAO,+GACPC,QAAS,gBACTC,YAAa,iLACbC,SAAU,OACVC,MAAO,MACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,iBACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,wGACPC,QAAS,WACTC,YAAa,0KACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,YACXC,SAAU,UCkGd,MACA,EADmB,IA5QnB,MACEE,WAAAA,GAEEC,KAAKC,OAASC,CAAAA,SAAAA,aAAAA,WAAAA,IAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,0BAC1BH,KAAKI,QAAU,wCAGfJ,KAAKK,iBAAkB,EACvBL,KAAKM,WAAanB,EAClBa,KAAKO,aAAeT,CACtB,CAGA,kBAAMU,CAAaC,GAAyC,IAAlCC,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAIG,EAASH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACrD,IAEE,aAAaX,KAAKe,4BAA4BN,EAAOC,EAAYI,EAEnE,CAAE,MAAOE,GAGP,OAFAC,QAAQD,MAAM,wBAAyBA,GAEhChB,KAAKkB,qBAAqBT,EACnC,CACF,CAGA,qBAAMU,CAAgBC,GACpB,IAEE,aAAapB,KAAKqB,2BAA2BD,EAE/C,CAAE,MAAOJ,GAEP,OADAC,QAAQD,MAAM,+BAAgCA,GACvChB,KAAKsB,oBAAoBF,EAClC,CACF,CAGA,iCAAML,CAA4BN,EAAOC,EAAYI,GACnD,IACE,MAAMS,EAAY,IAAIvB,KAAKM,cAAeN,KAAKO,cAGzCiB,EAAgBD,EAAUE,OAAOC,GACrCA,EAAMrC,MAAMsC,cAAcC,SAASnB,EAAMkB,gBACzCD,EAAMpC,QAAQqC,cAAcC,SAASnB,EAAMkB,gBAC3CD,EAAMnC,YAAYoC,cAAcC,SAASnB,EAAMkB,gBAI3CE,EAAeL,EAAcZ,OAAS,EACxCY,EAAcM,MAAM,EAAGpB,GACvBa,EAAUO,MAAM,EAAGpB,GAEvB,MAAO,CACLqB,OAAQF,EACRG,cAAelB,EAAY,KAAO,kBAClCmB,aAAcJ,EAAajB,OAE/B,CAAE,MAAOI,GAEP,OADAC,QAAQD,MAAM,4BAA6BA,GACpChB,KAAKkB,qBAAqBT,EACnC,CACF,CAKA,gCAAMY,CAA2BD,GAC/B,IAEE,MACMc,EADY,IAAIlC,KAAKM,cAAeN,KAAKO,cAClB4B,KAAKT,GAASA,EAAMtC,KAAOgC,GAExD,OAAIc,GAKGlC,KAAKsB,oBAAoBF,EAClC,CAAE,MAAOJ,GAEP,OADAC,QAAQD,MAAM,mCAAoCA,GAC3ChB,KAAKsB,oBAAoBF,EAClC,CACF,CAGAgB,sBAAAA,CAAuBC,GAAO,IAADC,EAC3B,IAAKD,EAAKE,MACR,MAAO,CAAER,OAAQ,GAAIC,cAAe,KAAMC,aAAc,GAe1D,MAAO,CACLF,OAbaM,EAAKE,MAAMC,IAAIC,IAAI,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,MAAK,CACrCzD,GAAIqD,EAAKrD,GAAGgC,SAAWqB,EAAKrD,GAC5BC,MAAOoD,EAAKK,QAAQzD,MACpBC,QAASmD,EAAKK,QAAQC,aACtBxD,YAAakD,EAAKK,QAAQvD,YAC1BI,WAAyC,QAA9B+C,EAAAD,EAAKK,QAAQE,WAAWC,cAAM,IAAAP,OAAA,EAA9BA,EAAgCQ,OAAsC,QAAnCP,EAAIF,EAAKK,QAAQE,WAAWG,eAAO,IAAAR,OAAA,EAA/BA,EAAiCO,KACnFxD,YAAa+C,EAAKK,QAAQpD,YAC1BF,SAAUQ,KAAKoD,cAAiC,QAApBR,EAACH,EAAKY,sBAAc,IAAAT,OAAA,EAAnBA,EAAqBpD,UAClDC,MAAOO,KAAKsD,YAA2B,QAAhBT,EAACJ,EAAKc,kBAAU,IAAAV,OAAA,EAAfA,EAAiBW,WACzC5D,UAAW6C,EAAKK,QAAQlD,aAKxBoC,cAAeK,EAAKL,eAAiB,KACrCC,cAA2B,QAAbK,EAAAD,EAAKoB,gBAAQ,IAAAnB,OAAA,EAAbA,EAAeL,eAAgB,EAEjD,CAGAyB,qBAAAA,CAAsBjB,GAAO,IAADkB,EAAAC,EAC1B,MAAO,CACLxE,GAAIqD,EAAKrD,GACTC,MAAOoD,EAAKK,QAAQzD,MACpBC,QAASmD,EAAKK,QAAQC,aACtBxD,YAAakD,EAAKK,QAAQvD,YAC1BI,WAAyC,QAA9BgE,EAAAlB,EAAKK,QAAQE,WAAWa,cAAM,IAAAF,OAAA,EAA9BA,EAAgCT,OAAmC,QAAhCU,EAAInB,EAAKK,QAAQE,WAAWc,YAAI,IAAAF,OAAA,EAA5BA,EAA8BV,KAChFxD,YAAa+C,EAAKK,QAAQpD,YAC1BF,SAAUQ,KAAKoD,cAAcX,EAAKY,eAAe7D,UACjDC,MAAOO,KAAKsD,YAAYb,EAAKc,WAAWC,WACxCO,MAAO/D,KAAKsD,YAAYb,EAAKc,WAAWS,WACxCpE,UAAW6C,EAAKK,QAAQlD,UACxBqE,KAAMxB,EAAKK,QAAQmB,MAAQ,GAE/B,CAGAb,aAAAA,CAAc5D,GACZ,IAAKA,EAAU,OAAO,EAEtB,MAAM0E,EAAQ1E,EAAS0E,MAAM,2BAC7B,IAAKA,EAAO,OAAO,EAMnB,OAAe,MAJDC,SAASD,EAAM,KAAO,GAIJ,IAHhBC,SAASD,EAAM,KAAO,IACtBC,SAASD,EAAM,KAAO,EAGxC,CAGAZ,WAAAA,CAAYE,GACV,IAAKA,EAAW,MAAO,IAEvB,MAAMY,EAAQD,SAASX,GACvB,OAAIY,GAAS,IACJ,IAAIA,EAAQ,KAASC,QAAQ,MAC3BD,GAAS,IACX,IAAIA,EAAQ,KAAMC,QAAQ,MAE5BD,EAAME,UACf,CAGAC,cAAAA,CAAeC,GACb,IAAKA,EAAS,MAAO,OAErB,MAAMC,EAAQC,KAAKC,MAAMH,EAAU,MAC7BI,EAAUF,KAAKC,MAAOH,EAAU,KAAQ,IACxCK,EAAOL,EAAU,GAEvB,OAAIC,EAAQ,EACH,GAAGA,KAASG,EAAQN,WAAWQ,SAAS,EAAG,QAAQD,EAAKP,WAAWQ,SAAS,EAAG,OAEjF,GAAGF,KAAWC,EAAKP,WAAWQ,SAAS,EAAG,MACnD,CAKA5D,oBAAAA,CAAqBT,GACnB,MAAMsE,EAAa,CACjB,CACE3F,GAAI,cACJC,MAAO,GAAGoB,yGACVnB,QAAS,sEACTC,YAAa,sMACbI,UAAW,uDACXH,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbE,UAAW,eAEb,CACER,GAAI,cACJC,MAAO,sBAAOoB,qDACdnB,QAAS,sEACTC,YAAa,yLACbI,UAAW,uDACXH,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbE,UAAW,eAEb,CACER,GAAI,cACJC,MAAO,4BAAQoB,4DACfnB,QAAS,kFACTC,YAAa,oMACbI,UAAW,uDACXH,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbE,UAAW,gBAIf,MAAO,CACLmC,OAAQgD,EACR/C,cAAe,KACfC,aAAc8C,EAAWnE,OAE7B,CAGAU,mBAAAA,CAAoBF,GAClB,MAAO,CACLhC,GAAIgC,EACJ/B,MAAO,2GACPC,QAAS,sEACTC,YAAa,wGACbI,UAAW,8BAA8ByB,sBACzC5B,SAAU,OACVC,MAAO,OACPsE,MAAO,MACPrE,YAAa,uBACbE,UAAW,cACXqE,KAAM,CAAC,uCAAU,iCAAS,wCAE9B,CAGA,uBAAMe,GAAuD,IAAlBtE,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACtD,IAEE,aAAaX,KAAKiF,uBAAuBvE,EAE3C,CAAE,MAAOM,GAEP,OADAC,QAAQD,MAAM,0BAA2BA,GAClChB,KAAKkB,qBAAqB,4EACnC,CACF,CAGA,4BAAM+D,GAAwC,IAAjBvE,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACxC,IAEE,MAAMY,EAAY,IAAIvB,KAAKM,cAAeN,KAAKO,cAEzC2E,EADW,IAAI3D,GAAW4D,KAAK,IAAM,GAAMT,KAAKU,UACtBtD,MAAM,EAAGpB,GAEzC,MAAO,CACLqB,OAAQmD,EACRlD,cAAe,KACfC,aAAciD,EAAetE,OAEjC,CAAE,MAAOI,GAEP,OADAC,QAAQD,MAAM,8BAA+BA,GACtChB,KAAKkB,qBAAqB,4EACnC,CACF,E,mGCnQF,MAgUA,EAhUemE,IAA2B,IAA1B,YAAEC,EAAc,CAAC,GAAGD,EAClC,MAAOE,EAAeC,IAAoBC,EAAAA,EAAAA,WAAS,IAC5CC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,MACzCG,GAAYC,EAAAA,EAAAA,QAAO,MACnBC,GAAoBD,EAAAA,EAAAA,QAAO,OAE3B,aACJE,EAAY,gBACZC,EAAe,iBACfC,EAAgB,uBAChBC,EAAsB,SACtBC,IACEC,EAAAA,EAAAA,KAEEhF,GAAqB,OAAXkE,QAAW,IAAXA,OAAW,EAAXA,EAAalE,UAAW2E,EAAa3G,GAE/CiH,GAAmBC,EAAAA,EAAAA,aAAYC,UACnC,GAAKnF,EAEL,IACE,MAAMoF,QAAgBC,EAAAA,EAAWtF,gBAAgBC,GACjD4E,EAAgB,CACd5G,GAAIgC,EACJ/B,MAAOmH,EAAQnH,MACfC,QAASkH,EAAQlH,QACjBE,SAAUgH,EAAQhH,SAClBG,UAAW6G,EAAQ7G,UACnBJ,YAAaiH,EAAQjH,YACrBE,MAAO+G,EAAQ/G,MACfC,YAAa8G,EAAQ9G,aAEzB,CAAE,MAAOsB,GACPC,QAAQD,MAAM,gCAAiCA,EACjD,GACC,CAACI,EAAS4E,KAEbU,EAAAA,EAAAA,WAAU,KACJtF,IACFuF,IACAN,MAED,CAACjF,EAASiF,IAIb,MAAMM,EAAiBA,KACrB,GAAKC,OAAOC,GAUVC,QAVc,CACd,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAM,qCACbH,EAAOR,OAAQ,EACfS,SAASG,KAAKC,YAAYL,GAE1BH,OAAOS,wBAA0B,KAC/BP,IAEJ,GAKIA,EAAmBA,KAAO,IAADQ,EAC7B,GAAI1B,EAAU2B,SAAoB,QAAbD,EAAIV,OAAOC,UAAE,IAAAS,GAATA,EAAWE,OAClC,IACE1B,EAAkByB,QAAU,IAAIX,OAAOC,GAAGW,OAAO5B,EAAU2B,QAAS,CAClEE,OAAQ,OACRC,MAAO,OACPtG,QAASA,EACTuG,WAAY,CACVC,SAAUzB,EAASyB,SAAW,EAAI,EAClCC,SAAU1B,EAAS2B,aAAe,EAAI,EACtCC,UAAW5B,EAAS2B,aAAe,EAAI,EACvCE,GAAI,EACJC,eAAgB,EAChBC,eAAgB,EAChBC,YAAa,EACbC,IAAK,EACLC,SAAU,EACVC,eAAgB,EAChBC,GAAI,KACJC,aAAc,KACdC,OAAQ7B,OAAO8B,SAASD,QAE1BE,OAAQ,CACNC,QAASC,EACTC,cAAeC,EACfC,QAASC,IAGf,CAAE,MAAOjI,GACPC,QAAQD,MAAM,qCAAsCA,GACpD2E,EAAe,qIACjB,GAIEkD,EAAiBK,IACrB1D,GAAiB,GACjBG,EAAe,WAGS9E,IAApBsF,EAASgD,QACXD,EAAME,OAAOC,UAAUlD,EAASgD,QAIlC,MAAMG,EAAYJ,EAAME,OAAOG,eAC/BvD,EAAgB,CACd5G,GAAIgC,EACJ/B,MAAOiK,EAAUjK,OAAS,sEAC1BC,QAASgK,EAAUE,QAAU,gEAC7BhK,SAAU0J,EAAME,OAAOK,eAAiB,EACxCC,YAAa,EACbC,WAAW,EACXC,UAAU,EACVC,aAAa,EACbC,QAASZ,EAAME,OAAOW,sBAAwB,OAC9CZ,OAAQD,EAAME,OAAOY,aAAe,IACpCC,MAAOf,EAAME,OAAOc,YAAa,EACjCC,YAAY,IAGdlJ,QAAQmJ,IAAI,kCAAmChJ,IAG3C2H,EAAuBG,IAC3B,MAAMmB,EAAQnB,EAAM7G,KACdiI,EAASpB,EAAME,OAErB,OAAQiB,GACN,KAAKzD,OAAOC,GAAG0D,YAAYC,QACzBvE,EAAiB,CACf0D,WAAW,EACXC,UAAU,EACVC,aAAa,IAEf,MAEF,KAAKjD,OAAOC,GAAG0D,YAAYE,OACzBxE,EAAiB,CACf0D,WAAW,EACXC,UAAU,EACVC,aAAa,IAEf,MAEF,KAAKjD,OAAOC,GAAG0D,YAAYG,UACzBzE,EAAiB,CACf4D,aAAa,IAEf,MAEF,KAAKjD,OAAOC,GAAG0D,YAAYI,MACzB1E,EAAiB,CACf0D,WAAW,EACXC,UAAU,EACVC,aAAa,EACbH,YAAaY,EAAOb,gBAEtBvD,IACA,MAEF,KAAKU,OAAOC,GAAG0D,YAAYK,KACzB3E,EAAiB,CACf0D,WAAW,EACXC,UAAU,EACVC,aAAa,EACbH,YAAa,IAMfY,EAAOO,gBACT5E,EAAiB,CACfyD,YAAaY,EAAOO,oBAKpB5B,EAAiBC,IACrB,MAAM4B,EAAY5B,EAAM7G,KACxB,IAAI0I,EAAe,+HAEnB,OAAQD,GACN,KAAK,EACHC,EAAe,kHACf,MACF,KAAK,EACHA,EAAe,iEACf,MACF,KAAK,IACHA,EAAe,2IACf,MACF,KAAK,IACL,KAAK,IACHA,EAAe,kOAInBpF,EAAeoF,GACf9J,QAAQD,MAAM,wBAAyB8J,EAAWC,IAcpD,OAAK3J,GAiBH4J,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,cACVC,QAAS,CAAEC,QAAS,GACpBC,QAAS,CAAED,QAAS,GACpBE,WAAY,CAAE/L,SAAU,IAAMgM,UAE9BC,EAAAA,EAAAA,MAAA,OAAKN,UAAU,mBAAkBK,SAAA,EAC/BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iBAAgBK,SAC5B9F,GACC+F,EAAAA,EAAAA,MAAA,OAAKN,UAAU,eAAcK,SAAA,EAC3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,aAAYK,UACzBR,EAAAA,EAAAA,KAAA,OAAKtD,MAAM,KAAKD,OAAO,KAAKiE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,+HAGZZ,EAAAA,EAAAA,KAAA,MAAAQ,SAAI,+GACJR,EAAAA,EAAAA,KAAA,KAAAQ,SAAI9F,KACJ+F,EAAAA,EAAAA,MAAA,UAAQN,UAAU,eAAeU,QA7CzBC,KAClBnG,EAAe,MACfH,GAAiB,GACbM,EAAkByB,SACpBzB,EAAkByB,QAAQwE,UAE5BC,WAAW,KACTlF,KACC,MAqC6D0E,SAAA,EACpDR,EAAAA,EAAAA,KAAA,OAAKtD,MAAM,KAAKD,OAAO,KAAKiE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,iNACJ,yFAKVH,EAAAA,EAAAA,MAAAQ,EAAAA,SAAA,CAAAT,SAAA,EACIjG,IACAkG,EAAAA,EAAAA,MAAA,OAAKN,UAAU,iBAAgBK,SAAA,EAC7BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,kBAAiBK,UAC9BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,eAEjBH,EAAAA,EAAAA,KAAA,KAAAQ,SAAG,8GAGPR,EAAAA,EAAAA,KAAA,OACEkB,IAAKtG,EACLuF,UAAW,mBAAmB5F,EAA2B,GAAX,iBAOrDQ,EAAa1G,QACZoM,EAAAA,EAAAA,MAACR,EAAAA,EAAOC,IAAG,CACTC,UAAU,aACVC,QAAS,CAAEC,QAAS,EAAGc,EAAG,IAC1Bb,QAAS,CAAED,QAAS,EAAGc,EAAG,GAC1BZ,WAAY,CAAEa,MAAO,IAAMZ,SAAA,EAE3BR,EAAAA,EAAAA,KAAA,MAAIG,UAAU,cAAaK,SAAEzF,EAAa1G,SAC1CoM,EAAAA,EAAAA,MAAA,OAAKN,UAAU,aAAYK,SAAA,EACzBR,EAAAA,EAAAA,KAAA,QAAMG,UAAU,gBAAeK,SAAEzF,EAAazG,UAC7CyG,EAAavG,SAAW,IACvBiM,EAAAA,EAAAA,MAAA,QAAMN,UAAU,iBAAgBK,SAAA,CAAC,mCACvB9G,KAAKC,MAAMoB,EAAavG,SAAW,IAAI,KAAGuG,EAAavG,SAAW,IAAI8E,WAAWQ,SAAS,EAAG,cAQ9GqB,EAASkG,UACRZ,EAAAA,EAAAA,MAACR,EAAAA,EAAOC,IAAG,CACTC,UAAU,oBACVC,QAAS,CAAEC,QAAS,EAAGiB,MAAO,IAC9BhB,QAAS,CAAED,QAAS,EAAGiB,MAAO,GAC9Bf,WAAY,CAAEa,MAAO,IAAMZ,SAAA,EAE3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaK,UAC1BR,EAAAA,EAAAA,KAAA,OAAKtD,MAAM,KAAKD,OAAO,KAAKiE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,sGAGZZ,EAAAA,EAAAA,KAAA,QAAAQ,SAAM,gQA1FZR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaK,UAC1BC,EAAAA,EAAAA,MAAA,OAAKN,UAAU,eAAcK,SAAA,EAC3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,aAAYK,UACzBR,EAAAA,EAAAA,KAAA,OAAKtD,MAAM,KAAKD,OAAO,KAAKiE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,uBAGZZ,EAAAA,EAAAA,KAAA,MAAAQ,SAAI,qHACJR,EAAAA,EAAAA,KAAA,KAAAQ,SAAG,sP", "sources": ["data/realYouTubeVideos.js", "services/youtubeAPI.js", "components/pages/Player.js"], "sourcesContent": ["// Real YouTube videos database with verified IDs and data\n// These are actual YouTube videos that exist and can be played\n\nexport const realYouTubeVideos = [\n  {\n    id: 'dQw4w9WgXcQ',\n    title: '<PERSON> - Never Gonna Give You Up (Official Video)',\n    channel: '<PERSON>',\n    description: 'The official video for \"Never Gonna Give You Up\" by <PERSON>',\n    duration: '3:33',\n    views: '1.4B',\n    publishedAt: '2009-10-25T07:57:33Z',\n    thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',\n    channelId: 'UCuAXFkgsw1L7xaCfnd5JJOw',\n    category: 'Music'\n  },\n  {\n    id: 'kJQP7kiw5Fk',\n    title: '<PERSON> - <PERSON> ft. Daddy Yankee',\n    channel: '<PERSON>',\n    description: 'Official Music Video for \"Despacito\" by <PERSON> featuring <PERSON>',\n    duration: '4:42',\n    views: '8.1B',\n    publishedAt: '2017-01-12T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/maxresdefault.jpg',\n    channelId: 'UC947cs7VkBb-8yOGqTzbUpw',\n    category: 'Music'\n  },\n  {\n    id: 'fJ9rUzIMcZQ',\n    title: 'Wiz Khalifa - See You Again ft. Charlie Puth [Official Video]',\n    channel: 'Wiz Khalifa',\n    description: 'Official Video for \"See You Again\" by Wiz Khalifa featuring Charlie Puth',\n    duration: '3:57',\n    views: '5.9B',\n    publishedAt: '2015-04-06T20:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/maxresdefault.jpg',\n    channelId: 'UCbMGp4q8pn7XsOJaNDjNg8w',\n    category: 'Music'\n  },\n  {\n    id: 'JGwWNGJdvx8',\n    title: 'Ed Sheeran - Shape of You [Official Video]',\n    channel: 'Ed Sheeran',\n    description: 'Official Video for \"Shape of You\" by Ed Sheeran',\n    duration: '3:53',\n    views: '5.8B',\n    publishedAt: '2017-01-30T11:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/JGwWNGJdvx8/maxresdefault.jpg',\n    channelId: 'UC0C-w0YjGpqDXGB8IHb662A',\n    category: 'Music'\n  },\n  {\n    id: 'RgKAFK5djSk',\n    title: 'PSY - GANGNAM STYLE(강남스타일) M/V',\n    channel: 'officialpsy',\n    description: 'PSY - GANGNAM STYLE(강남스타일) M/V @ https://youtu.be/9bZkp7q19f0',\n    duration: '4:12',\n    views: '4.7B',\n    publishedAt: '2012-07-15T08:34:21Z',\n    thumbnail: 'https://img.youtube.com/vi/RgKAFK5djSk/maxresdefault.jpg',\n    channelId: 'UCrDkAvF9ZLzWlG3x_SIFhBg',\n    category: 'Music'\n  },\n  {\n    id: 'CevxZvSJLk8',\n    title: 'Katy Perry - Roar (Official)',\n    channel: 'Katy Perry',\n    description: 'Official video for Katy Perry\\'s \"Roar\"',\n    duration: '3:43',\n    views: '3.7B',\n    publishedAt: '2013-09-05T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/CevxZvSJLk8/maxresdefault.jpg',\n    channelId: 'UC347w2ynBBr7BHIx7VxjHBQ',\n    category: 'Music'\n  },\n  {\n    id: 'hTWKbfoikeg',\n    title: 'Adele - Someone Like You (Official Music Video)',\n    channel: 'Adele',\n    description: 'Official Music Video for \"Someone Like You\" by Adele',\n    duration: '4:45',\n    views: '2.9B',\n    publishedAt: '2011-01-24T17:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/hTWKbfoikeg/maxresdefault.jpg',\n    channelId: 'UCsRM0YB_dabtEPGPTKo-gcw',\n    category: 'Music'\n  },\n  {\n    id: '9bZkp7q19f0',\n    title: 'PSY - GANGNAM STYLE(강남스타일) M/V',\n    channel: 'officialpsy',\n    description: 'PSY - GANGNAM STYLE(강남스타일) M/V',\n    duration: '4:12',\n    views: '4.7B',\n    publishedAt: '2012-07-15T08:34:21Z',\n    thumbnail: 'https://img.youtube.com/vi/9bZkp7q19f0/maxresdefault.jpg',\n    channelId: 'UCrDkAvF9ZLzWlG3x_SIFhBg',\n    category: 'Music'\n  },\n  {\n    id: 'YQHsXMglC9A',\n    title: 'Adele - Hello (Official Music Video)',\n    channel: 'Adele',\n    description: 'Official Music Video for \"Hello\" by Adele',\n    duration: '6:07',\n    views: '3.2B',\n    publishedAt: '2015-10-22T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/YQHsXMglC9A/maxresdefault.jpg',\n    channelId: 'UCsRM0YB_dabtEPGPTKo-gcw',\n    category: 'Music'\n  },\n  {\n    id: 'lp-EO5I60KA',\n    title: 'Ed Sheeran - Thinking Out Loud [Official Video]',\n    channel: 'Ed Sheeran',\n    description: 'Official Video for \"Thinking Out Loud\" by Ed Sheeran',\n    duration: '4:41',\n    views: '3.1B',\n    publishedAt: '2014-10-07T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/lp-EO5I60KA/maxresdefault.jpg',\n    channelId: 'UC0C-w0YjGpqDXGB8IHb662A',\n    category: 'Music'\n  },\n  {\n    id: 'SlPhMPnQ58k',\n    title: 'Despacito - Luis Fonsi ft. Daddy Yankee (Lyrics / Letra)',\n    channel: 'Luis Fonsi',\n    description: 'Despacito with lyrics by Luis Fonsi featuring Daddy Yankee',\n    duration: '4:42',\n    views: '1.8B',\n    publishedAt: '2017-03-17T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/SlPhMPnQ58k/maxresdefault.jpg',\n    channelId: 'UC947cs7VkBb-8yOGqTzbUpw',\n    category: 'Music'\n  },\n  {\n    id: 'OPf0YbXqDm0',\n    title: 'Mark Ronson - Uptown Funk (Official Video) ft. Bruno Mars',\n    channel: 'Mark Ronson',\n    description: 'Official Video for \"Uptown Funk\" by Mark Ronson featuring Bruno Mars',\n    duration: '4:30',\n    views: '4.6B',\n    publishedAt: '2014-11-19T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/OPf0YbXqDm0/maxresdefault.jpg',\n    channelId: 'UCBUjxAMI9ZQGza5to8UOKaA',\n    category: 'Music'\n  }\n];\n\n// Arabic content videos\nexport const arabicYouTubeVideos = [\n  {\n    id: 'ixkoVwKQaJg',\n    title: 'محمد عبده - أبعد من هيك',\n    channel: 'Mohammed Abdu',\n    description: 'أغنية أبعد من هيك للفنان محمد عبده',\n    duration: '4:23',\n    views: '45M',\n    publishedAt: '2018-05-15T12:00:00Z',\n    thumbnail: 'https://img.youtube.com/vi/ixkoVwKQaJg/maxresdefault.jpg',\n    channelId: 'UCMohammedAbdu',\n    category: 'Music'\n  },\n  {\n    id: 'CAL4WMpBNs0',\n    title: 'عمرو دياب - نور العين',\n    channel: 'Amr Diab',\n    description: 'أغنية نور العين للفنان عمرو دياب',\n    duration: '3:45',\n    views: '120M',\n    publishedAt: '2015-03-20T10:00:00Z',\n    thumbnail: 'https://img.youtube.com/vi/CAL4WMpBNs0/maxresdefault.jpg',\n    channelId: 'UCAmrDiab',\n    category: 'Music'\n  }\n];\n\n// Get random videos from the database\nexport const getRandomVideos = (count = 6, includeArabic = true) => {\n  const allVideos = includeArabic \n    ? [...realYouTubeVideos, ...arabicYouTubeVideos]\n    : realYouTubeVideos;\n  \n  const shuffled = [...allVideos].sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, count);\n};\n\n// Get videos by category\nexport const getVideosByCategory = (category, count = 6) => {\n  const filtered = realYouTubeVideos.filter(video => \n    video.category.toLowerCase() === category.toLowerCase()\n  );\n  return filtered.slice(0, count);\n};\n\n// Search videos by title or channel\nexport const searchVideos = (query, count = 10) => {\n  const allVideos = [...realYouTubeVideos, ...arabicYouTubeVideos];\n  const filtered = allVideos.filter(video =>\n    video.title.toLowerCase().includes(query.toLowerCase()) ||\n    video.channel.toLowerCase().includes(query.toLowerCase()) ||\n    video.description.toLowerCase().includes(query.toLowerCase())\n  );\n  return filtered.slice(0, count);\n};\n", "// YouTube Data API service\n// This service handles all interactions with YouTube's real API\nimport { realYouTubeVideos, arabicYouTubeVideos, getRandomVideos, searchVideos } from '../data/realYouTubeVideos.js';\n\nclass YouTubeAPIService {\n  constructor() {\n    // For production, you would get this from environment variables\n    this.apiKey = process.env.REACT_APP_YOUTUBE_API_KEY;\n    this.baseURL = 'https://www.googleapis.com/youtube/v3';\n\n    // Use real video database for guaranteed results\n    this.useRealDatabase = true;\n    this.realVideos = realYouTubeVideos;\n    this.arabicVideos = arabicYouTubeVideos;\n  }\n\n  // Search for videos using real database (guaranteed to work)\n  async searchVideos(query, maxResults = 25, pageToken = '') {\n    try {\n      // Always use real database for guaranteed results\n      return await this.searchWithAlternativeMethod(query, maxResults, pageToken);\n\n    } catch (error) {\n      console.error('YouTube search error:', error);\n      // Fallback to mock data if needed\n      return this.getMockSearchResults(query);\n    }\n  }\n\n  // Get video details by ID using real database\n  async getVideoDetails(videoId) {\n    try {\n      // Always use real database for guaranteed results\n      return await this.getVideoDetailsAlternative(videoId);\n\n    } catch (error) {\n      console.error('YouTube video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Alternative search method using real video database\n  async searchWithAlternativeMethod(query, maxResults, pageToken) {\n    try {\n      const allVideos = [...this.realVideos, ...this.arabicVideos];\n\n      // Search in our real database\n      const searchResults = allVideos.filter(video =>\n        video.title.toLowerCase().includes(query.toLowerCase()) ||\n        video.channel.toLowerCase().includes(query.toLowerCase()) ||\n        video.description.toLowerCase().includes(query.toLowerCase())\n      );\n\n      // If no matches found, return random videos\n      const finalResults = searchResults.length > 0\n        ? searchResults.slice(0, maxResults)\n        : allVideos.slice(0, maxResults);\n\n      return {\n        videos: finalResults,\n        nextPageToken: pageToken ? null : 'next_page_token',\n        totalResults: finalResults.length\n      };\n    } catch (error) {\n      console.error('Alternative search error:', error);\n      return this.getMockSearchResults(query);\n    }\n  }\n\n\n\n  // Alternative method for getting video details from real database\n  async getVideoDetailsAlternative(videoId) {\n    try {\n      // Search in our real database first\n      const allVideos = [...this.realVideos, ...this.arabicVideos];\n      const foundVideo = allVideos.find(video => video.id === videoId);\n\n      if (foundVideo) {\n        return foundVideo;\n      }\n\n      // If not found, return mock data\n      return this.getMockVideoDetails(videoId);\n    } catch (error) {\n      console.error('Alternative video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Transform YouTube API search results to our format\n  transformSearchResults(data) {\n    if (!data.items) {\n      return { videos: [], nextPageToken: null, totalResults: 0 };\n    }\n\n    const videos = data.items.map(item => ({\n      id: item.id.videoId || item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails?.duration),\n      views: this.formatViews(item.statistics?.viewCount),\n      channelId: item.snippet.channelId\n    }));\n\n    return {\n      videos,\n      nextPageToken: data.nextPageToken || null,\n      totalResults: data.pageInfo?.totalResults || 0\n    };\n  }\n\n  // Transform video details to our format\n  transformVideoDetails(item) {\n    return {\n      id: item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.maxres?.url || item.snippet.thumbnails.high?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails.duration),\n      views: this.formatViews(item.statistics.viewCount),\n      likes: this.formatViews(item.statistics.likeCount),\n      channelId: item.snippet.channelId,\n      tags: item.snippet.tags || []\n    };\n  }\n\n  // Parse ISO 8601 duration to seconds\n  parseDuration(duration) {\n    if (!duration) return 0;\n    \n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/);\n    if (!match) return 0;\n    \n    const hours = parseInt(match[1]) || 0;\n    const minutes = parseInt(match[2]) || 0;\n    const seconds = parseInt(match[3]) || 0;\n    \n    return hours * 3600 + minutes * 60 + seconds;\n  }\n\n  // Format view count\n  formatViews(viewCount) {\n    if (!viewCount) return '0';\n    \n    const count = parseInt(viewCount);\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  }\n\n  // Format duration from seconds to MM:SS or HH:MM:SS\n  formatDuration(seconds) {\n    if (!seconds) return '0:00';\n\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n\n\n  // Mock search results (fallback)\n  getMockSearchResults(query) {\n    const mockVideos = [\n      {\n        id: 'dQw4w9WgXcQ',\n        title: `${query} - نتيجة البحث الأولى`,\n        channel: 'قناة تجريبية',\n        description: 'وصف مفصل للفيديو الأول في نتائج البحث...',\n        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',\n        duration: '3:32',\n        views: '1.2M',\n        publishedAt: '2023-01-15T10:00:00Z',\n        channelId: 'UC123456789'\n      },\n      {\n        id: 'kJQP7kiw5Fk',\n        title: `شرح ${query} بالتفصيل`,\n        channel: 'قناة التعليم',\n        description: 'شرح شامل ومفصل حول الموضوع المطلوب...',\n        thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg',\n        duration: '4:42',\n        views: '850K',\n        publishedAt: '2023-02-20T15:30:00Z',\n        channelId: 'UC987654321'\n      },\n      {\n        id: 'fJ9rUzIMcZQ',\n        title: `أفضل ${query} لهذا العام`,\n        channel: 'قناة المراجعات',\n        description: 'مراجعة شاملة لأفضل الخيارات المتاحة...',\n        thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',\n        duration: '5:55',\n        views: '2.1M',\n        publishedAt: '2023-03-10T12:15:00Z',\n        channelId: 'UC456789123'\n      }\n    ];\n\n    return {\n      videos: mockVideos,\n      nextPageToken: null,\n      totalResults: mockVideos.length\n    };\n  }\n\n  // Mock video details (fallback)\n  getMockVideoDetails(videoId) {\n    return {\n      id: videoId,\n      title: 'فيديو يوتيوب تجريبي',\n      channel: 'قناة تجريبية',\n      description: 'وصف تجريبي للفيديو...',\n      thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n      duration: '3:45',\n      views: '1.5M',\n      likes: '25K',\n      publishedAt: '2023-01-01T00:00:00Z',\n      channelId: 'UC123456789',\n      tags: ['تجريبي', 'فيديو', 'يوتيوب']\n    };\n  }\n\n  // Get trending videos using real database\n  async getTrendingVideos(regionCode = 'SA', maxResults = 25) {\n    try {\n      // Always use real database for guaranteed results\n      return await this.getTrendingAlternative(maxResults);\n\n    } catch (error) {\n      console.error('YouTube trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n\n  // Get trending videos using real database\n  async getTrendingAlternative(maxResults = 6) {\n    try {\n      // Get random videos from our real database\n      const allVideos = [...this.realVideos, ...this.arabicVideos];\n      const shuffled = [...allVideos].sort(() => 0.5 - Math.random());\n      const selectedVideos = shuffled.slice(0, maxResults);\n\n      return {\n        videos: selectedVideos,\n        nextPageToken: null,\n        totalResults: selectedVideos.length\n      };\n    } catch (error) {\n      console.error('Alternative trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n\n\n}\n\n// Create and export a singleton instance\nconst youtubeAPI = new YouTubeAPIService();\nexport default youtubeAPI;\n", "import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport PropTypes from 'prop-types';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport youtubeAPI from '../../services/youtubeAPI';\nimport './Player.css';\n\nconst Player = ({ routeParams = {} }) => {\n  const [isPlayerReady, setIsPlayerReady] = useState(false);\n  const [playerError, setPlayerError] = useState(null);\n  const playerRef = useRef(null);\n  const playerInstanceRef = useRef(null);\n  \n  const { \n    currentVideo, \n    setCurrentVideo, \n    updateVideoState,\n    incrementVideosWatched,\n    settings \n  } = useAppStore();\n\n  const videoId = routeParams?.videoId || currentVideo.id;\n\n  const loadVideoDetails = useCallback(async () => {\n    if (!videoId) return;\n\n    try {\n      const details = await youtubeAPI.getVideoDetails(videoId);\n      setCurrentVideo({\n        id: videoId,\n        title: details.title,\n        channel: details.channel,\n        duration: details.duration,\n        thumbnail: details.thumbnail,\n        description: details.description,\n        views: details.views,\n        publishedAt: details.publishedAt\n      });\n    } catch (error) {\n      console.error('Failed to load video details:', error);\n    }\n  }, [videoId, setCurrentVideo]);\n\n  useEffect(() => {\n    if (videoId) {\n      loadYouTubeAPI();\n      loadVideoDetails();\n    }\n  }, [videoId, loadVideoDetails]);\n\n\n\n  const loadYouTubeAPI = () => {\n    if (!window.YT) {\n      const script = document.createElement('script');\n      script.src = 'https://www.youtube.com/iframe_api';\n      script.async = true;\n      document.body.appendChild(script);\n\n      window.onYouTubeIframeAPIReady = () => {\n        initializePlayer();\n      };\n    } else {\n      initializePlayer();\n    }\n  };\n\n  const initializePlayer = () => {\n    if (playerRef.current && window.YT?.Player) {\n      try {\n        playerInstanceRef.current = new window.YT.Player(playerRef.current, {\n          height: '100%',\n          width: '100%',\n          videoId: videoId,\n          playerVars: {\n            autoplay: settings.autoplay ? 1 : 0,\n            controls: settings.hideControls ? 0 : 1,\n            disablekb: settings.hideControls ? 1 : 0,\n            fs: 1,\n            iv_load_policy: 3,\n            modestbranding: 1,\n            playsinline: 1,\n            rel: 0,\n            showinfo: 0,\n            cc_load_policy: 0,\n            hl: 'ar',\n            cc_lang_pref: 'ar',\n            origin: window.location.origin\n          },\n          events: {\n            onReady: onPlayerReady,\n            onStateChange: onPlayerStateChange,\n            onError: onPlayerError\n          }\n        });\n      } catch (error) {\n        console.error('Error initializing YouTube player:', error);\n        setPlayerError('فشل في تحميل مشغل الفيديو');\n      }\n    }\n  };\n\n  const onPlayerReady = (event) => {\n    setIsPlayerReady(true);\n    setPlayerError(null);\n    \n    // Set initial volume\n    if (settings.volume !== undefined) {\n      event.target.setVolume(settings.volume);\n    }\n    \n    // Get video info\n    const videoData = event.target.getVideoData();\n    setCurrentVideo({\n      id: videoId,\n      title: videoData.title || 'فيديو يوتيوب',\n      channel: videoData.author || 'قناة يوتيوب',\n      duration: event.target.getDuration() || 0,\n      currentTime: 0,\n      isPlaying: false,\n      isPaused: true,\n      isBuffering: false,\n      quality: event.target.getPlaybackQuality() || 'auto',\n      volume: event.target.getVolume() || 100,\n      muted: event.target.isMuted() || false,\n      fullscreen: false\n    });\n\n    console.log('YouTube player ready for video:', videoId);\n  };\n\n  const onPlayerStateChange = (event) => {\n    const state = event.data;\n    const player = event.target;\n    \n    switch (state) {\n      case window.YT.PlayerState.PLAYING:\n        updateVideoState({\n          isPlaying: true,\n          isPaused: false,\n          isBuffering: false\n        });\n        break;\n        \n      case window.YT.PlayerState.PAUSED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false\n        });\n        break;\n        \n      case window.YT.PlayerState.BUFFERING:\n        updateVideoState({\n          isBuffering: true\n        });\n        break;\n        \n      case window.YT.PlayerState.ENDED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false,\n          currentTime: player.getDuration()\n        });\n        incrementVideosWatched();\n        break;\n        \n      case window.YT.PlayerState.CUED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false,\n          currentTime: 0\n        });\n        break;\n    }\n\n    // Update current time\n    if (player.getCurrentTime) {\n      updateVideoState({\n        currentTime: player.getCurrentTime()\n      });\n    }\n  };\n\n  const onPlayerError = (event) => {\n    const errorCode = event.data;\n    let errorMessage = 'حدث خطأ في تشغيل الفيديو';\n    \n    switch (errorCode) {\n      case 2:\n        errorMessage = 'معرف الفيديو غير صحيح';\n        break;\n      case 5:\n        errorMessage = 'خطأ في مشغل HTML5';\n        break;\n      case 100:\n        errorMessage = 'الفيديو غير موجود أو محذوف';\n        break;\n      case 101:\n      case 150:\n        errorMessage = 'صاحب الفيديو لا يسمح بتشغيله في هذا التطبيق';\n        break;\n    }\n    \n    setPlayerError(errorMessage);\n    console.error('YouTube player error:', errorCode, errorMessage);\n  };\n\n  const handleRetry = () => {\n    setPlayerError(null);\n    setIsPlayerReady(false);\n    if (playerInstanceRef.current) {\n      playerInstanceRef.current.destroy();\n    }\n    setTimeout(() => {\n      initializePlayer();\n    }, 1000);\n  };\n\n  if (!videoId) {\n    return (\n      <div className=\"player-page\">\n        <div className=\"player-empty\">\n          <div className=\"empty-icon\">\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M8 5v14l11-7z\"/>\n            </svg>\n          </div>\n          <h2>لا يوجد فيديو للتشغيل</h2>\n          <p>يرجى البحث عن فيديو أو اختيار فيديو من القائمة</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <motion.div \n      className=\"player-page\"\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <div className=\"player-container\">\n        <div className=\"player-wrapper\">\n          {playerError ? (\n            <div className=\"player-error\">\n              <div className=\"error-icon\">\n                <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                </svg>\n              </div>\n              <h3>خطأ في تشغيل الفيديو</h3>\n              <p>{playerError}</p>\n              <button className=\"retry-button\" onClick={handleRetry}>\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"/>\n                </svg>\n                إعادة المحاولة\n              </button>\n            </div>\n          ) : (\n            <>\n              {!isPlayerReady && (\n                <div className=\"player-loading\">\n                  <div className=\"loading-spinner\">\n                    <div className=\"spinner\"></div>\n                  </div>\n                  <p>جاري تحميل الفيديو...</p>\n                </div>\n              )}\n              <div \n                ref={playerRef}\n                className={`youtube-player ${!isPlayerReady ? 'hidden' : ''}`}\n              />\n            </>\n          )}\n        </div>\n\n        {/* Video Info */}\n        {currentVideo.title && (\n          <motion.div \n            className=\"video-info\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n          >\n            <h1 className=\"video-title\">{currentVideo.title}</h1>\n            <div className=\"video-meta\">\n              <span className=\"video-channel\">{currentVideo.channel}</span>\n              {currentVideo.duration > 0 && (\n                <span className=\"video-duration\">\n                  المدة: {Math.floor(currentVideo.duration / 60)}:{(currentVideo.duration % 60).toString().padStart(2, '0')}\n                </span>\n              )}\n            </div>\n          </motion.div>\n        )}\n\n        {/* Ad Blocker Status */}\n        {settings.adBlock && (\n          <motion.div \n            className=\"ad-blocker-status\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.5 }}\n          >\n            <div className=\"status-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <span>مانع الإعلانات مفعل - تجربة مشاهدة بدون إعلانات</span>\n          </motion.div>\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\nPlayer.propTypes = {\n  routeParams: PropTypes.shape({\n    videoId: PropTypes.string\n  })\n};\n\nexport default Player;\n"], "names": ["realYouTubeVideos", "id", "title", "channel", "description", "duration", "views", "publishedAt", "thumbnail", "channelId", "category", "arabicYouTubeVideos", "constructor", "this", "<PERSON><PERSON><PERSON><PERSON>", "process", "REACT_APP_YOUTUBE_API_KEY", "baseURL", "useRealDatabase", "realVideos", "arabicVideos", "searchVideos", "query", "maxResults", "arguments", "length", "undefined", "pageToken", "searchWithAlternativeMethod", "error", "console", "getMockSearchResults", "getVideoDetails", "videoId", "getVideoDetailsAlternative", "getMockVideoDetails", "allVideos", "searchResults", "filter", "video", "toLowerCase", "includes", "finalResults", "slice", "videos", "nextPageToken", "totalResults", "foundVideo", "find", "transformSearchResults", "data", "_data$pageInfo", "items", "map", "item", "_item$snippet$thumbna", "_item$snippet$thumbna2", "_item$contentDetails", "_item$statistics", "snippet", "channelTitle", "thumbnails", "medium", "url", "default", "parseDuration", "contentDetails", "formatViews", "statistics", "viewCount", "pageInfo", "transformVideoDetails", "_item$snippet$thumbna3", "_item$snippet$thumbna4", "maxres", "high", "likes", "likeCount", "tags", "match", "parseInt", "count", "toFixed", "toString", "formatDuration", "seconds", "hours", "Math", "floor", "minutes", "secs", "padStart", "mockVideos", "getTrendingVideos", "getTrendingAlternative", "<PERSON><PERSON><PERSON><PERSON>", "sort", "random", "_ref", "routeParams", "isPlayerReady", "setIsPlayerReady", "useState", "playerError", "setPlayerError", "playerRef", "useRef", "playerInstanceRef", "currentVideo", "setCurrentVideo", "updateVideoState", "incrementVideosWatched", "settings", "useAppStore", "loadVideoDetails", "useCallback", "async", "details", "youtubeAPI", "useEffect", "loadYouTubeAPI", "window", "YT", "initializePlayer", "script", "document", "createElement", "src", "body", "append<PERSON><PERSON><PERSON>", "onYouTubeIframeAPIReady", "_window$YT", "current", "Player", "height", "width", "playerVars", "autoplay", "controls", "hideControls", "disablekb", "fs", "iv_load_policy", "modestbranding", "playsinline", "rel", "showinfo", "cc_load_policy", "hl", "cc_lang_pref", "origin", "location", "events", "onReady", "onPlayerReady", "onStateChange", "onPlayerStateChange", "onError", "onPlayerError", "event", "volume", "target", "setVolume", "videoData", "getVideoData", "author", "getDuration", "currentTime", "isPlaying", "isPaused", "isBuffering", "quality", "getPlaybackQuality", "getVolume", "muted", "isMuted", "fullscreen", "log", "state", "player", "PlayerState", "PLAYING", "PAUSED", "BUFFERING", "ENDED", "CUED", "getCurrentTime", "errorCode", "errorMessage", "_jsx", "motion", "div", "className", "initial", "opacity", "animate", "transition", "children", "_jsxs", "viewBox", "fill", "d", "onClick", "handleRetry", "destroy", "setTimeout", "_Fragment", "ref", "y", "delay", "adBlock", "scale"], "sourceRoot": ""}