import { create } from 'zustand';

// Simple router store for navigation
const useRouter = create((set, get) => ({
  // Current route state
  currentRoute: 'home',
  previousRoute: null,
  routeParams: {},
  routeHistory: ['home'],
  
  // Available routes
  routes: {
    home: {
      path: '/',
      component: 'Home',
      title: 'الرئيسية',
      icon: 'home'
    },
    player: {
      path: '/player/:videoId?',
      component: 'Player',
      title: 'المشغل',
      icon: 'play'
    },
    search: {
      path: '/search/:query?',
      component: 'Search',
      title: 'البحث',
      icon: 'search'
    },
    settings: {
      path: '/settings/:section?',
      component: 'Settings',
      title: 'الإعدادات',
      icon: 'settings'
    },
    history: {
      path: '/history',
      component: 'History',
      title: 'السجل',
      icon: 'history'
    },
    favorites: {
      path: '/favorites',
      component: 'Favorites',
      title: 'المفضلة',
      icon: 'heart'
    },
    about: {
      path: '/about',
      component: 'About',
      title: 'حول التطبيق',
      icon: 'info'
    }
  },
  
  // Navigation actions
  navigate: (route, params = {}) => {
    const state = get();
    const previousRoute = state.currentRoute;
    
    set((state) => ({
      previousRoute,
      currentRoute: route,
      routeParams: params,
      routeHistory: [...state.routeHistory, route].slice(-10) // Keep last 10 routes
    }));
    
    // Update document title
    const routeConfig = state.routes[route];
    if (routeConfig) {
      document.title = `${routeConfig.title} - YouTube Player Pro`;
    }
  },
  
  goBack: () => {
    const state = get();
    if (state.routeHistory.length > 1) {
      const previousRoute = state.routeHistory[state.routeHistory.length - 2];
      set((state) => ({
        currentRoute: previousRoute,
        routeHistory: state.routeHistory.slice(0, -1)
      }));
    }
  },
  
  goHome: () => {
    get().navigate('home');
  },
  
  // Route utilities
  getCurrentRoute: () => {
    const state = get();
    return state.routes[state.currentRoute];
  },
  
  isCurrentRoute: (route) => {
    return get().currentRoute === route;
  },
  
  getRouteTitle: (route) => {
    const state = get();
    return state.routes[route]?.title || route;
  },
  
  // URL utilities (for future web version)
  buildUrl: (route, params = {}) => {
    const state = get();
    const routeConfig = state.routes[route];
    if (!routeConfig) return '/';
    
    let url = routeConfig.path;
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`:${key}?`, value).replace(`:${key}`, value);
    });
    
    // Remove optional parameters that weren't replaced
    url = url.replace(/\/:[^/]+\?/g, '');
    
    return url;
  },
  
  parseUrl: (url) => {
    const state = get();
    
    // Find matching route
    for (const [routeName, routeConfig] of Object.entries(state.routes)) {
      const pattern = routeConfig.path
        .replace(/:[^/]+\?/g, '([^/]*)')  // Optional parameters
        .replace(/:[^/]+/g, '([^/]+)');   // Required parameters
      
      const regex = new RegExp(`^${pattern}$`);
      const match = url.match(regex);
      
      if (match) {
        // Extract parameters
        const paramNames = (routeConfig.path.match(/:[^/]+/g) || [])
          .map(param => param.slice(1).replace('?', ''));
        
        const params = {};
        paramNames.forEach((name, index) => {
          if (match[index + 1]) {
            params[name] = decodeURIComponent(match[index + 1]);
          }
        });
        
        return { route: routeName, params };
      }
    }
    
    return { route: 'home', params: {} };
  }
}));

// Navigation helper functions
export const router = {
  navigate: (route, params) => useRouter.getState().navigate(route, params),
  goBack: () => useRouter.getState().goBack(),
  goHome: () => useRouter.getState().goHome(),
  getCurrentRoute: () => useRouter.getState().getCurrentRoute(),
  isCurrentRoute: (route) => useRouter.getState().isCurrentRoute(route),
  getRouteTitle: (route) => useRouter.getState().getRouteTitle(route)
};

// Route components mapping
export const routeComponents = {
  Home: () => import('../components/pages/Home'),
  Player: () => import('../components/pages/Player'),
  Search: () => import('../components/pages/Search'),
  Settings: () => import('../components/pages/Settings'),
  History: () => import('../components/pages/History'),
  Favorites: () => import('../components/pages/Favorites'),
  About: () => import('../components/pages/About')
};

export default useRouter;
