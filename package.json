{"name": "youtube-player-pro", "version": "2.0.0", "description": "مشغل يوتيوب احترافي مع واجهة عربية متقدمة ومانع إعلانات قوي ونظام ربح مدمج", "main": "public/electron.js", "homepage": "./", "author": {"name": "YouTube Player Pro Team", "email": "<EMAIL>"}, "license": "MIT", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --watchAll=false", "test:watch": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "eject": "react-scripts eject", "electron": "electron .", "electron:dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron:pack": "npm run build && electron-builder", "electron:dist": "npm run build && electron-builder --publish=never", "electron:dist-win": "npm run build && electron-builder --win --publish=never", "electron:dist-mac": "npm run build && electron-builder --mac --publish=never", "electron:dist-linux": "npm run build && electron-builder --linux --publish=never", "preelectron:pack": "npm run build", "preelectron:dist": "npm run build", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,scss,json}", "analyze": "npm run build && npx webpack-bundle-analyzer build/static/js/*.js", "clean": "rimraf build dist node_modules/.cache", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "react-query": "^3.39.3", "zustand": "^4.3.6", "axios": "^1.3.4", "lodash": "^4.17.21", "classnames": "^2.3.2", "react-hot-toast": "^2.4.0", "react-icons": "^4.7.1", "framer-motion": "^10.0.1", "react-helmet-async": "^1.3.0", "react-error-boundary": "^4.0.10", "i18next": "^22.4.10", "react-i18next": "^12.1.5", "date-fns": "^2.29.3", "uuid": "^9.0.0"}, "devDependencies": {"react-scripts": "5.0.1", "electron": "^23.1.1", "electron-builder": "^23.6.0", "concurrently": "^7.6.0", "wait-on": "^7.0.1", "cross-env": "^7.0.3", "eslint": "^8.34.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.4", "rimraf": "^4.1.2", "webpack-bundle-analyzer": "^4.8.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/user-event": "^14.4.3", "jest": "^27.5.1"}, "build": {"appId": "com.youtubeplayerpro.app", "productName": "YouTube Player Pro", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "public/preload.js", "node_modules/**/*"], "mac": {"category": "public.app-category.entertainment", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "prettier"], "rules": {"no-console": "warn", "no-unused-vars": "warn"}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/index.js", "!src/reportWebVitals.js"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}}