{"name": "youtube-player-app", "version": "1.0.0", "description": "مشغل يوتيوب مخصص مع واجهة عربية وثيم داكن ومانع إعلانات", "main": "public/electron.js", "homepage": "./", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "set ELECTRON_IS_DEV=1 && electron .", "build-electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "dist-win": "npm run build && electron-builder --win --publish=never", "pack": "npm run build && electron-builder --dir"}, "keywords": ["youtube", "player", "arabic", "dark-theme", "electron", "adblock"], "author": {"name": "YouTube Player Dev<PERSON>per", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^32.2.7", "electron-builder": "^25.1.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-scripts": "5.0.1"}, "dependencies": {"axios": "^1.7.9"}, "build": {"appId": "com.youtube.player.app", "productName": "مشغل يوتيوب", "copyright": "Copyright © 2024 YouTube Player Developer", "directories": {"output": "dist", "buildResources": "build-resources"}, "files": ["build/**/*", "src/main/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "src/assets", "to": "assets"}], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "src/assets/icon.ico", "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-${version}-${arch}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "installerIcon": "src/assets/icon.ico", "uninstallerIcon": "src/assets/icon.ico", "installerHeaderIcon": "src/assets/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "مشغل يوتيوب", "include": "installer.nsh"}, "portable": {"artifactName": "${productName}-${version}-Portable.${ext}"}, "mac": {"target": "dmg", "icon": "src/assets/icon.icns", "category": "public.app-category.entertainment"}, "linux": {"target": ["AppImage", "deb"], "icon": "src/assets/icon.png", "category": "AudioVideo"}, "publish": {"provider": "github", "owner": "youtube-player", "repo": "youtube-player-app"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}