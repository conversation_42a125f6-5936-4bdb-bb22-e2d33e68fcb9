{"version": 3, "file": "static/js/49.6bbb4c8f.chunk.js", "mappings": "iLAIA,MAyBA,EAzBkBA,KAEdC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,iBAAgBC,UAC7BC,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTJ,UAAU,sBACVK,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAAMT,SAAA,EAE9BF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,gDAEJC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBC,SAAA,EAC9BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,UACzBF,EAAAA,EAAAA,KAAA,OAAKY,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcb,UACjEF,EAAAA,EAAAA,KAAA,QAAMgB,EAAE,wLAGZhB,EAAAA,EAAAA,KAAA,MAAAE,SAAI,2HACJF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,kL", "sources": ["components/pages/Favorites.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport './Favorites.css';\n\nconst Favorites = () => {\n  return (\n    <div className=\"favorites-page\">\n      <motion.div \n        className=\"favorites-container\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        <h1>المفضلة</h1>\n        \n        <div className=\"favorites-empty\">\n          <div className=\"empty-icon\">\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/>\n            </svg>\n          </div>\n          <h2>لا توجد فيديوهات مفضلة</h2>\n          <p>أضف فيديوهاتك المفضلة لتظهر هنا</p>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default Favorites;\n"], "names": ["Favorites", "_jsx", "className", "children", "_jsxs", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "width", "height", "viewBox", "fill", "d"], "sourceRoot": ""}