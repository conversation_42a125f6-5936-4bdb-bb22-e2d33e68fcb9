"use strict";(self.webpackChunkyoutube_player_pro=self.webpackChunkyoutube_player_pro||[]).push([[844],{844:(e,s,t)=>{t.r(s),t.d(s,{default:()=>c});t(43);var i=t(228),n=t(663),a=t(579);const c=()=>{const{settings:e,updateSettings:s,resetSettings:t,theme:c,setTheme:l,stats:d,resetStats:h}=(0,n.A)(),r=(e,t)=>{s({[e]:t})},o=e=>{l(e)};return(0,a.jsx)("div",{className:"settings-page",children:(0,a.jsxs)(i.P.div,{className:"settings-container",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,a.jsx)("h1",{children:"\u0627\u0644\u0625\u0639\u062f\u0627\u062f\u0627\u062a"}),(0,a.jsxs)("section",{className:"settings-section",children:[(0,a.jsx)("h2",{children:"\u0625\u0639\u062f\u0627\u062f\u0627\u062a \u0627\u0644\u0641\u064a\u062f\u064a\u0648"}),(0,a.jsxs)("div",{className:"setting-item",children:[(0,a.jsx)("label",{children:"\u062c\u0648\u062f\u0629 \u0627\u0644\u0641\u064a\u062f\u064a\u0648 \u0627\u0644\u0627\u0641\u062a\u0631\u0627\u0636\u064a\u0629"}),(0,a.jsxs)("select",{value:e.quality,onChange:e=>r("quality",e.target.value),children:[(0,a.jsx)("option",{value:"auto",children:"\u062a\u0644\u0642\u0627\u0626\u064a"}),(0,a.jsx)("option",{value:"hd1080",children:"1080p"}),(0,a.jsx)("option",{value:"hd720",children:"720p"}),(0,a.jsx)("option",{value:"large",children:"480p"}),(0,a.jsx)("option",{value:"medium",children:"360p"}),(0,a.jsx)("option",{value:"small",children:"240p"})]})]}),(0,a.jsx)("div",{className:"setting-item",children:(0,a.jsxs)("label",{children:[(0,a.jsx)("input",{type:"checkbox",checked:e.autoplay,onChange:e=>r("autoplay",e.target.checked)}),"\u062a\u0634\u063a\u064a\u0644 \u062a\u0644\u0642\u0627\u0626\u064a"]})}),(0,a.jsxs)("div",{className:"setting-item",children:[(0,a.jsx)("label",{children:"\u0645\u0633\u062a\u0648\u0649 \u0627\u0644\u0635\u0648\u062a"}),(0,a.jsx)("input",{type:"range",min:"0",max:"100",value:e.volume,onChange:e=>r("volume",parseInt(e.target.value))}),(0,a.jsxs)("span",{children:[e.volume,"%"]})]})]}),(0,a.jsxs)("section",{className:"settings-section",children:[(0,a.jsx)("h2",{children:"\u0625\u0639\u062f\u0627\u062f\u0627\u062a \u0627\u0644\u0648\u0627\u062c\u0647\u0629"}),(0,a.jsxs)("div",{className:"setting-item",children:[(0,a.jsx)("label",{children:"\u0627\u0644\u0645\u0638\u0647\u0631"}),(0,a.jsxs)("div",{className:"theme-options",children:[(0,a.jsx)("button",{className:"theme-option "+("dark"===c?"active":""),onClick:()=>o("dark"),children:"\u062f\u0627\u0643\u0646"}),(0,a.jsx)("button",{className:"theme-option "+("light"===c?"active":""),onClick:()=>o("light"),children:"\u0641\u0627\u062a\u062d"})]})]}),(0,a.jsx)("div",{className:"setting-item",children:(0,a.jsxs)("label",{children:[(0,a.jsx)("input",{type:"checkbox",checked:e.hideControls,onChange:e=>r("hideControls",e.target.checked)}),"\u0625\u062e\u0641\u0627\u0621 \u0639\u0646\u0627\u0635\u0631 \u0627\u0644\u062a\u062d\u0643\u0645"]})})]}),(0,a.jsxs)("section",{className:"settings-section",children:[(0,a.jsx)("h2",{children:"\u0625\u0639\u062f\u0627\u062f\u0627\u062a \u0627\u0644\u062e\u0635\u0648\u0635\u064a\u0629"}),(0,a.jsx)("div",{className:"setting-item",children:(0,a.jsxs)("label",{children:[(0,a.jsx)("input",{type:"checkbox",checked:e.adBlock,onChange:e=>r("adBlock",e.target.checked)}),"\u062a\u0641\u0639\u064a\u0644 \u0645\u0627\u0646\u0639 \u0627\u0644\u0625\u0639\u0644\u0627\u0646\u0627\u062a"]})}),(0,a.jsx)("div",{className:"setting-item",children:(0,a.jsxs)("label",{children:[(0,a.jsx)("input",{type:"checkbox",checked:e.saveHistory,onChange:e=>r("saveHistory",e.target.checked)}),"\u062d\u0641\u0638 \u0633\u062c\u0644 \u0627\u0644\u0645\u0634\u0627\u0647\u062f\u0629"]})})]}),(0,a.jsxs)("section",{className:"settings-section",children:[(0,a.jsx)("h2",{children:"\u0627\u0644\u0625\u062d\u0635\u0627\u0626\u064a\u0627\u062a"}),(0,a.jsxs)("div",{className:"stats-display",children:[(0,a.jsxs)("div",{className:"stat-item",children:[(0,a.jsx)("span",{children:"\u0627\u0644\u0641\u064a\u062f\u064a\u0648\u0647\u0627\u062a \u0627\u0644\u0645\u0634\u0627\u0647\u062f\u0629:"}),(0,a.jsx)("span",{children:d.videosWatched})]}),(0,a.jsxs)("div",{className:"stat-item",children:[(0,a.jsx)("span",{children:"\u0627\u0644\u0625\u0639\u0644\u0627\u0646\u0627\u062a \u0627\u0644\u0645\u062d\u062c\u0648\u0628\u0629:"}),(0,a.jsx)("span",{children:d.adsBlocked})]}),(0,a.jsxs)("div",{className:"stat-item",children:[(0,a.jsx)("span",{children:"\u0648\u0642\u062a \u0627\u0644\u0645\u0634\u0627\u0647\u062f\u0629:"}),(0,a.jsxs)("span",{children:[Math.floor(d.timeSpent/60)," \u062f\u0642\u064a\u0642\u0629"]})]})]}),(0,a.jsx)("button",{className:"reset-button",onClick:h,children:"\u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646 \u0627\u0644\u0625\u062d\u0635\u0627\u0626\u064a\u0627\u062a"})]}),(0,a.jsxs)("section",{className:"settings-section",children:[(0,a.jsx)("h2",{children:"\u0625\u062c\u0631\u0627\u0621\u0627\u062a"}),(0,a.jsx)("div",{className:"settings-actions",children:(0,a.jsx)("button",{className:"reset-button",onClick:t,children:"\u0625\u0639\u0627\u062f\u0629 \u062a\u0639\u064a\u064a\u0646 \u062c\u0645\u064a\u0639 \u0627\u0644\u0625\u0639\u062f\u0627\u062f\u0627\u062a"})})]})]})})}}}]);
//# sourceMappingURL=844.e3b82b7f.chunk.js.map