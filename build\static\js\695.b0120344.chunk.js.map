{"version": 3, "file": "static/js/695.b0120344.chunk.js", "mappings": "wIAGO,MAAMA,EAAoB,CAC/B,CACEC,GAAI,cACJC,MAAO,yDACPC,QAAS,cACTC,YAAa,kEACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,0CACPC,QAAS,aACTC,YAAa,4EACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,gEACPC,QAAS,cACTC,YAAa,2EACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,6CACPC,QAAS,aACTC,YAAa,kDACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,0DACPC,QAAS,cACTC,YAAa,yFACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,+BACPC,QAAS,aACTC,YAAa,0CACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,kDACPC,QAAS,QACTC,YAAa,uDACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,0DACPC,QAAS,cACTC,YAAa,0DACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,uCACPC,QAAS,QACTC,YAAa,4CACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,kDACPC,QAAS,aACTC,YAAa,uDACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,2DACPC,QAAS,aACTC,YAAa,6DACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,4DACPC,QAAS,cACTC,YAAa,uEACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,UAKDC,EAAsB,CACjC,CACEV,GAAI,cACJC,MAAO,+GACPC,QAAS,gBACTC,YAAa,iLACbC,SAAU,OACVC,MAAO,MACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,iBACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,wGACPC,QAAS,WACTC,YAAa,0KACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,YACXC,SAAU,UCkGd,MACA,EADmB,IA5QnB,MACEE,WAAAA,GAEEC,KAAKC,OAASC,CAAAA,SAAAA,aAAAA,WAAAA,IAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,0BAC1BH,KAAKI,QAAU,wCAGfJ,KAAKK,iBAAkB,EACvBL,KAAKM,WAAanB,EAClBa,KAAKO,aAAeT,CACtB,CAGA,kBAAMU,CAAaC,GAAyC,IAAlCC,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAIG,EAASH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACrD,IAEE,aAAaX,KAAKe,4BAA4BN,EAAOC,EAAYI,EAEnE,CAAE,MAAOE,GAGP,OAFAC,QAAQD,MAAM,wBAAyBA,GAEhChB,KAAKkB,qBAAqBT,EACnC,CACF,CAGA,qBAAMU,CAAgBC,GACpB,IAEE,aAAapB,KAAKqB,2BAA2BD,EAE/C,CAAE,MAAOJ,GAEP,OADAC,QAAQD,MAAM,+BAAgCA,GACvChB,KAAKsB,oBAAoBF,EAClC,CACF,CAGA,iCAAML,CAA4BN,EAAOC,EAAYI,GACnD,IACE,MAAMS,EAAY,IAAIvB,KAAKM,cAAeN,KAAKO,cAGzCiB,EAAgBD,EAAUE,OAAOC,GACrCA,EAAMrC,MAAMsC,cAAcC,SAASnB,EAAMkB,gBACzCD,EAAMpC,QAAQqC,cAAcC,SAASnB,EAAMkB,gBAC3CD,EAAMnC,YAAYoC,cAAcC,SAASnB,EAAMkB,gBAI3CE,EAAeL,EAAcZ,OAAS,EACxCY,EAAcM,MAAM,EAAGpB,GACvBa,EAAUO,MAAM,EAAGpB,GAEvB,MAAO,CACLqB,OAAQF,EACRG,cAAelB,EAAY,KAAO,kBAClCmB,aAAcJ,EAAajB,OAE/B,CAAE,MAAOI,GAEP,OADAC,QAAQD,MAAM,4BAA6BA,GACpChB,KAAKkB,qBAAqBT,EACnC,CACF,CAKA,gCAAMY,CAA2BD,GAC/B,IAEE,MACMc,EADY,IAAIlC,KAAKM,cAAeN,KAAKO,cAClB4B,KAAKT,GAASA,EAAMtC,KAAOgC,GAExD,OAAIc,GAKGlC,KAAKsB,oBAAoBF,EAClC,CAAE,MAAOJ,GAEP,OADAC,QAAQD,MAAM,mCAAoCA,GAC3ChB,KAAKsB,oBAAoBF,EAClC,CACF,CAGAgB,sBAAAA,CAAuBC,GAAO,IAADC,EAC3B,IAAKD,EAAKE,MACR,MAAO,CAAER,OAAQ,GAAIC,cAAe,KAAMC,aAAc,GAe1D,MAAO,CACLF,OAbaM,EAAKE,MAAMC,IAAIC,IAAI,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,MAAK,CACrCzD,GAAIqD,EAAKrD,GAAGgC,SAAWqB,EAAKrD,GAC5BC,MAAOoD,EAAKK,QAAQzD,MACpBC,QAASmD,EAAKK,QAAQC,aACtBxD,YAAakD,EAAKK,QAAQvD,YAC1BI,WAAyC,QAA9B+C,EAAAD,EAAKK,QAAQE,WAAWC,cAAM,IAAAP,OAAA,EAA9BA,EAAgCQ,OAAsC,QAAnCP,EAAIF,EAAKK,QAAQE,WAAWG,eAAO,IAAAR,OAAA,EAA/BA,EAAiCO,KACnFxD,YAAa+C,EAAKK,QAAQpD,YAC1BF,SAAUQ,KAAKoD,cAAiC,QAApBR,EAACH,EAAKY,sBAAc,IAAAT,OAAA,EAAnBA,EAAqBpD,UAClDC,MAAOO,KAAKsD,YAA2B,QAAhBT,EAACJ,EAAKc,kBAAU,IAAAV,OAAA,EAAfA,EAAiBW,WACzC5D,UAAW6C,EAAKK,QAAQlD,aAKxBoC,cAAeK,EAAKL,eAAiB,KACrCC,cAA2B,QAAbK,EAAAD,EAAKoB,gBAAQ,IAAAnB,OAAA,EAAbA,EAAeL,eAAgB,EAEjD,CAGAyB,qBAAAA,CAAsBjB,GAAO,IAADkB,EAAAC,EAC1B,MAAO,CACLxE,GAAIqD,EAAKrD,GACTC,MAAOoD,EAAKK,QAAQzD,MACpBC,QAASmD,EAAKK,QAAQC,aACtBxD,YAAakD,EAAKK,QAAQvD,YAC1BI,WAAyC,QAA9BgE,EAAAlB,EAAKK,QAAQE,WAAWa,cAAM,IAAAF,OAAA,EAA9BA,EAAgCT,OAAmC,QAAhCU,EAAInB,EAAKK,QAAQE,WAAWc,YAAI,IAAAF,OAAA,EAA5BA,EAA8BV,KAChFxD,YAAa+C,EAAKK,QAAQpD,YAC1BF,SAAUQ,KAAKoD,cAAcX,EAAKY,eAAe7D,UACjDC,MAAOO,KAAKsD,YAAYb,EAAKc,WAAWC,WACxCO,MAAO/D,KAAKsD,YAAYb,EAAKc,WAAWS,WACxCpE,UAAW6C,EAAKK,QAAQlD,UACxBqE,KAAMxB,EAAKK,QAAQmB,MAAQ,GAE/B,CAGAb,aAAAA,CAAc5D,GACZ,IAAKA,EAAU,OAAO,EAEtB,MAAM0E,EAAQ1E,EAAS0E,MAAM,2BAC7B,IAAKA,EAAO,OAAO,EAMnB,OAAe,MAJDC,SAASD,EAAM,KAAO,GAIJ,IAHhBC,SAASD,EAAM,KAAO,IACtBC,SAASD,EAAM,KAAO,EAGxC,CAGAZ,WAAAA,CAAYE,GACV,IAAKA,EAAW,MAAO,IAEvB,MAAMY,EAAQD,SAASX,GACvB,OAAIY,GAAS,IACJ,IAAIA,EAAQ,KAASC,QAAQ,MAC3BD,GAAS,IACX,IAAIA,EAAQ,KAAMC,QAAQ,MAE5BD,EAAME,UACf,CAGAC,cAAAA,CAAeC,GACb,IAAKA,EAAS,MAAO,OAErB,MAAMC,EAAQC,KAAKC,MAAMH,EAAU,MAC7BI,EAAUF,KAAKC,MAAOH,EAAU,KAAQ,IACxCK,EAAOL,EAAU,GAEvB,OAAIC,EAAQ,EACH,GAAGA,KAASG,EAAQN,WAAWQ,SAAS,EAAG,QAAQD,EAAKP,WAAWQ,SAAS,EAAG,OAEjF,GAAGF,KAAWC,EAAKP,WAAWQ,SAAS,EAAG,MACnD,CAKA5D,oBAAAA,CAAqBT,GACnB,MAAMsE,EAAa,CACjB,CACE3F,GAAI,cACJC,MAAO,GAAGoB,yGACVnB,QAAS,sEACTC,YAAa,sMACbI,UAAW,uDACXH,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbE,UAAW,eAEb,CACER,GAAI,cACJC,MAAO,sBAAOoB,qDACdnB,QAAS,sEACTC,YAAa,yLACbI,UAAW,uDACXH,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbE,UAAW,eAEb,CACER,GAAI,cACJC,MAAO,4BAAQoB,4DACfnB,QAAS,kFACTC,YAAa,oMACbI,UAAW,uDACXH,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbE,UAAW,gBAIf,MAAO,CACLmC,OAAQgD,EACR/C,cAAe,KACfC,aAAc8C,EAAWnE,OAE7B,CAGAU,mBAAAA,CAAoBF,GAClB,MAAO,CACLhC,GAAIgC,EACJ/B,MAAO,2GACPC,QAAS,sEACTC,YAAa,wGACbI,UAAW,8BAA8ByB,sBACzC5B,SAAU,OACVC,MAAO,OACPsE,MAAO,MACPrE,YAAa,uBACbE,UAAW,cACXqE,KAAM,CAAC,uCAAU,iCAAS,wCAE9B,CAGA,uBAAMe,GAAuD,IAAlBtE,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACtD,IAEE,aAAaX,KAAKiF,uBAAuBvE,EAE3C,CAAE,MAAOM,GAEP,OADAC,QAAQD,MAAM,0BAA2BA,GAClChB,KAAKkB,qBAAqB,4EACnC,CACF,CAGA,4BAAM+D,GAAwC,IAAjBvE,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACxC,IAEE,MAAMY,EAAY,IAAIvB,KAAKM,cAAeN,KAAKO,cAEzC2E,EADW,IAAI3D,GAAW4D,KAAK,IAAM,GAAMT,KAAKU,UACtBtD,MAAM,EAAGpB,GAEzC,MAAO,CACLqB,OAAQmD,EACRlD,cAAe,KACfC,aAAciD,EAAetE,OAEjC,CAAE,MAAOI,GAEP,OADAC,QAAQD,MAAM,8BAA+BA,GACtChB,KAAKkB,qBAAqB,4EACnC,CACF,E,6GCnQF,MA6RA,EA7RamE,KACX,MAAOC,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,KAC9CC,EAAiBC,IAAsBF,EAAAA,EAAAA,WAAS,IAEjD,MAAEG,EAAK,aAAEC,IAAiBC,EAAAA,EAAAA,MAC1B,SAAEC,IAAaC,EAAAA,EAAAA,OAGrBC,EAAAA,EAAAA,WAAU,KACmBC,WACzB,IACEP,GAAmB,GACnB,MAAMQ,QAAeC,EAAAA,EAAWnB,kBAAkB,KAAM,GACxDO,EAAkBW,EAAOnE,OAC3B,CAAE,MAAOf,GACPC,QAAQD,MAAM,kCAAmCA,GAEjDuE,EAAkB,CAChB,CACEnG,GAAI,cACJC,MAAO,0KACPC,QAAS,qBACTE,SAAU,OACVC,MAAO,OACPE,UAAW,wDAEb,CACEP,GAAI,cACJC,MAAO,4JACPC,QAAS,4EACTE,SAAU,OACVC,MAAO,OACPE,UAAW,wDAEb,CACEP,GAAI,cACJC,MAAO,oJACPC,QAAS,4EACTE,SAAU,OACVC,MAAO,OACPE,UAAW,yDAGjB,CAAC,QACC+F,GAAmB,EACrB,GAGFU,IACC,IAEH,MAkBMC,EAAe,CACnBC,OAAQ,CAAEC,QAAS,EAAGC,EAAG,IACzBC,QAAS,CAAEF,QAAS,EAAGC,EAAG,IAG5B,OACEE,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVC,SAlBsB,CACxBR,OAAQ,CAAEC,QAAS,GACnBE,QAAS,CACPF,QAAS,EACTQ,WAAY,CACVC,gBAAiB,MAcnBC,QAAQ,SACRC,QAAQ,UAASC,SAAA,EAGjBT,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,eAAeC,SAAUT,EAAac,SAAA,EAC9DT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAACV,EAAAA,EAAOW,GAAE,CACRT,UAAU,aACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAC5B,6KAGDE,EAAAA,EAAAA,KAACV,EAAAA,EAAOa,EAAC,CACPX,UAAU,mBACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAC5B,qbAGDT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,eACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAAA,EAE3BT,EAAAA,EAAAA,MAACC,EAAAA,EAAOc,OAAM,CACZZ,UAAU,sBACVa,QArDcC,KACxB7B,EAAS,WAqDC8B,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAOV,SAAA,EAE1BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,iPACJ,8DAGRd,EAAAA,EAAAA,KAACV,EAAAA,EAAOc,OAAM,CACZZ,UAAU,wBACVa,QAASA,IAAM5B,EAAS,SACxB8B,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAOV,SAC3B,4FAKLE,EAAAA,EAAAA,KAACV,EAAAA,EAAOC,IAAG,CACTC,UAAU,aACVI,QAAS,CAAEV,QAAS,EAAGsB,MAAO,IAC9BX,QAAS,CAAEX,QAAS,EAAGsB,MAAO,GAC9Bd,WAAY,CAAEQ,MAAO,IAAMJ,UAE3BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,qBAAoBM,SAAA,EACjCE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,uBAAsBM,UACnCE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uBAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,yBAAwBM,SAAA,EACrCE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,oBACfQ,EAAAA,EAAAA,KAAA,OAAKR,UAAU,oBACfQ,EAAAA,EAAAA,KAAA,OAAKR,UAAU,+BAOvBH,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,gBAAgBC,SAAUT,EAAac,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,4DAC9BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,aAAYM,SAAA,EACzBT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBM,UAC/BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uBAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAExB,EAAMyC,iBACrCf,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,yEAIjCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeM,UAC5BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,sGAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAExB,EAAM0C,cACrChB,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,yEAIjCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,iBAAgBM,UAC7BT,EAAAA,EAAAA,MAAA,OAAKqB,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,SAAA,EACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,qJACRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,mDAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAEzC,KAAKC,MAAMgB,EAAM2C,UAAY,OAC5DjB,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,qFAOrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,mBAAmBC,SAAUT,EAAac,SAAA,EAClEE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,oFAC7B1B,GACCiB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iBAAgBM,SAAA,EAC7BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,kBAAiBM,UAC9BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAEjBQ,EAAAA,EAAAA,KAAA,KAAAF,SAAG,2KAGLE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,cAAaM,SACzB7B,EAAe9C,IAAI,CAACd,EAAO6G,KAC5B7B,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAETC,UAAU,aACVC,SAAUT,EACVuB,WAAY,CAAEC,MAAO,KAAMrB,GAAI,GAC/BkB,QAASA,KAAMc,OAvKDpH,EAuKkBM,EAAMtC,QAtKhD0G,EAAS,SAAU,CAAE1E,YADGA,OAuK4B+F,SAAA,EAE1CT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,kBAAiBM,SAAA,EAC9BE,EAAAA,EAAAA,KAAA,OAAKoB,IAAK/G,EAAM/B,UAAW+I,IAAKhH,EAAMrC,MAAOsJ,QAAQ,UACrDtB,EAAAA,EAAAA,KAAA,OAAKR,UAAU,iBAAgBM,SAAEzF,EAAMlC,YACvC6H,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeM,UAC5BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,0BAIdzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,aAAYM,SAAA,EACzBE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,cAAaM,SAAEzF,EAAMrC,SACnCgI,EAAAA,EAAAA,KAAA,KAAGR,UAAU,gBAAeM,SAAEzF,EAAMpC,WACpCoH,EAAAA,EAAAA,MAAA,KAAGG,UAAU,cAAaM,SAAA,CAAEzF,EAAMjC,MAAM,gDAlBrCiC,EAAMtC,WA2BnBsH,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,mBAAmBC,SAAUT,EAAac,SAAA,EAClEE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,uGAC9BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,gBAAeM,SAAA,EAC5BT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,sGAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,4FAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,wRAKrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+KAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,wGAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,mPAKrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uDAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,uDAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,wQ", "sources": ["data/realYouTubeVideos.js", "services/youtubeAPI.js", "components/pages/Home.js"], "sourcesContent": ["// Real YouTube videos database with verified IDs and data\n// These are actual YouTube videos that exist and can be played\n\nexport const realYouTubeVideos = [\n  {\n    id: 'dQw4w9WgXcQ',\n    title: '<PERSON> - Never Gonna Give You Up (Official Video)',\n    channel: '<PERSON>',\n    description: 'The official video for \"Never Gonna Give You Up\" by <PERSON>',\n    duration: '3:33',\n    views: '1.4B',\n    publishedAt: '2009-10-25T07:57:33Z',\n    thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',\n    channelId: 'UCuAXFkgsw1L7xaCfnd5JJOw',\n    category: 'Music'\n  },\n  {\n    id: 'kJQP7kiw5Fk',\n    title: '<PERSON> - <PERSON> ft. Daddy Yankee',\n    channel: '<PERSON>',\n    description: 'Official Music Video for \"Despacito\" by <PERSON> featuring <PERSON>',\n    duration: '4:42',\n    views: '8.1B',\n    publishedAt: '2017-01-12T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/maxresdefault.jpg',\n    channelId: 'UC947cs7VkBb-8yOGqTzbUpw',\n    category: 'Music'\n  },\n  {\n    id: 'fJ9rUzIMcZQ',\n    title: 'Wiz Khalifa - See You Again ft. Charlie Puth [Official Video]',\n    channel: 'Wiz Khalifa',\n    description: 'Official Video for \"See You Again\" by Wiz Khalifa featuring Charlie Puth',\n    duration: '3:57',\n    views: '5.9B',\n    publishedAt: '2015-04-06T20:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/maxresdefault.jpg',\n    channelId: 'UCbMGp4q8pn7XsOJaNDjNg8w',\n    category: 'Music'\n  },\n  {\n    id: 'JGwWNGJdvx8',\n    title: 'Ed Sheeran - Shape of You [Official Video]',\n    channel: 'Ed Sheeran',\n    description: 'Official Video for \"Shape of You\" by Ed Sheeran',\n    duration: '3:53',\n    views: '5.8B',\n    publishedAt: '2017-01-30T11:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/JGwWNGJdvx8/maxresdefault.jpg',\n    channelId: 'UC0C-w0YjGpqDXGB8IHb662A',\n    category: 'Music'\n  },\n  {\n    id: 'RgKAFK5djSk',\n    title: 'PSY - GANGNAM STYLE(강남스타일) M/V',\n    channel: 'officialpsy',\n    description: 'PSY - GANGNAM STYLE(강남스타일) M/V @ https://youtu.be/9bZkp7q19f0',\n    duration: '4:12',\n    views: '4.7B',\n    publishedAt: '2012-07-15T08:34:21Z',\n    thumbnail: 'https://img.youtube.com/vi/RgKAFK5djSk/maxresdefault.jpg',\n    channelId: 'UCrDkAvF9ZLzWlG3x_SIFhBg',\n    category: 'Music'\n  },\n  {\n    id: 'CevxZvSJLk8',\n    title: 'Katy Perry - Roar (Official)',\n    channel: 'Katy Perry',\n    description: 'Official video for Katy Perry\\'s \"Roar\"',\n    duration: '3:43',\n    views: '3.7B',\n    publishedAt: '2013-09-05T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/CevxZvSJLk8/maxresdefault.jpg',\n    channelId: 'UC347w2ynBBr7BHIx7VxjHBQ',\n    category: 'Music'\n  },\n  {\n    id: 'hTWKbfoikeg',\n    title: 'Adele - Someone Like You (Official Music Video)',\n    channel: 'Adele',\n    description: 'Official Music Video for \"Someone Like You\" by Adele',\n    duration: '4:45',\n    views: '2.9B',\n    publishedAt: '2011-01-24T17:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/hTWKbfoikeg/maxresdefault.jpg',\n    channelId: 'UCsRM0YB_dabtEPGPTKo-gcw',\n    category: 'Music'\n  },\n  {\n    id: '9bZkp7q19f0',\n    title: 'PSY - GANGNAM STYLE(강남스타일) M/V',\n    channel: 'officialpsy',\n    description: 'PSY - GANGNAM STYLE(강남스타일) M/V',\n    duration: '4:12',\n    views: '4.7B',\n    publishedAt: '2012-07-15T08:34:21Z',\n    thumbnail: 'https://img.youtube.com/vi/9bZkp7q19f0/maxresdefault.jpg',\n    channelId: 'UCrDkAvF9ZLzWlG3x_SIFhBg',\n    category: 'Music'\n  },\n  {\n    id: 'YQHsXMglC9A',\n    title: 'Adele - Hello (Official Music Video)',\n    channel: 'Adele',\n    description: 'Official Music Video for \"Hello\" by Adele',\n    duration: '6:07',\n    views: '3.2B',\n    publishedAt: '2015-10-22T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/YQHsXMglC9A/maxresdefault.jpg',\n    channelId: 'UCsRM0YB_dabtEPGPTKo-gcw',\n    category: 'Music'\n  },\n  {\n    id: 'lp-EO5I60KA',\n    title: 'Ed Sheeran - Thinking Out Loud [Official Video]',\n    channel: 'Ed Sheeran',\n    description: 'Official Video for \"Thinking Out Loud\" by Ed Sheeran',\n    duration: '4:41',\n    views: '3.1B',\n    publishedAt: '2014-10-07T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/lp-EO5I60KA/maxresdefault.jpg',\n    channelId: 'UC0C-w0YjGpqDXGB8IHb662A',\n    category: 'Music'\n  },\n  {\n    id: 'SlPhMPnQ58k',\n    title: 'Despacito - Luis Fonsi ft. Daddy Yankee (Lyrics / Letra)',\n    channel: 'Luis Fonsi',\n    description: 'Despacito with lyrics by Luis Fonsi featuring Daddy Yankee',\n    duration: '4:42',\n    views: '1.8B',\n    publishedAt: '2017-03-17T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/SlPhMPnQ58k/maxresdefault.jpg',\n    channelId: 'UC947cs7VkBb-8yOGqTzbUpw',\n    category: 'Music'\n  },\n  {\n    id: 'OPf0YbXqDm0',\n    title: 'Mark Ronson - Uptown Funk (Official Video) ft. Bruno Mars',\n    channel: 'Mark Ronson',\n    description: 'Official Video for \"Uptown Funk\" by Mark Ronson featuring Bruno Mars',\n    duration: '4:30',\n    views: '4.6B',\n    publishedAt: '2014-11-19T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/OPf0YbXqDm0/maxresdefault.jpg',\n    channelId: 'UCBUjxAMI9ZQGza5to8UOKaA',\n    category: 'Music'\n  }\n];\n\n// Arabic content videos\nexport const arabicYouTubeVideos = [\n  {\n    id: 'ixkoVwKQaJg',\n    title: 'محمد عبده - أبعد من هيك',\n    channel: 'Mohammed Abdu',\n    description: 'أغنية أبعد من هيك للفنان محمد عبده',\n    duration: '4:23',\n    views: '45M',\n    publishedAt: '2018-05-15T12:00:00Z',\n    thumbnail: 'https://img.youtube.com/vi/ixkoVwKQaJg/maxresdefault.jpg',\n    channelId: 'UCMohammedAbdu',\n    category: 'Music'\n  },\n  {\n    id: 'CAL4WMpBNs0',\n    title: 'عمرو دياب - نور العين',\n    channel: 'Amr Diab',\n    description: 'أغنية نور العين للفنان عمرو دياب',\n    duration: '3:45',\n    views: '120M',\n    publishedAt: '2015-03-20T10:00:00Z',\n    thumbnail: 'https://img.youtube.com/vi/CAL4WMpBNs0/maxresdefault.jpg',\n    channelId: 'UCAmrDiab',\n    category: 'Music'\n  }\n];\n\n// Get random videos from the database\nexport const getRandomVideos = (count = 6, includeArabic = true) => {\n  const allVideos = includeArabic \n    ? [...realYouTubeVideos, ...arabicYouTubeVideos]\n    : realYouTubeVideos;\n  \n  const shuffled = [...allVideos].sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, count);\n};\n\n// Get videos by category\nexport const getVideosByCategory = (category, count = 6) => {\n  const filtered = realYouTubeVideos.filter(video => \n    video.category.toLowerCase() === category.toLowerCase()\n  );\n  return filtered.slice(0, count);\n};\n\n// Search videos by title or channel\nexport const searchVideos = (query, count = 10) => {\n  const allVideos = [...realYouTubeVideos, ...arabicYouTubeVideos];\n  const filtered = allVideos.filter(video =>\n    video.title.toLowerCase().includes(query.toLowerCase()) ||\n    video.channel.toLowerCase().includes(query.toLowerCase()) ||\n    video.description.toLowerCase().includes(query.toLowerCase())\n  );\n  return filtered.slice(0, count);\n};\n", "// YouTube Data API service\n// This service handles all interactions with YouTube's real API\nimport { realYouTubeVideos, arabicYouTubeVideos, getRandomVideos, searchVideos } from '../data/realYouTubeVideos.js';\n\nclass YouTubeAPIService {\n  constructor() {\n    // For production, you would get this from environment variables\n    this.apiKey = process.env.REACT_APP_YOUTUBE_API_KEY;\n    this.baseURL = 'https://www.googleapis.com/youtube/v3';\n\n    // Use real video database for guaranteed results\n    this.useRealDatabase = true;\n    this.realVideos = realYouTubeVideos;\n    this.arabicVideos = arabicYouTubeVideos;\n  }\n\n  // Search for videos using real database (guaranteed to work)\n  async searchVideos(query, maxResults = 25, pageToken = '') {\n    try {\n      // Always use real database for guaranteed results\n      return await this.searchWithAlternativeMethod(query, maxResults, pageToken);\n\n    } catch (error) {\n      console.error('YouTube search error:', error);\n      // Fallback to mock data if needed\n      return this.getMockSearchResults(query);\n    }\n  }\n\n  // Get video details by ID using real database\n  async getVideoDetails(videoId) {\n    try {\n      // Always use real database for guaranteed results\n      return await this.getVideoDetailsAlternative(videoId);\n\n    } catch (error) {\n      console.error('YouTube video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Alternative search method using real video database\n  async searchWithAlternativeMethod(query, maxResults, pageToken) {\n    try {\n      const allVideos = [...this.realVideos, ...this.arabicVideos];\n\n      // Search in our real database\n      const searchResults = allVideos.filter(video =>\n        video.title.toLowerCase().includes(query.toLowerCase()) ||\n        video.channel.toLowerCase().includes(query.toLowerCase()) ||\n        video.description.toLowerCase().includes(query.toLowerCase())\n      );\n\n      // If no matches found, return random videos\n      const finalResults = searchResults.length > 0\n        ? searchResults.slice(0, maxResults)\n        : allVideos.slice(0, maxResults);\n\n      return {\n        videos: finalResults,\n        nextPageToken: pageToken ? null : 'next_page_token',\n        totalResults: finalResults.length\n      };\n    } catch (error) {\n      console.error('Alternative search error:', error);\n      return this.getMockSearchResults(query);\n    }\n  }\n\n\n\n  // Alternative method for getting video details from real database\n  async getVideoDetailsAlternative(videoId) {\n    try {\n      // Search in our real database first\n      const allVideos = [...this.realVideos, ...this.arabicVideos];\n      const foundVideo = allVideos.find(video => video.id === videoId);\n\n      if (foundVideo) {\n        return foundVideo;\n      }\n\n      // If not found, return mock data\n      return this.getMockVideoDetails(videoId);\n    } catch (error) {\n      console.error('Alternative video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Transform YouTube API search results to our format\n  transformSearchResults(data) {\n    if (!data.items) {\n      return { videos: [], nextPageToken: null, totalResults: 0 };\n    }\n\n    const videos = data.items.map(item => ({\n      id: item.id.videoId || item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails?.duration),\n      views: this.formatViews(item.statistics?.viewCount),\n      channelId: item.snippet.channelId\n    }));\n\n    return {\n      videos,\n      nextPageToken: data.nextPageToken || null,\n      totalResults: data.pageInfo?.totalResults || 0\n    };\n  }\n\n  // Transform video details to our format\n  transformVideoDetails(item) {\n    return {\n      id: item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.maxres?.url || item.snippet.thumbnails.high?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails.duration),\n      views: this.formatViews(item.statistics.viewCount),\n      likes: this.formatViews(item.statistics.likeCount),\n      channelId: item.snippet.channelId,\n      tags: item.snippet.tags || []\n    };\n  }\n\n  // Parse ISO 8601 duration to seconds\n  parseDuration(duration) {\n    if (!duration) return 0;\n    \n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/);\n    if (!match) return 0;\n    \n    const hours = parseInt(match[1]) || 0;\n    const minutes = parseInt(match[2]) || 0;\n    const seconds = parseInt(match[3]) || 0;\n    \n    return hours * 3600 + minutes * 60 + seconds;\n  }\n\n  // Format view count\n  formatViews(viewCount) {\n    if (!viewCount) return '0';\n    \n    const count = parseInt(viewCount);\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  }\n\n  // Format duration from seconds to MM:SS or HH:MM:SS\n  formatDuration(seconds) {\n    if (!seconds) return '0:00';\n\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n\n\n  // Mock search results (fallback)\n  getMockSearchResults(query) {\n    const mockVideos = [\n      {\n        id: 'dQw4w9WgXcQ',\n        title: `${query} - نتيجة البحث الأولى`,\n        channel: 'قناة تجريبية',\n        description: 'وصف مفصل للفيديو الأول في نتائج البحث...',\n        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',\n        duration: '3:32',\n        views: '1.2M',\n        publishedAt: '2023-01-15T10:00:00Z',\n        channelId: 'UC123456789'\n      },\n      {\n        id: 'kJQP7kiw5Fk',\n        title: `شرح ${query} بالتفصيل`,\n        channel: 'قناة التعليم',\n        description: 'شرح شامل ومفصل حول الموضوع المطلوب...',\n        thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg',\n        duration: '4:42',\n        views: '850K',\n        publishedAt: '2023-02-20T15:30:00Z',\n        channelId: 'UC987654321'\n      },\n      {\n        id: 'fJ9rUzIMcZQ',\n        title: `أفضل ${query} لهذا العام`,\n        channel: 'قناة المراجعات',\n        description: 'مراجعة شاملة لأفضل الخيارات المتاحة...',\n        thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',\n        duration: '5:55',\n        views: '2.1M',\n        publishedAt: '2023-03-10T12:15:00Z',\n        channelId: 'UC456789123'\n      }\n    ];\n\n    return {\n      videos: mockVideos,\n      nextPageToken: null,\n      totalResults: mockVideos.length\n    };\n  }\n\n  // Mock video details (fallback)\n  getMockVideoDetails(videoId) {\n    return {\n      id: videoId,\n      title: 'فيديو يوتيوب تجريبي',\n      channel: 'قناة تجريبية',\n      description: 'وصف تجريبي للفيديو...',\n      thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n      duration: '3:45',\n      views: '1.5M',\n      likes: '25K',\n      publishedAt: '2023-01-01T00:00:00Z',\n      channelId: 'UC123456789',\n      tags: ['تجريبي', 'فيديو', 'يوتيوب']\n    };\n  }\n\n  // Get trending videos using real database\n  async getTrendingVideos(regionCode = 'SA', maxResults = 25) {\n    try {\n      // Always use real database for guaranteed results\n      return await this.getTrendingAlternative(maxResults);\n\n    } catch (error) {\n      console.error('YouTube trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n\n  // Get trending videos using real database\n  async getTrendingAlternative(maxResults = 6) {\n    try {\n      // Get random videos from our real database\n      const allVideos = [...this.realVideos, ...this.arabicVideos];\n      const shuffled = [...allVideos].sort(() => 0.5 - Math.random());\n      const selectedVideos = shuffled.slice(0, maxResults);\n\n      return {\n        videos: selectedVideos,\n        nextPageToken: null,\n        totalResults: selectedVideos.length\n      };\n    } catch (error) {\n      console.error('Alternative trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n\n\n}\n\n// Create and export a singleton instance\nconst youtubeAPI = new YouTubeAPIService();\nexport default youtubeAPI;\n", "import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport useRouter from '../../services/router';\nimport youtubeAPI from '../../services/youtubeAPI';\nimport './Home.css';\n\nconst Home = () => {\n  const [featuredVideos, setFeaturedVideos] = useState([]);\n  const [isLoadingVideos, setIsLoadingVideos] = useState(true);\n\n  const { stats, currentVideo } = useAppStore();\n  const { navigate } = useRouter();\n\n  // Load trending videos on component mount\n  useEffect(() => {\n    const loadTrendingVideos = async () => {\n      try {\n        setIsLoadingVideos(true);\n        const result = await youtubeAPI.getTrendingVideos('SA', 6);\n        setFeaturedVideos(result.videos);\n      } catch (error) {\n        console.error('Failed to load trending videos:', error);\n        // Fallback to mock data\n        setFeaturedVideos([\n          {\n            id: 'dQw4w9WgXcQ',\n            title: 'مرحباً بك في مشغل يوتيوب المتقدم',\n            channel: 'YouTube Player Pro',\n            duration: '3:32',\n            views: '1.2M',\n            thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg'\n          },\n          {\n            id: 'kJQP7kiw5Fk',\n            title: 'كيفية استخدام مانع الإعلانات',\n            channel: 'دليل المستخدم',\n            duration: '4:42',\n            views: '850K',\n            thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg'\n          },\n          {\n            id: 'fJ9rUzIMcZQ',\n            title: 'الميزات الجديدة في الإصدار 2.0',\n            channel: 'أخبار التطبيق',\n            duration: '5:55',\n            views: '2.1M',\n            thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg'\n          }\n        ]);\n      } finally {\n        setIsLoadingVideos(false);\n      }\n    };\n\n    loadTrendingVideos();\n  }, []);\n\n  const handleVideoClick = (videoId) => {\n    navigate('player', { videoId });\n  };\n\n  const handleSearchClick = () => {\n    navigate('search');\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 }\n  };\n\n  return (\n    <motion.div \n      className=\"home-page\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Hero Section */}\n      <motion.section className=\"hero-section\" variants={itemVariants}>\n        <div className=\"hero-content\">\n          <motion.h1 \n            className=\"hero-title\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.2 }}\n          >\n            مرحباً بك في مشغل يوتيوب المتقدم\n          </motion.h1>\n          <motion.p \n            className=\"hero-description\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4 }}\n          >\n            استمتع بمشاهدة فيديوهات يوتيوب بدون إعلانات مع واجهة عربية متقدمة وميزات احترافية\n          </motion.p>\n          <motion.div \n            className=\"hero-actions\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6 }}\n          >\n            <motion.button\n              className=\"hero-button primary\"\n              onClick={handleSearchClick}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\n              </svg>\n              ابدأ البحث\n            </motion.button>\n            <motion.button\n              className=\"hero-button secondary\"\n              onClick={() => navigate('about')}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              تعرف على المزيد\n            </motion.button>\n          </motion.div>\n        </div>\n        <motion.div \n          className=\"hero-image\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ delay: 0.8 }}\n        >\n          <div className=\"hero-video-preview\">\n            <div className=\"video-preview-screen\">\n              <svg width=\"80\" height=\"80\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M8 5v14l11-7z\"/>\n              </svg>\n            </div>\n            <div className=\"video-preview-controls\">\n              <div className=\"control-button\"></div>\n              <div className=\"control-button\"></div>\n              <div className=\"control-button\"></div>\n            </div>\n          </div>\n        </motion.div>\n      </motion.section>\n\n      {/* Stats Section */}\n      <motion.section className=\"stats-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">إحصائياتك</h2>\n        <div className=\"stats-grid\">\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon videos\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M8 5v14l11-7z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{stats.videosWatched}</span>\n              <span className=\"stat-label\">فيديو مشاهد</span>\n            </div>\n          </motion.div>\n\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon ads\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{stats.adsBlocked}</span>\n              <span className=\"stat-label\">إعلان محجوب</span>\n            </div>\n          </motion.div>\n\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon time\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z\"/>\n                <path d=\"M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{Math.floor(stats.timeSpent / 60)}</span>\n              <span className=\"stat-label\">دقيقة مشاهدة</span>\n            </div>\n          </motion.div>\n        </div>\n      </motion.section>\n\n      {/* Featured Videos */}\n      <motion.section className=\"featured-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">فيديوهات مميزة</h2>\n        {isLoadingVideos ? (\n          <div className=\"videos-loading\">\n            <div className=\"loading-spinner\">\n              <div className=\"spinner\"></div>\n            </div>\n            <p>جاري تحميل الفيديوهات المميزة...</p>\n          </div>\n        ) : (\n          <div className=\"videos-grid\">\n            {featuredVideos.map((video, index) => (\n            <motion.div\n              key={video.id}\n              className=\"video-card\"\n              variants={itemVariants}\n              whileHover={{ scale: 1.02, y: -4 }}\n              onClick={() => handleVideoClick(video.id)}\n            >\n              <div className=\"video-thumbnail\">\n                <img src={video.thumbnail} alt={video.title} loading=\"lazy\" />\n                <div className=\"video-duration\">{video.duration}</div>\n                <div className=\"video-overlay\">\n                  <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                    <path d=\"M8 5v14l11-7z\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"video-info\">\n                <h3 className=\"video-title\">{video.title}</h3>\n                <p className=\"video-channel\">{video.channel}</p>\n                <p className=\"video-views\">{video.views} مشاهدة</p>\n              </div>\n            </motion.div>\n          ))}\n          </div>\n        )}\n      </motion.section>\n\n      {/* Features Section */}\n      <motion.section className=\"features-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">المميزات الرئيسية</h2>\n        <div className=\"features-grid\">\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">مانع إعلانات قوي</h3>\n            <p className=\"feature-description\">\n              يحجب جميع أنواع الإعلانات لتجربة مشاهدة سلسة ومريحة\n            </p>\n          </motion.div>\n\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">واجهة عربية متقدمة</h3>\n            <p className=\"feature-description\">\n              تصميم عربي كامل مع دعم RTL وثيم داكن مريح للعينين\n            </p>\n          </motion.div>\n\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">أداء محسن</h3>\n            <p className=\"feature-description\">\n              تشغيل سريع وسلس مع استهلاك أقل للموارد والذاكرة\n            </p>\n          </motion.div>\n        </div>\n      </motion.section>\n    </motion.div>\n  );\n};\n\nexport default Home;\n"], "names": ["realYouTubeVideos", "id", "title", "channel", "description", "duration", "views", "publishedAt", "thumbnail", "channelId", "category", "arabicYouTubeVideos", "constructor", "this", "<PERSON><PERSON><PERSON><PERSON>", "process", "REACT_APP_YOUTUBE_API_KEY", "baseURL", "useRealDatabase", "realVideos", "arabicVideos", "searchVideos", "query", "maxResults", "arguments", "length", "undefined", "pageToken", "searchWithAlternativeMethod", "error", "console", "getMockSearchResults", "getVideoDetails", "videoId", "getVideoDetailsAlternative", "getMockVideoDetails", "allVideos", "searchResults", "filter", "video", "toLowerCase", "includes", "finalResults", "slice", "videos", "nextPageToken", "totalResults", "foundVideo", "find", "transformSearchResults", "data", "_data$pageInfo", "items", "map", "item", "_item$snippet$thumbna", "_item$snippet$thumbna2", "_item$contentDetails", "_item$statistics", "snippet", "channelTitle", "thumbnails", "medium", "url", "default", "parseDuration", "contentDetails", "formatViews", "statistics", "viewCount", "pageInfo", "transformVideoDetails", "_item$snippet$thumbna3", "_item$snippet$thumbna4", "maxres", "high", "likes", "likeCount", "tags", "match", "parseInt", "count", "toFixed", "toString", "formatDuration", "seconds", "hours", "Math", "floor", "minutes", "secs", "padStart", "mockVideos", "getTrendingVideos", "getTrendingAlternative", "<PERSON><PERSON><PERSON><PERSON>", "sort", "random", "Home", "featured<PERSON><PERSON><PERSON>", "setFeaturedVideos", "useState", "isLoadingVideos", "setIsLoadingVideos", "stats", "currentVideo", "useAppStore", "navigate", "useRouter", "useEffect", "async", "result", "youtubeAPI", "loadTrendingVideos", "itemVariants", "hidden", "opacity", "y", "visible", "_jsxs", "motion", "div", "className", "variants", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "initial", "animate", "children", "section", "_jsx", "h1", "delay", "p", "button", "onClick", "handleSearchClick", "whileHover", "scale", "whileTap", "width", "height", "viewBox", "fill", "d", "videosWatched", "adsBlocked", "timeSpent", "index", "handleVideoClick", "src", "alt", "loading"], "sourceRoot": ""}