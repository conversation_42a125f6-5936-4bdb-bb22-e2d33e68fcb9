import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import useAppStore from '../../stores/appStore';
import useRouter from '../../services/router';
import LoadingSpinner from '../common/LoadingSpinner';
import youtubeAPI from '../../services/youtubeAPI';
import './Search.css';

const Search = ({ routeParams }) => {
  const [searchResults, setSearchResults] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const { 
    search: { query, history },
    setSearchQuery,
    addToSearchHistory,
    setSearchResults: setStoreSearchResults
  } = useAppStore();
  
  const { navigate } = useRouter();
  
  const initialQuery = routeParams?.query || query || '';

  useEffect(() => {
    if (initialQuery) {
      setSearchQuery(initialQuery);
      performSearch(initialQuery);
    }
  }, [initialQuery]);

  const performSearch = async (searchQuery) => {
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      // Use real YouTube API
      const results = await youtubeAPI.searchVideos(searchQuery, 20);
      setSearchResults(results.videos);
      setStoreSearchResults(results.videos);
      addToSearchHistory(searchQuery);
    } catch (err) {
      setError('فشل في البحث. يرجى المحاولة مرة أخرى.');
      console.error('Search error:', err);
    } finally {
      setIsLoading(false);
    }
  };



  const handleVideoClick = (videoId) => {
    navigate('player', { videoId });
  };

  const handleSearchSubmit = (newQuery) => {
    if (newQuery.trim()) {
      setSearchQuery(newQuery);
      performSearch(newQuery);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'أمس';
    if (diffDays < 7) return `منذ ${diffDays} أيام`;
    if (diffDays < 30) return `منذ ${Math.floor(diffDays / 7)} أسابيع`;
    if (diffDays < 365) return `منذ ${Math.floor(diffDays / 30)} أشهر`;
    return `منذ ${Math.floor(diffDays / 365)} سنوات`;
  };

  return (
    <div className="search-page">
      {/* Search Header */}
      <div className="search-header">
        <h1>نتائج البحث</h1>
        {query && (
          <p className="search-query">
            البحث عن: <strong>"{query}"</strong>
          </p>
        )}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="search-loading">
          <LoadingSpinner text="جاري البحث..." />
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="search-error">
          <div className="error-icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>
          <h3>خطأ في البحث</h3>
          <p>{error}</p>
          <button 
            className="retry-button"
            onClick={() => performSearch(query)}
          >
            إعادة المحاولة
          </button>
        </div>
      )}

      {/* Search Results */}
      {!isLoading && !error && searchResults.length > 0 && (
        <motion.div 
          className="search-results"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="results-info">
            <span>تم العثور على {searchResults.length} نتيجة</span>
          </div>
          
          <div className="results-list">
            {searchResults.map((video, index) => (
              <motion.div
                key={video.id}
                className="result-item"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                onClick={() => handleVideoClick(video.id)}
              >
                <div className="result-thumbnail">
                  <img src={video.thumbnail} alt={video.title} loading="lazy" />
                  <div className="video-duration">{video.duration}</div>
                  <div className="play-overlay">
                    <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  </div>
                </div>
                
                <div className="result-content">
                  <h3 className="result-title">{video.title}</h3>
                  <div className="result-meta">
                    <span className="result-channel">{video.channel}</span>
                    <span className="result-views">{video.views} مشاهدة</span>
                    <span className="result-date">{formatDate(video.publishedAt)}</span>
                  </div>
                  <p className="result-description">{video.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Empty State */}
      {!isLoading && !error && searchResults.length === 0 && query && (
        <div className="search-empty">
          <div className="empty-icon">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </svg>
          </div>
          <h2>لا توجد نتائج</h2>
          <p>لم نتمكن من العثور على أي فيديوهات تطابق بحثك</p>
          <div className="search-suggestions">
            <h4>جرب:</h4>
            <ul>
              <li>التأكد من صحة الكتابة</li>
              <li>استخدام كلمات مختلفة</li>
              <li>استخدام كلمات أكثر عمومية</li>
            </ul>
          </div>
        </div>
      )}

      {/* Search History */}
      {!query && history.length > 0 && (
        <div className="search-history">
          <h2>عمليات البحث السابقة</h2>
          <div className="history-list">
            {history.slice(0, 10).map((item, index) => (
              <motion.button
                key={`history-${item}-${index}`}
                className="history-item"
                onClick={() => handleSearchSubmit(item)}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
                </svg>
                <span>{item}</span>
              </motion.button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Search;
