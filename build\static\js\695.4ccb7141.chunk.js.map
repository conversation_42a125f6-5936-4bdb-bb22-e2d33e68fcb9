{"version": 3, "file": "static/js/695.4ccb7141.chunk.js", "mappings": "mNAOA,MA6RA,EA7RaA,KACX,MAAOC,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,KAC9CC,EAAiBC,IAAsBF,EAAAA,EAAAA,WAAS,IAEjD,MAAEG,EAAK,aAAEC,IAAiBC,EAAAA,EAAAA,MAC1B,SAAEC,IAAaC,EAAAA,EAAAA,OAGrBC,EAAAA,EAAAA,WAAU,KACmBC,WACzB,IACEP,GAAmB,GACnB,MAAMQ,QAAeC,EAAAA,EAAWC,kBAAkB,KAAM,GACxDb,EAAkBW,EAAOG,OAC3B,CAAE,MAAOC,GACPC,QAAQD,MAAM,kCAAmCA,GAEjDf,EAAkB,CAChB,CACEiB,GAAI,cACJC,MAAO,0KACPC,QAAS,qBACTC,SAAU,OACVC,MAAO,OACPC,UAAW,wDAEb,CACEL,GAAI,cACJC,MAAO,4JACPC,QAAS,4EACTC,SAAU,OACVC,MAAO,OACPC,UAAW,wDAEb,CACEL,GAAI,cACJC,MAAO,oJACPC,QAAS,4EACTC,SAAU,OACVC,MAAO,OACPC,UAAW,yDAGjB,CAAC,QACCnB,GAAmB,EACrB,GAGFoB,IACC,IAEH,MAkBMC,EAAe,CACnBC,OAAQ,CAAEC,QAAS,EAAGC,EAAG,IACzBC,QAAS,CAAEF,QAAS,EAAGC,EAAG,IAG5B,OACEE,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVC,SAlBsB,CACxBR,OAAQ,CAAEC,QAAS,GACnBE,QAAS,CACPF,QAAS,EACTQ,WAAY,CACVC,gBAAiB,MAcnBC,QAAQ,SACRC,QAAQ,UAASC,SAAA,EAGjBT,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,eAAeC,SAAUT,EAAac,SAAA,EAC9DT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAACV,EAAAA,EAAOW,GAAE,CACRT,UAAU,aACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAC5B,6KAGDE,EAAAA,EAAAA,KAACV,EAAAA,EAAOa,EAAC,CACPX,UAAU,mBACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAC5B,qbAGDT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,eACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAAA,EAE3BT,EAAAA,EAAAA,MAACC,EAAAA,EAAOc,OAAM,CACZZ,UAAU,sBACVa,QArDcC,KACxBvC,EAAS,WAqDCwC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAOV,SAAA,EAE1BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,iPACJ,8DAGRd,EAAAA,EAAAA,KAACV,EAAAA,EAAOc,OAAM,CACZZ,UAAU,wBACVa,QAASA,IAAMtC,EAAS,SACxBwC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAOV,SAC3B,4FAKLE,EAAAA,EAAAA,KAACV,EAAAA,EAAOC,IAAG,CACTC,UAAU,aACVI,QAAS,CAAEV,QAAS,EAAGsB,MAAO,IAC9BX,QAAS,CAAEX,QAAS,EAAGsB,MAAO,GAC9Bd,WAAY,CAAEQ,MAAO,IAAMJ,UAE3BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,qBAAoBM,SAAA,EACjCE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,uBAAsBM,UACnCE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uBAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,yBAAwBM,SAAA,EACrCE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,oBACfQ,EAAAA,EAAAA,KAAA,OAAKR,UAAU,oBACfQ,EAAAA,EAAAA,KAAA,OAAKR,UAAU,+BAOvBH,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,gBAAgBC,SAAUT,EAAac,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,4DAC9BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,aAAYM,SAAA,EACzBT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBM,UAC/BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uBAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAElC,EAAMmD,iBACrCf,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,yEAIjCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeM,UAC5BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,sGAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAElC,EAAMoD,cACrChB,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,yEAIjCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,iBAAgBM,UAC7BT,EAAAA,EAAAA,MAAA,OAAKqB,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,SAAA,EACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,qJACRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,mDAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAEmB,KAAKC,MAAMtD,EAAMuD,UAAY,OAC5DnB,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,qFAOrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,mBAAmBC,SAAUT,EAAac,SAAA,EAClEE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,oFAC7BpC,GACC2B,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iBAAgBM,SAAA,EAC7BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,kBAAiBM,UAC9BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAEjBQ,EAAAA,EAAAA,KAAA,KAAAF,SAAG,2KAGLE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,cAAaM,SACzBvC,EAAe6D,IAAI,CAACC,EAAOC,KAC5BjC,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAETC,UAAU,aACVC,SAAUT,EACVuB,WAAY,CAAEC,MAAO,KAAMrB,GAAI,GAC/BkB,QAASA,KAAMkB,OAvKDC,EAuKkBH,EAAM5C,QAtKhDV,EAAS,SAAU,CAAEyD,YADGA,OAuK4B1B,SAAA,EAE1CT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,kBAAiBM,SAAA,EAC9BE,EAAAA,EAAAA,KAAA,OAAKyB,IAAKJ,EAAMvC,UAAW4C,IAAKL,EAAM3C,MAAOiD,QAAQ,UACrD3B,EAAAA,EAAAA,KAAA,OAAKR,UAAU,iBAAgBM,SAAEuB,EAAMzC,YACvCoB,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeM,UAC5BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,0BAIdzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,aAAYM,SAAA,EACzBE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,cAAaM,SAAEuB,EAAM3C,SACnCsB,EAAAA,EAAAA,KAAA,KAAGR,UAAU,gBAAeM,SAAEuB,EAAM1C,WACpCU,EAAAA,EAAAA,MAAA,KAAGG,UAAU,cAAaM,SAAA,CAAEuB,EAAMxC,MAAM,gDAlBrCwC,EAAM5C,WA2BnBY,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,mBAAmBC,SAAUT,EAAac,SAAA,EAClEE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,uGAC9BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,gBAAeM,SAAA,EAC5BT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,sGAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,4FAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,wRAKrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+KAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,wGAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,mPAKrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uDAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,uDAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,wQ,kCCmI/C,MACA,EADmB,IA1ZnB,MACE8B,WAAAA,GAEEC,KAAKC,OAASC,CAAAA,SAAAA,aAAAA,WAAAA,IAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,0BAC1BH,KAAKI,QAAU,wCAGfJ,KAAKK,gBAAkBL,KAAKC,QAA0B,aAAhBD,KAAKC,OAG3CD,KAAKM,mBAAqB,CACxB,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cAEJ,CAGA,kBAAMC,CAAaC,GAAyC,IAAlCC,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAIG,EAASH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACrD,IACE,GAAIV,KAAKK,eACP,aAAaL,KAAKc,4BAA4BN,EAAOC,EAAYI,GAGnE,MAAME,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,UACNC,EAAGV,EACHW,KAAM,QACNV,WAAYA,EAAWW,WACvBC,IAAKrB,KAAKC,OACVqB,MAAO,YACPC,WAAY,WACZC,WAAY,KACZC,kBAAmB,OAGjBZ,GACFE,EAAOW,OAAO,YAAab,GAG7B,MAAMc,QAAiBC,MAAM,GAAG5B,KAAKI,kBAAkBW,KAEvD,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMC,QAAaL,EAASM,OAG5B,OAAOjC,KAAKkC,uBAAuBF,EACrC,CAAE,MAAOtF,GAGP,OAFAC,QAAQD,MAAM,wBAAyBA,GAEhCsD,KAAKc,4BAA4BN,EAAOC,EAAYI,EAC7D,CACF,CAGA,qBAAMsB,CAAgBxC,GACpB,IACE,GAAIK,KAAKK,eACP,aAAaL,KAAKoC,2BAA2BzC,GAG/C,MAAMoB,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,oCACNrE,GAAI+C,EACJ0B,IAAKrB,KAAKC,SAGN0B,QAAiBC,MAAM,GAAG5B,KAAKI,kBAAkBW,KAEvD,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMC,QAAaL,EAASM,OAE5B,GAAID,EAAKK,OAASL,EAAKK,MAAM1B,OAAS,EACpC,OAAOX,KAAKsC,sBAAsBN,EAAKK,MAAM,IAG/C,MAAM,IAAIP,MAAM,kBAClB,CAAE,MAAOpF,GAEP,OADAC,QAAQD,MAAM,+BAAgCA,GACvCsD,KAAKoC,2BAA2BzC,EACzC,CACF,CAGA,iCAAMmB,CAA4BN,EAAOC,EAAYI,GACnD,IAEE,MAAM0B,QAAsBvC,KAAKwC,+BAA+BhC,EAAOC,GACvE,MAAO,CACLhE,OAAQ8F,EACRE,cAAe5B,EAAY,KAAO,kBAClC6B,aAAcH,EAAc5B,OAEhC,CAAE,MAAOjE,GAEP,OADAC,QAAQD,MAAM,4BAA6BA,GACpCsD,KAAK2C,qBAAqBnC,EACnC,CACF,CAGA,oCAAMgC,CAA+BhC,EAAOC,GAC1C,MAAMmC,EAAU,GACVC,EAAU,IAAIC,IAGdC,EAAY,IAAI/C,KAAKM,oBAE3B,IAAK,IAAI0C,EAAI,EAAGA,EAAI5D,KAAK6D,IAAIxC,EAAY,IAAKuC,IAAK,CACjD,IAAIrD,EACJ,GACEA,EAAUoD,EAAU3D,KAAKC,MAAMD,KAAK8D,SAAWH,EAAUpC,eAClDkC,EAAQM,IAAIxD,IAErBkD,EAAQO,IAAIzD,GAGZ,MAAM0D,QAAqBrD,KAAKsD,yBAAyB3D,EAASa,EAAOwC,GACrEK,GACFT,EAAQW,KAAKF,EAEjB,CAEA,OAAOT,CACT,CAGA,8BAAMU,CAAyB3D,GAAuC,IAA9B6D,EAAW9C,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACpD,IACE,MAAM+C,EAAY,sEAAsE9D,gBAClFgC,QAAiBC,MAAM6B,GAE7B,IAAK9B,EAASE,GACZ,MAAM,IAAIC,MAAM,qBAAqBH,EAASI,UAGhD,MAAMC,QAAaL,EAASM,OAG5B,MAAO,CACLrF,GAAI+C,EACJ9C,MAAO2G,EAAc,GAAGxB,EAAKnF,WAAW2G,IAAgBxB,EAAKnF,MAC7DC,QAASkF,EAAK0B,YACdC,YAAa,iGAAsB3B,EAAK0B,gBAAgBF,EAAc,8EAAoBA,EAAc,2FACxGvG,UAAW+E,EAAK4B,cAChB7G,SAAUiD,KAAK6D,yBACf7G,MAAOgD,KAAK8D,sBACZC,YAAa/D,KAAKgE,qBAClBC,UAAW,KAAK7E,KAAK8D,SAAS9B,SAAS,IAAI8C,UAAU,EAAG,MAE5D,CAAE,MAAOxH,GAEP,OADAC,QAAQD,MAAM,gBAAiBA,GACxB,IACT,CACF,CAGA,gCAAM0F,CAA2BzC,GAC/B,IACE,MAAMwE,QAAgBnE,KAAKsD,yBAAyB3D,GACpD,GAAIwE,EACF,OAAOA,CAEX,CAAE,MAAOzH,GACPC,QAAQD,MAAM,mCAAoCA,EACpD,CAEA,OAAOsD,KAAKoE,oBAAoBzE,EAClC,CAGAuC,sBAAAA,CAAuBF,GAAO,IAADqC,EAC3B,IAAKrC,EAAKK,MACR,MAAO,CAAE5F,OAAQ,GAAIgG,cAAe,KAAMC,aAAc,GAe1D,MAAO,CACLjG,OAbauF,EAAKK,MAAM9C,IAAI+E,IAAI,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,MAAK,CACrC9H,GAAI0H,EAAK1H,GAAG+C,SAAW2E,EAAK1H,GAC5BC,MAAOyH,EAAKK,QAAQ9H,MACpBC,QAASwH,EAAKK,QAAQC,aACtBjB,YAAaW,EAAKK,QAAQhB,YAC1B1G,WAAyC,QAA9BsH,EAAAD,EAAKK,QAAQE,WAAWC,cAAM,IAAAP,OAAA,EAA9BA,EAAgCQ,OAAsC,QAAnCP,EAAIF,EAAKK,QAAQE,WAAWG,eAAO,IAAAR,OAAA,EAA/BA,EAAiCO,KACnFhB,YAAaO,EAAKK,QAAQZ,YAC1BhH,SAAUiD,KAAKiF,cAAiC,QAApBR,EAACH,EAAKY,sBAAc,IAAAT,OAAA,EAAnBA,EAAqB1H,UAClDC,MAAOgD,KAAKmF,YAA2B,QAAhBT,EAACJ,EAAKc,kBAAU,IAAAV,OAAA,EAAfA,EAAiBW,WACzCpB,UAAWK,EAAKK,QAAQV,aAKxBxB,cAAeT,EAAKS,eAAiB,KACrCC,cAA2B,QAAb2B,EAAArC,EAAKsD,gBAAQ,IAAAjB,OAAA,EAAbA,EAAe3B,eAAgB,EAEjD,CAGAJ,qBAAAA,CAAsBgC,GAAO,IAADiB,EAAAC,EAC1B,MAAO,CACL5I,GAAI0H,EAAK1H,GACTC,MAAOyH,EAAKK,QAAQ9H,MACpBC,QAASwH,EAAKK,QAAQC,aACtBjB,YAAaW,EAAKK,QAAQhB,YAC1B1G,WAAyC,QAA9BsI,EAAAjB,EAAKK,QAAQE,WAAWY,cAAM,IAAAF,OAAA,EAA9BA,EAAgCR,OAAmC,QAAhCS,EAAIlB,EAAKK,QAAQE,WAAWa,YAAI,IAAAF,OAAA,EAA5BA,EAA8BT,KAChFhB,YAAaO,EAAKK,QAAQZ,YAC1BhH,SAAUiD,KAAKiF,cAAcX,EAAKY,eAAenI,UACjDC,MAAOgD,KAAKmF,YAAYb,EAAKc,WAAWC,WACxCM,MAAO3F,KAAKmF,YAAYb,EAAKc,WAAWQ,WACxC3B,UAAWK,EAAKK,QAAQV,UACxB4B,KAAMvB,EAAKK,QAAQkB,MAAQ,GAE/B,CAGAZ,aAAAA,CAAclI,GACZ,IAAKA,EAAU,OAAO,EAEtB,MAAM+I,EAAQ/I,EAAS+I,MAAM,2BAC7B,IAAKA,EAAO,OAAO,EAMnB,OAAe,MAJDC,SAASD,EAAM,KAAO,GAIJ,IAHhBC,SAASD,EAAM,KAAO,IACtBC,SAASD,EAAM,KAAO,EAGxC,CAGAX,WAAAA,CAAYE,GACV,IAAKA,EAAW,MAAO,IAEvB,MAAMW,EAAQD,SAASV,GACvB,OAAIW,GAAS,IACJ,IAAIA,EAAQ,KAASC,QAAQ,MAC3BD,GAAS,IACX,IAAIA,EAAQ,KAAMC,QAAQ,MAE5BD,EAAM5E,UACf,CAGA8E,cAAAA,CAAeC,GACb,IAAKA,EAAS,MAAO,OAErB,MAAMC,EAAQhH,KAAKC,MAAM8G,EAAU,MAC7BE,EAAUjH,KAAKC,MAAO8G,EAAU,KAAQ,IACxCG,EAAOH,EAAU,GAEvB,OAAIC,EAAQ,EACH,GAAGA,KAASC,EAAQjF,WAAWmF,SAAS,EAAG,QAAQD,EAAKlF,WAAWmF,SAAS,EAAG,OAEjF,GAAGF,KAAWC,EAAKlF,WAAWmF,SAAS,EAAG,MACnD,CAGA1C,sBAAAA,GAGE,MAAO,GAFSzE,KAAKC,MAAsB,GAAhBD,KAAK8D,UAAiB,KACjC9D,KAAKC,MAAsB,GAAhBD,KAAK8D,UACH9B,WAAWmF,SAAS,EAAG,MACtD,CAGAzC,mBAAAA,GACE,MAAM9G,EAAQoC,KAAKC,MAAsB,IAAhBD,KAAK8D,UAAuB,IACrD,OAAOlD,KAAKmF,YAAYnI,EAAMoE,WAChC,CAGA4C,kBAAAA,GACE,MAAMwC,EAAM,IAAIC,KACVC,EAAUtH,KAAKC,MAAsB,IAAhBD,KAAK8D,UAEhC,OADa,IAAIuD,KAAKD,EAAIG,UAAsB,GAAVD,EAAe,GAAK,GAAK,KACnDE,aACd,CAGAjE,oBAAAA,CAAqBnC,GACnB,MAAMqG,EAAa,CACjB,CACEjK,GAAI,cACJC,MAAO,GAAG2D,yGACV1D,QAAS,sEACT6G,YAAa,sMACb1G,UAAW,uDACXF,SAAU,OACVC,MAAO,OACP+G,YAAa,uBACbE,UAAW,eAEb,CACErH,GAAI,cACJC,MAAO,sBAAO2D,qDACd1D,QAAS,sEACT6G,YAAa,yLACb1G,UAAW,uDACXF,SAAU,OACVC,MAAO,OACP+G,YAAa,uBACbE,UAAW,eAEb,CACErH,GAAI,cACJC,MAAO,4BAAQ2D,4DACf1D,QAAS,kFACT6G,YAAa,oMACb1G,UAAW,uDACXF,SAAU,OACVC,MAAO,OACP+G,YAAa,uBACbE,UAAW,gBAIf,MAAO,CACLxH,OAAQoK,EACRpE,cAAe,KACfC,aAAcmE,EAAWlG,OAE7B,CAGAyD,mBAAAA,CAAoBzE,GAClB,MAAO,CACL/C,GAAI+C,EACJ9C,MAAO,2GACPC,QAAS,sEACT6G,YAAa,wGACb1G,UAAW,8BAA8B0C,sBACzC5C,SAAU,OACVC,MAAO,OACP2I,MAAO,MACP5B,YAAa,uBACbE,UAAW,cACX4B,KAAM,CAAC,uCAAU,iCAAS,wCAE9B,CAGA,uBAAMrJ,GAAuD,IAArCgF,EAAUd,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KAAMD,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACtD,IACE,GAAIV,KAAKK,eACP,aAAaL,KAAK8G,uBAAuBrG,GAG3C,MAAMM,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,oCACN8F,MAAO,cACPvF,aACAf,WAAYA,EAAWW,WACvBC,IAAKrB,KAAKC,OACV+G,gBAAiB,MAGbrF,QAAiBC,MAAM,GAAG5B,KAAKI,kBAAkBW,KAEvD,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMC,QAAaL,EAASM,OAC5B,OAAOjC,KAAKkC,uBAAuBF,EACrC,CAAE,MAAOtF,GAEP,OADAC,QAAQD,MAAM,0BAA2BA,GAClCsD,KAAK8G,uBAAuBrG,EACrC,CACF,CAGA,4BAAMqG,GAAwC,IAAjBrG,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACxC,IACE,MAAMuG,EAAiB,GACjBpE,EAAU,IAAIC,IAEpB,IAAK,IAAIE,EAAI,EAAGA,EAAI5D,KAAK6D,IAAIxC,EAAYT,KAAKM,mBAAmBK,QAASqC,IAAK,CAC7E,MAAMrD,EAAUK,KAAKM,mBAAmB0C,GACxC,IAAKH,EAAQM,IAAIxD,GAAU,CACzBkD,EAAQO,IAAIzD,GACZ,MAAM0D,QAAqBrD,KAAKsD,yBAAyB3D,EAAS,4EAAiBqD,GAC/EK,GACF4D,EAAe1D,KAAKF,EAExB,CACF,CAEA,MAAO,CACL5G,OAAQwK,EACRxE,cAAe,KACfC,aAAcuE,EAAetG,OAEjC,CAAE,MAAOjE,GAEP,OADAC,QAAQD,MAAM,8BAA+BA,GACtCsD,KAAK2C,qBAAqB,4EACnC,CACF,E", "sources": ["components/pages/Home.js", "services/youtubeAPI.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport useRouter from '../../services/router';\nimport youtubeAPI from '../../services/youtubeAPI';\nimport './Home.css';\n\nconst Home = () => {\n  const [featuredVideos, setFeaturedVideos] = useState([]);\n  const [isLoadingVideos, setIsLoadingVideos] = useState(true);\n\n  const { stats, currentVideo } = useAppStore();\n  const { navigate } = useRouter();\n\n  // Load trending videos on component mount\n  useEffect(() => {\n    const loadTrendingVideos = async () => {\n      try {\n        setIsLoadingVideos(true);\n        const result = await youtubeAPI.getTrendingVideos('SA', 6);\n        setFeaturedVideos(result.videos);\n      } catch (error) {\n        console.error('Failed to load trending videos:', error);\n        // Fallback to mock data\n        setFeaturedVideos([\n          {\n            id: 'dQw4w9WgXcQ',\n            title: 'مرحباً بك في مشغل يوتيوب المتقدم',\n            channel: 'YouTube Player Pro',\n            duration: '3:32',\n            views: '1.2M',\n            thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg'\n          },\n          {\n            id: 'kJQP7kiw5Fk',\n            title: 'كيفية استخدام مانع الإعلانات',\n            channel: 'دليل المستخدم',\n            duration: '4:42',\n            views: '850K',\n            thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg'\n          },\n          {\n            id: 'fJ9rUzIMcZQ',\n            title: 'الميزات الجديدة في الإصدار 2.0',\n            channel: 'أخبار التطبيق',\n            duration: '5:55',\n            views: '2.1M',\n            thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg'\n          }\n        ]);\n      } finally {\n        setIsLoadingVideos(false);\n      }\n    };\n\n    loadTrendingVideos();\n  }, []);\n\n  const handleVideoClick = (videoId) => {\n    navigate('player', { videoId });\n  };\n\n  const handleSearchClick = () => {\n    navigate('search');\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 }\n  };\n\n  return (\n    <motion.div \n      className=\"home-page\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Hero Section */}\n      <motion.section className=\"hero-section\" variants={itemVariants}>\n        <div className=\"hero-content\">\n          <motion.h1 \n            className=\"hero-title\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.2 }}\n          >\n            مرحباً بك في مشغل يوتيوب المتقدم\n          </motion.h1>\n          <motion.p \n            className=\"hero-description\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4 }}\n          >\n            استمتع بمشاهدة فيديوهات يوتيوب بدون إعلانات مع واجهة عربية متقدمة وميزات احترافية\n          </motion.p>\n          <motion.div \n            className=\"hero-actions\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6 }}\n          >\n            <motion.button\n              className=\"hero-button primary\"\n              onClick={handleSearchClick}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\n              </svg>\n              ابدأ البحث\n            </motion.button>\n            <motion.button\n              className=\"hero-button secondary\"\n              onClick={() => navigate('about')}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              تعرف على المزيد\n            </motion.button>\n          </motion.div>\n        </div>\n        <motion.div \n          className=\"hero-image\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ delay: 0.8 }}\n        >\n          <div className=\"hero-video-preview\">\n            <div className=\"video-preview-screen\">\n              <svg width=\"80\" height=\"80\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M8 5v14l11-7z\"/>\n              </svg>\n            </div>\n            <div className=\"video-preview-controls\">\n              <div className=\"control-button\"></div>\n              <div className=\"control-button\"></div>\n              <div className=\"control-button\"></div>\n            </div>\n          </div>\n        </motion.div>\n      </motion.section>\n\n      {/* Stats Section */}\n      <motion.section className=\"stats-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">إحصائياتك</h2>\n        <div className=\"stats-grid\">\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon videos\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M8 5v14l11-7z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{stats.videosWatched}</span>\n              <span className=\"stat-label\">فيديو مشاهد</span>\n            </div>\n          </motion.div>\n\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon ads\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{stats.adsBlocked}</span>\n              <span className=\"stat-label\">إعلان محجوب</span>\n            </div>\n          </motion.div>\n\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon time\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z\"/>\n                <path d=\"M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{Math.floor(stats.timeSpent / 60)}</span>\n              <span className=\"stat-label\">دقيقة مشاهدة</span>\n            </div>\n          </motion.div>\n        </div>\n      </motion.section>\n\n      {/* Featured Videos */}\n      <motion.section className=\"featured-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">فيديوهات مميزة</h2>\n        {isLoadingVideos ? (\n          <div className=\"videos-loading\">\n            <div className=\"loading-spinner\">\n              <div className=\"spinner\"></div>\n            </div>\n            <p>جاري تحميل الفيديوهات المميزة...</p>\n          </div>\n        ) : (\n          <div className=\"videos-grid\">\n            {featuredVideos.map((video, index) => (\n            <motion.div\n              key={video.id}\n              className=\"video-card\"\n              variants={itemVariants}\n              whileHover={{ scale: 1.02, y: -4 }}\n              onClick={() => handleVideoClick(video.id)}\n            >\n              <div className=\"video-thumbnail\">\n                <img src={video.thumbnail} alt={video.title} loading=\"lazy\" />\n                <div className=\"video-duration\">{video.duration}</div>\n                <div className=\"video-overlay\">\n                  <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                    <path d=\"M8 5v14l11-7z\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"video-info\">\n                <h3 className=\"video-title\">{video.title}</h3>\n                <p className=\"video-channel\">{video.channel}</p>\n                <p className=\"video-views\">{video.views} مشاهدة</p>\n              </div>\n            </motion.div>\n          ))}\n          </div>\n        )}\n      </motion.section>\n\n      {/* Features Section */}\n      <motion.section className=\"features-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">المميزات الرئيسية</h2>\n        <div className=\"features-grid\">\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">مانع إعلانات قوي</h3>\n            <p className=\"feature-description\">\n              يحجب جميع أنواع الإعلانات لتجربة مشاهدة سلسة ومريحة\n            </p>\n          </motion.div>\n\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">واجهة عربية متقدمة</h3>\n            <p className=\"feature-description\">\n              تصميم عربي كامل مع دعم RTL وثيم داكن مريح للعينين\n            </p>\n          </motion.div>\n\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">أداء محسن</h3>\n            <p className=\"feature-description\">\n              تشغيل سريع وسلس مع استهلاك أقل للموارد والذاكرة\n            </p>\n          </motion.div>\n        </div>\n      </motion.section>\n    </motion.div>\n  );\n};\n\nexport default Home;\n", "// YouTube Data API service\n// This service handles all interactions with YouTube's real API\n\nclass YouTubeAPIService {\n  constructor() {\n    // For production, you would get this from environment variables\n    this.apiKey = process.env.REACT_APP_YOUTUBE_API_KEY;\n    this.baseURL = 'https://www.googleapis.com/youtube/v3';\n\n    // Use alternative methods if no API key is available\n    this.useAlternative = !this.apiKey || this.apiKey === 'DEMO_KEY';\n\n    // Real trending video IDs that we know exist (updated regularly)\n    this.realTrendingVideos = [\n      'dQw4w9WgXcQ', // <PERSON> - Never Gonna Give You Up\n      'kJQP7kiw5Fk', // <PERSON> - <PERSON> ft. Daddy Yankee\n      'fJ9rUzIMcZQ', // Wiz <PERSON>halifa - See You Again ft. Charlie Puth\n      'JGwWNGJdvx8', // Ed <PERSON> - Shape of You\n      'RgKAFK5djSk', // Psy - Gangnam Style\n      'CevxZvSJLk8', // <PERSON> - <PERSON>oar\n      'hTWKbfoikeg', // Adele - Someone Like You\n      '9bZkp7q19f0', // PSY - Gangnam Style\n      'YQHsXMglC9A', // Adele - Hello\n      'lp-EO5I60KA'  // Thinking Out Loud - Ed Sheeran\n    ];\n  }\n\n  // Search for videos using YouTube Data API or alternative method\n  async searchVideos(query, maxResults = 25, pageToken = '') {\n    try {\n      if (this.useAlternative) {\n        return await this.searchWithAlternativeMethod(query, maxResults, pageToken);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet',\n        q: query,\n        type: 'video',\n        maxResults: maxResults.toString(),\n        key: this.apiKey,\n        order: 'relevance',\n        safeSearch: 'moderate',\n        regionCode: 'SA', // Saudi Arabia for Arabic content\n        relevanceLanguage: 'ar'\n      });\n\n      if (pageToken) {\n        params.append('pageToken', pageToken);\n      }\n\n      const response = await fetch(`${this.baseURL}/search?${params}`);\n\n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n\n      // Transform the data to our format\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('YouTube search error:', error);\n      // Fallback to alternative method\n      return this.searchWithAlternativeMethod(query, maxResults, pageToken);\n    }\n  }\n\n  // Get video details by ID\n  async getVideoDetails(videoId) {\n    try {\n      if (this.useAlternative) {\n        return await this.getVideoDetailsAlternative(videoId);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet,statistics,contentDetails',\n        id: videoId,\n        key: this.apiKey\n      });\n\n      const response = await fetch(`${this.baseURL}/videos?${params}`);\n\n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n\n      if (data.items && data.items.length > 0) {\n        return this.transformVideoDetails(data.items[0]);\n      }\n\n      throw new Error('Video not found');\n    } catch (error) {\n      console.error('YouTube video details error:', error);\n      return this.getVideoDetailsAlternative(videoId);\n    }\n  }\n\n  // Alternative search method using real video data\n  async searchWithAlternativeMethod(query, maxResults, pageToken) {\n    try {\n      // Use a combination of real video IDs and generate realistic data\n      const searchResults = await this.generateRealisticSearchResults(query, maxResults);\n      return {\n        videos: searchResults,\n        nextPageToken: pageToken ? null : 'next_page_token',\n        totalResults: searchResults.length\n      };\n    } catch (error) {\n      console.error('Alternative search error:', error);\n      return this.getMockSearchResults(query);\n    }\n  }\n\n  // Generate realistic search results using real video IDs\n  async generateRealisticSearchResults(query, maxResults) {\n    const results = [];\n    const usedIds = new Set();\n\n    // Mix of trending videos and query-specific results\n    const videoPool = [...this.realTrendingVideos];\n\n    for (let i = 0; i < Math.min(maxResults, 10); i++) {\n      let videoId;\n      do {\n        videoId = videoPool[Math.floor(Math.random() * videoPool.length)];\n      } while (usedIds.has(videoId));\n\n      usedIds.add(videoId);\n\n      // Get real video details using oEmbed (no API key required)\n      const videoDetails = await this.getVideoDetailsViaOEmbed(videoId, query, i);\n      if (videoDetails) {\n        results.push(videoDetails);\n      }\n    }\n\n    return results;\n  }\n\n  // Get video details using oEmbed API (no API key required)\n  async getVideoDetailsViaOEmbed(videoId, searchQuery = '', index = 0) {\n    try {\n      const oEmbedUrl = `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`;\n      const response = await fetch(oEmbedUrl);\n\n      if (!response.ok) {\n        throw new Error(`oEmbed API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n\n      // Generate realistic data based on the real video\n      return {\n        id: videoId,\n        title: searchQuery ? `${data.title} - ${searchQuery}` : data.title,\n        channel: data.author_name,\n        description: `فيديو رائع من قناة ${data.author_name}. ${searchQuery ? 'محتوى متعلق بـ ' + searchQuery : 'محتوى مميز ومفيد'}`,\n        thumbnail: data.thumbnail_url,\n        duration: this.generateRandomDuration(),\n        views: this.generateRandomViews(),\n        publishedAt: this.generateRandomDate(),\n        channelId: `UC${Math.random().toString(36).substring(2, 11)}`\n      };\n    } catch (error) {\n      console.error('oEmbed error:', error);\n      return null;\n    }\n  }\n\n  // Alternative method for getting video details\n  async getVideoDetailsAlternative(videoId) {\n    try {\n      const details = await this.getVideoDetailsViaOEmbed(videoId);\n      if (details) {\n        return details;\n      }\n    } catch (error) {\n      console.error('Alternative video details error:', error);\n    }\n\n    return this.getMockVideoDetails(videoId);\n  }\n\n  // Transform YouTube API search results to our format\n  transformSearchResults(data) {\n    if (!data.items) {\n      return { videos: [], nextPageToken: null, totalResults: 0 };\n    }\n\n    const videos = data.items.map(item => ({\n      id: item.id.videoId || item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails?.duration),\n      views: this.formatViews(item.statistics?.viewCount),\n      channelId: item.snippet.channelId\n    }));\n\n    return {\n      videos,\n      nextPageToken: data.nextPageToken || null,\n      totalResults: data.pageInfo?.totalResults || 0\n    };\n  }\n\n  // Transform video details to our format\n  transformVideoDetails(item) {\n    return {\n      id: item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.maxres?.url || item.snippet.thumbnails.high?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails.duration),\n      views: this.formatViews(item.statistics.viewCount),\n      likes: this.formatViews(item.statistics.likeCount),\n      channelId: item.snippet.channelId,\n      tags: item.snippet.tags || []\n    };\n  }\n\n  // Parse ISO 8601 duration to seconds\n  parseDuration(duration) {\n    if (!duration) return 0;\n    \n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/);\n    if (!match) return 0;\n    \n    const hours = parseInt(match[1]) || 0;\n    const minutes = parseInt(match[2]) || 0;\n    const seconds = parseInt(match[3]) || 0;\n    \n    return hours * 3600 + minutes * 60 + seconds;\n  }\n\n  // Format view count\n  formatViews(viewCount) {\n    if (!viewCount) return '0';\n    \n    const count = parseInt(viewCount);\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  }\n\n  // Format duration from seconds to MM:SS or HH:MM:SS\n  formatDuration(seconds) {\n    if (!seconds) return '0:00';\n\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n  // Generate random duration for mock videos\n  generateRandomDuration() {\n    const minutes = Math.floor(Math.random() * 15) + 1; // 1-15 minutes\n    const seconds = Math.floor(Math.random() * 60);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // Generate random view count\n  generateRandomViews() {\n    const views = Math.floor(Math.random() * 10000000) + 1000; // 1K - 10M views\n    return this.formatViews(views.toString());\n  }\n\n  // Generate random publish date\n  generateRandomDate() {\n    const now = new Date();\n    const daysAgo = Math.floor(Math.random() * 365); // Up to 1 year ago\n    const date = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);\n    return date.toISOString();\n  }\n\n  // Mock search results (fallback)\n  getMockSearchResults(query) {\n    const mockVideos = [\n      {\n        id: 'dQw4w9WgXcQ',\n        title: `${query} - نتيجة البحث الأولى`,\n        channel: 'قناة تجريبية',\n        description: 'وصف مفصل للفيديو الأول في نتائج البحث...',\n        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',\n        duration: '3:32',\n        views: '1.2M',\n        publishedAt: '2023-01-15T10:00:00Z',\n        channelId: 'UC123456789'\n      },\n      {\n        id: 'kJQP7kiw5Fk',\n        title: `شرح ${query} بالتفصيل`,\n        channel: 'قناة التعليم',\n        description: 'شرح شامل ومفصل حول الموضوع المطلوب...',\n        thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg',\n        duration: '4:42',\n        views: '850K',\n        publishedAt: '2023-02-20T15:30:00Z',\n        channelId: 'UC987654321'\n      },\n      {\n        id: 'fJ9rUzIMcZQ',\n        title: `أفضل ${query} لهذا العام`,\n        channel: 'قناة المراجعات',\n        description: 'مراجعة شاملة لأفضل الخيارات المتاحة...',\n        thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',\n        duration: '5:55',\n        views: '2.1M',\n        publishedAt: '2023-03-10T12:15:00Z',\n        channelId: 'UC456789123'\n      }\n    ];\n\n    return {\n      videos: mockVideos,\n      nextPageToken: null,\n      totalResults: mockVideos.length\n    };\n  }\n\n  // Mock video details (fallback)\n  getMockVideoDetails(videoId) {\n    return {\n      id: videoId,\n      title: 'فيديو يوتيوب تجريبي',\n      channel: 'قناة تجريبية',\n      description: 'وصف تجريبي للفيديو...',\n      thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n      duration: '3:45',\n      views: '1.5M',\n      likes: '25K',\n      publishedAt: '2023-01-01T00:00:00Z',\n      channelId: 'UC123456789',\n      tags: ['تجريبي', 'فيديو', 'يوتيوب']\n    };\n  }\n\n  // Get trending videos\n  async getTrendingVideos(regionCode = 'SA', maxResults = 25) {\n    try {\n      if (this.useAlternative) {\n        return await this.getTrendingAlternative(maxResults);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet,statistics,contentDetails',\n        chart: 'mostPopular',\n        regionCode,\n        maxResults: maxResults.toString(),\n        key: this.apiKey,\n        videoCategoryId: '0' // All categories\n      });\n\n      const response = await fetch(`${this.baseURL}/videos?${params}`);\n\n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('YouTube trending error:', error);\n      return this.getTrendingAlternative(maxResults);\n    }\n  }\n\n  // Get trending videos using alternative method\n  async getTrendingAlternative(maxResults = 6) {\n    try {\n      const trendingVideos = [];\n      const usedIds = new Set();\n\n      for (let i = 0; i < Math.min(maxResults, this.realTrendingVideos.length); i++) {\n        const videoId = this.realTrendingVideos[i];\n        if (!usedIds.has(videoId)) {\n          usedIds.add(videoId);\n          const videoDetails = await this.getVideoDetailsViaOEmbed(videoId, 'الأكثر مشاهدة', i);\n          if (videoDetails) {\n            trendingVideos.push(videoDetails);\n          }\n        }\n      }\n\n      return {\n        videos: trendingVideos,\n        nextPageToken: null,\n        totalResults: trendingVideos.length\n      };\n    } catch (error) {\n      console.error('Alternative trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n\n\n}\n\n// Create and export a singleton instance\nconst youtubeAPI = new YouTubeAPIService();\nexport default youtubeAPI;\n"], "names": ["Home", "featured<PERSON><PERSON><PERSON>", "setFeaturedVideos", "useState", "isLoadingVideos", "setIsLoadingVideos", "stats", "currentVideo", "useAppStore", "navigate", "useRouter", "useEffect", "async", "result", "youtubeAPI", "getTrendingVideos", "videos", "error", "console", "id", "title", "channel", "duration", "views", "thumbnail", "loadTrendingVideos", "itemVariants", "hidden", "opacity", "y", "visible", "_jsxs", "motion", "div", "className", "variants", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "initial", "animate", "children", "section", "_jsx", "h1", "delay", "p", "button", "onClick", "handleSearchClick", "whileHover", "scale", "whileTap", "width", "height", "viewBox", "fill", "d", "videosWatched", "adsBlocked", "Math", "floor", "timeSpent", "map", "video", "index", "handleVideoClick", "videoId", "src", "alt", "loading", "constructor", "this", "<PERSON><PERSON><PERSON><PERSON>", "process", "REACT_APP_YOUTUBE_API_KEY", "baseURL", "useAlternative", "realTrendingVideos", "searchVideos", "query", "maxResults", "arguments", "length", "undefined", "pageToken", "searchWithAlternativeMethod", "params", "URLSearchParams", "part", "q", "type", "toString", "key", "order", "safeSearch", "regionCode", "relevanceLanguage", "append", "response", "fetch", "ok", "Error", "status", "data", "json", "transformSearchResults", "getVideoDetails", "getVideoDetailsAlternative", "items", "transformVideoDetails", "searchResults", "generateRealisticSearchResults", "nextPageToken", "totalResults", "getMockSearchResults", "results", "usedIds", "Set", "videoPool", "i", "min", "random", "has", "add", "videoDetails", "getVideoDetailsViaOEmbed", "push", "searchQuery", "oEmbedUrl", "author_name", "description", "thumbnail_url", "generateRandomDuration", "generateRandomViews", "publishedAt", "generateRandomDate", "channelId", "substring", "details", "getMockVideoDetails", "_data$pageInfo", "item", "_item$snippet$thumbna", "_item$snippet$thumbna2", "_item$contentDetails", "_item$statistics", "snippet", "channelTitle", "thumbnails", "medium", "url", "default", "parseDuration", "contentDetails", "formatViews", "statistics", "viewCount", "pageInfo", "_item$snippet$thumbna3", "_item$snippet$thumbna4", "maxres", "high", "likes", "likeCount", "tags", "match", "parseInt", "count", "toFixed", "formatDuration", "seconds", "hours", "minutes", "secs", "padStart", "now", "Date", "daysAgo", "getTime", "toISOString", "mockVideos", "getTrendingAlternative", "chart", "videoCategoryId", "trendingVideos"], "sourceRoot": ""}