/* الخطوط والألوان الأساسية */
:root {
  --primary-bg: #1a1a1a;
  --secondary-bg: #2d2d2d;
  --accent-bg: #3d3d3d;
  --primary-text: #ffffff;
  --secondary-text: #b3b3b3;
  --accent-color: #ff0000;
  --border-color: #404040;
  --hover-bg: #404040;
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', 'Cairo', 'Tahoma', sans-serif;
  background-color: var(--primary-bg);
  color: var(--primary-text);
  direction: rtl;
  overflow: hidden;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--primary-bg);
}

.app-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--primary-bg);
}

/* شريط العنوان */
.header {
  background-color: var(--secondary-bg);
  padding: 10px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: bold;
  color: var(--accent-color);
}

.search-container {
  display: flex;
  align-items: center;
  background-color: var(--accent-bg);
  border-radius: 25px;
  padding: 8px 15px;
  min-width: 400px;
}

.search-input {
  background: none;
  border: none;
  color: var(--primary-text);
  font-size: 14px;
  width: 100%;
  outline: none;
  direction: rtl;
}

.search-input::placeholder {
  color: var(--secondary-text);
}

.search-btn {
  background: none;
  border: none;
  color: var(--secondary-text);
  cursor: pointer;
  padding: 5px;
  margin-left: 10px;
}

.search-btn:hover {
  color: var(--primary-text);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-stats {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-left: 15px;
  padding: 6px 12px;
  background-color: var(--accent-bg);
  border-radius: 15px;
}

.stat-item {
  font-size: 12px;
  color: var(--secondary-text);
  display: flex;
  align-items: center;
  gap: 4px;
}

.header-btn {
  background: none;
  border: none;
  color: var(--secondary-text);
  cursor: pointer;
  padding: 8px;
  border-radius: 5px;
  transition: all 0.3s ease;
}

.header-btn:hover {
  background-color: var(--hover-bg);
  color: var(--primary-text);
}

/* الشريط الجانبي */
.sidebar {
  width: 300px;
  background-color: var(--secondary-bg);
  border-left: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.video-item {
  display: flex;
  gap: 10px;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin-bottom: 10px;
}

.video-item:hover {
  background-color: var(--hover-bg);
}

.video-thumbnail {
  width: 80px;
  height: 60px;
  border-radius: 5px;
  object-fit: cover;
}

.video-info {
  flex: 1;
}

.video-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-channel {
  font-size: 12px;
  color: var(--secondary-text);
}

/* مشغل يوتيوب */
.youtube-player-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
  position: relative;
}

.youtube-player {
  width: 100%;
  height: 100%;
}

.player-placeholder {
  text-align: center;
  color: var(--secondary-text);
}

.player-placeholder h2 {
  font-size: 24px;
  margin-bottom: 10px;
}

.player-placeholder p {
  font-size: 16px;
}

/* نافذة الإعدادات */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-modal {
  background-color: var(--secondary-bg);
  border-radius: 10px;
  padding: 30px;
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.settings-title {
  font-size: 20px;
  font-weight: bold;
}

.close-btn {
  background: none;
  border: none;
  color: var(--secondary-text);
  cursor: pointer;
  font-size: 20px;
  padding: 5px;
}

.close-btn:hover {
  color: var(--primary-text);
}

.settings-section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: var(--accent-color);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: var(--accent-bg);
  border-radius: 8px;
}

.setting-label {
  font-size: 14px;
}

.setting-control select {
  background-color: var(--primary-bg);
  color: var(--primary-text);
  border: 1px solid var(--border-color);
  border-radius: 5px;
  padding: 5px 10px;
  font-size: 14px;
}

.toggle-switch {
  position: relative;
  width: 50px;
  height: 25px;
  background-color: var(--border-color);
  border-radius: 25px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.toggle-switch.active {
  background-color: var(--accent-color);
}

.toggle-switch::after {
  content: '';
  position: absolute;
  top: 2px;
  right: 2px;
  width: 21px;
  height: 21px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s ease;
}

.toggle-switch.active::after {
  transform: translateX(-25px);
}

/* تحسينات للاستجابة */
@media (max-width: 768px) {
  .sidebar {
    width: 250px;
  }
  
  .search-container {
    min-width: 250px;
  }
  
  .settings-modal {
    width: 90vw;
    padding: 20px;
  }
}

/* شريط التمرير المخصص */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary-bg);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--hover-bg);
}



/* عناصر التحكم المخصصة للمشغل */
.player-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.custom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 15px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.player-wrapper:hover .custom-controls {
  opacity: 1;
}

.controls-row {
  display: flex;
  align-items: center;
  gap: 15px;
  color: white;
}

.control-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 18px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.control-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.time-display {
  font-size: 14px;
  font-weight: 500;
  min-width: 100px;
  text-align: center;
}

.progress-container {
  flex: 1;
  margin: 0 10px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.progress-bar::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: var(--accent-color);
  border-radius: 50%;
  cursor: pointer;
}

.volume-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-bar {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.volume-bar::-webkit-slider-thumb {
  appearance: none;
  width: 10px;
  height: 10px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
}

.video-info-overlay {
  position: absolute;
  top: 15px;
  left: 15px;
  background: rgba(0, 0, 0, 0.7);
  padding: 10px 15px;
  border-radius: 8px;
  color: white;
  max-width: 300px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.player-wrapper:hover .video-info-overlay {
  opacity: 1;
}

.video-info-overlay h3 {
  font-size: 14px;
  margin-bottom: 5px;
  line-height: 1.3;
}

.video-info-overlay p {
  font-size: 12px;
  color: #ccc;
  margin: 0;
}

/* تحسينات الشريط الجانبي */
.video-item {
  position: relative;
}

.video-duration {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
}

.video-views {
  font-size: 11px;
  color: var(--secondary-text);
  margin-top: 2px;
}

.video-published {
  font-size: 11px;
  color: var(--secondary-text);
}

/* تحسينات الإعدادات */
.settings-section {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 20px;
}

.settings-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.quality-preview {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
  padding: 10px;
  background-color: var(--primary-bg);
  border-radius: 5px;
}

.quality-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--success-color);
}

.quality-description {
  font-size: 12px;
  color: var(--secondary-text);
}

/* حالات التحميل */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  border-top-color: var(--accent-color);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.error-message {
  background-color: var(--error-color);
  color: white;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  text-align: center;
}

.success-message {
  background-color: var(--success-color);
  color: white;
  padding: 10px;
  border-radius: 5px;
  margin: 10px 0;
  text-align: center;
}

/* تحسينات الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .controls-row {
    gap: 8px;
  }
  
  .time-display {
    font-size: 12px;
    min-width: 80px;
  }
  
  .volume-container {
    display: none;
  }
  
  .video-info-overlay {
    max-width: 200px;
    font-size: 12px;
  }
}

/* تأثيرات الانتقال */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}


/* شريط الحالة */
.status-bar {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 8px 20px;
  background-color: var(--secondary-bg);
  border-top: 1px solid var(--border-color);
  font-size: 12px;
  color: var(--secondary-text);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* رسالة الترحيب */
.welcome-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.5s ease-in;
}

.welcome-content {
  background-color: var(--secondary-bg);
  border-radius: 15px;
  padding: 40px;
  text-align: center;
  max-width: 500px;
  border: 2px solid var(--accent-color);
}

.welcome-content h2 {
  color: var(--accent-color);
  margin-bottom: 20px;
  font-size: 24px;
}

.welcome-content p {
  margin-bottom: 20px;
  color: var(--primary-text);
}

.welcome-content ul {
  text-align: right;
  margin: 20px 0;
  padding-right: 20px;
}

.welcome-content li {
  margin-bottom: 10px;
  color: var(--primary-text);
}

.welcome-btn {
  background-color: var(--accent-color);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.welcome-btn:hover {
  background-color: #cc0000;
  transform: translateY(-2px);
}

/* تحسينات للفيديو النشط */
.video-item.active {
  background-color: var(--accent-color);
  color: white;
}

.video-item.active .video-channel,
.video-item.active .video-views,
.video-item.active .video-published {
  color: rgba(255, 255, 255, 0.8);
}

/* ثيم فاتح (اختياري) */
.app.light-theme {
  --primary-bg: #ffffff;
  --secondary-bg: #f5f5f5;
  --accent-bg: #e0e0e0;
  --primary-text: #000000;
  --secondary-text: #666666;
  --border-color: #cccccc;
  --hover-bg: #e0e0e0;
}

/* تحسينات الأداء */
.video-thumbnail {
  transition: transform 0.2s ease;
}

.video-item:hover .video-thumbnail {
  transform: scale(1.05);
}

/* مؤشرات التحميل المحسنة */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 30px;
}

.loading-text {
  color: var(--secondary-text);
  font-size: 14px;
}

/* تحسينات الإعدادات المتقدمة */
.advanced-settings {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.stat-card {
  background-color: var(--accent-bg);
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: var(--accent-color);
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: var(--secondary-text);
}

/* تأثيرات الانتقال المحسنة */
.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.bounce-in {
  animation: bounceIn 0.5s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* تحسينات إمكانية الوصول */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* تحسينات للطباعة */
@media print {
  .sidebar,
  .header,
  .status-bar,
  .settings-overlay {
    display: none;
  }
  
  .main-content {
    width: 100%;
  }
}


/* لوحة تحكم المطور */
.developer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.developer-panel {
  background-color: var(--secondary-bg);
  border-radius: 15px;
  padding: 0;
  width: 90vw;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  border: 2px solid var(--accent-color);
}

.panel-header {
  background-color: var(--accent-color);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h2 {
  margin: 0;
  font-size: 20px;
}

.panel-tabs {
  display: flex;
  background-color: var(--accent-bg);
  border-bottom: 1px solid var(--border-color);
}

.tab-btn {
  flex: 1;
  background: none;
  border: none;
  color: var(--secondary-text);
  padding: 15px 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.tab-btn:hover {
  background-color: var(--hover-bg);
  color: var(--primary-text);
}

.tab-btn.active {
  background-color: var(--primary-bg);
  color: var(--accent-color);
  border-bottom: 2px solid var(--accent-color);
}

.tab-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

/* إحصائيات الأرباح */
.revenue-overview {
  margin-bottom: 30px;
}

.revenue-card {
  background: linear-gradient(135deg, var(--accent-color), #cc0000);
  color: white;
  padding: 25px;
  border-radius: 15px;
  text-align: center;
  margin-bottom: 20px;
}

.revenue-card.main {
  font-size: 18px;
}

.revenue-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.revenue-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 5px;
}

.revenue-label {
  font-size: 14px;
  opacity: 0.9;
}

/* شبكة الإحصائيات */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-bottom: 30px;
}

/* نماذج الإضافة */
.add-ad-section,
.add-affiliate-section {
  background-color: var(--accent-bg);
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--primary-text);
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--border-color);
  border-radius: 5px;
  background-color: var(--primary-bg);
  color: var(--primary-text);
  font-size: 14px;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.add-btn {
  background-color: var(--accent-color);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background-color: #cc0000;
  transform: translateY(-2px);
}

/* معاينة الإعلان */
.ad-preview {
  background-color: var(--accent-bg);
  padding: 20px;
  border-radius: 10px;
  margin-top: 20px;
}

.preview-ad {
  border: 2px dashed var(--border-color);
  padding: 15px;
  border-radius: 8px;
  background-color: var(--primary-bg);
}

.ad-content {
  position: relative;
  padding: 15px;
  background-color: var(--secondary-bg);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ad-content:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.ad-text h5 {
  margin: 0 0 8px 0;
  color: var(--primary-text);
  font-size: 16px;
}

.ad-text p {
  margin: 0;
  color: var(--secondary-text);
  font-size: 14px;
  line-height: 1.4;
}

.ad-label {
  position: absolute;
  top: 5px;
  left: 5px;
  background-color: var(--accent-color);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
}

/* الإعلانات المدمجة */
.integrated-banner-ad {
  margin: 15px 0;
  border-radius: 8px;
  overflow: hidden;
  animation: slideUp 0.5s ease-out;
}

.integrated-sidebar-ad {
  margin: 10px 0;
  border-radius: 8px;
  overflow: hidden;
  font-size: 12px;
}

.integrated-sidebar-ad .ad-content {
  padding: 10px;
}

.integrated-sidebar-ad .ad-text h5 {
  font-size: 13px;
  margin-bottom: 5px;
}

.integrated-sidebar-ad .ad-text p {
  font-size: 11px;
}

/* الروابط التابعة */
.affiliate-link {
  color: var(--accent-color);
  text-decoration: none;
  border-bottom: 1px dotted var(--accent-color);
  transition: all 0.3s ease;
}

.affiliate-link:hover {
  color: #cc0000;
  border-bottom-style: solid;
}

/* نصائح الربح */
.monetization-tips,
.affiliate-info {
  background-color: var(--accent-bg);
  padding: 20px;
  border-radius: 10px;
  margin-top: 20px;
}

.monetization-tips h4,
.affiliate-info h4 {
  color: var(--accent-color);
  margin-bottom: 15px;
  font-size: 16px;
}

.monetization-tips ul,
.affiliate-info ul {
  list-style: none;
  padding: 0;
}

.monetization-tips li,
.affiliate-info li {
  padding: 8px 0;
  border-bottom: 1px solid var(--border-color);
  color: var(--primary-text);
  font-size: 14px;
}

.monetization-tips li:last-child,
.affiliate-info li:last-child {
  border-bottom: none;
}

/* إعدادات الربح */
.monetization-settings {
  background-color: var(--accent-bg);
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 20px;
}

.monetization-settings h4 {
  color: var(--accent-color);
  margin-bottom: 20px;
}

/* تصدير البيانات */
.export-section {
  background-color: var(--accent-bg);
  padding: 20px;
  border-radius: 10px;
  text-align: center;
}

.export-btn {
  background-color: var(--success-color);
  color: white;
  border: none;
  padding: 12px 25px;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.export-btn:hover {
  background-color: #45a049;
  transform: translateY(-2px);
}

/* رسائل النجاح والخطأ */
.message {
  padding: 12px;
  border-radius: 8px;
  margin: 15px 0;
  text-align: center;
  font-weight: 500;
}

.message.success {
  background-color: var(--success-color);
  color: white;
}

.message.error {
  background-color: var(--error-color);
  color: white;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .developer-panel {
    width: 95vw;
    max-height: 95vh;
  }
  
  .panel-tabs {
    flex-wrap: wrap;
  }
  
  .tab-btn {
    font-size: 12px;
    padding: 12px 8px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
  }
  
  .revenue-card {
    padding: 20px;
  }
  
  .revenue-value {
    font-size: 24px;
  }
}

/* تأثيرات الحركة */
@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}


/* معلومات المطور */
.developer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 3000;
}

.developer-info-modal {
  background-color: var(--secondary-bg);
  border-radius: 15px;
  padding: 0;
  width: 90vw;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  border: 2px solid var(--accent-color);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.developer-header {
  background: linear-gradient(135deg, var(--accent-color), #cc0000);
  color: white;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
}

.developer-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid white;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #4a90e2, #357abd);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  color: white;
}

.developer-details h2 {
  margin: 0 0 5px 0;
  font-size: 24px;
}

.developer-title {
  margin: 0 0 5px 0;
  opacity: 0.9;
  font-size: 14px;
}

.developer-channel {
  margin: 0;
  font-weight: bold;
  font-size: 16px;
}

.developer-content {
  padding: 25px;
  max-height: 60vh;
  overflow-y: auto;
}

.contact-section,
.social-section,
.about-section,
.app-info-section {
  margin-bottom: 25px;
}

.contact-section h3,
.social-section h3,
.about-section h3,
.app-info-section h3 {
  color: var(--accent-color);
  margin-bottom: 15px;
  font-size: 18px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: var(--accent-bg);
  border-radius: 10px;
}

.contact-icon {
  font-size: 24px;
}

.contact-text {
  flex: 1;
  font-size: 16px;
  font-weight: bold;
  color: var(--primary-text);
}

.contact-btn {
  background-color: var(--accent-color);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.contact-btn:hover {
  background-color: #cc0000;
  transform: translateY(-2px);
}

.social-links {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.social-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: var(--accent-bg);
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-item:hover {
  background-color: var(--hover-bg);
  transform: translateX(-5px);
}

.social-item.youtube:hover {
  border-left: 4px solid #ff0000;
}

.social-item.facebook:hover {
  border-left: 4px solid #1877f2;
}

.social-item.telegram:hover {
  border-left: 4px solid #0088cc;
}

.social-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
}

.social-info {
  flex: 1;
}

.social-name {
  font-weight: bold;
  color: var(--primary-text);
  margin-bottom: 3px;
}

.social-handle {
  font-size: 12px;
  color: var(--secondary-text);
}

.social-arrow {
  font-size: 18px;
  color: var(--secondary-text);
}

.skills-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 15px;
}

.skill-tag {
  background-color: var(--accent-color);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.app-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-top: 15px;
}

.feature-item {
  padding: 8px;
  background-color: var(--accent-bg);
  border-radius: 6px;
  font-size: 14px;
  color: var(--primary-text);
}

.developer-footer {
  background-color: var(--accent-bg);
  padding: 20px;
  text-align: center;
  border-top: 1px solid var(--border-color);
}

.developer-footer p {
  margin: 5px 0;
  color: var(--secondary-text);
}

/* معلومات المطور المدمجة */
.developer-info-compact {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.developer-credit {
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: var(--secondary-bg);
  padding: 8px 12px;
  border-radius: 20px;
  border: 1px solid var(--border-color);
  font-size: 12px;
  color: var(--secondary-text);
}

.credit-text {
  color: var(--primary-text);
}

.info-toggle-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.info-toggle-btn:hover {
  background-color: var(--hover-bg);
}

/* تحسينات الشريط الجانبي */
.sidebar-tabs {
  display: flex;
  background-color: var(--accent-bg);
  border-bottom: 1px solid var(--border-color);
}

.tab-btn {
  flex: 1;
  background: none;
  border: none;
  color: var(--secondary-text);
  padding: 12px 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 13px;
}

.tab-btn:hover {
  background-color: var(--hover-bg);
  color: var(--primary-text);
}

.tab-btn.active {
  background-color: var(--primary-bg);
  color: var(--accent-color);
  border-bottom: 2px solid var(--accent-color);
}

.channel-header {
  padding: 20px 15px;
  background: linear-gradient(135deg, var(--accent-color), #cc0000);
  color: white;
  margin-bottom: 15px;
}

.channel-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin: 0 auto 15px;
  overflow: hidden;
  border: 3px solid white;
}

.channel-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: bold;
}

.channel-name {
  text-align: center;
  margin: 0 0 5px 0;
  font-size: 18px;
}

.channel-subscribers {
  text-align: center;
  margin: 0 0 10px 0;
  font-size: 14px;
  opacity: 0.9;
}

.channel-description {
  text-align: center;
  margin: 0 0 15px 0;
  font-size: 12px;
  opacity: 0.8;
  line-height: 1.4;
}

.subscribe-btn {
  display: block;
  width: 100%;
  background-color: white;
  color: var(--accent-color);
  border: none;
  padding: 10px;
  border-radius: 6px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.subscribe-btn:hover {
  background-color: #f0f0f0;
  transform: translateY(-2px);
}

.section-title {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color);
}

.section-title h4 {
  margin: 0;
  color: var(--primary-text);
  font-size: 16px;
}

.sidebar-footer {
  border-top: 1px solid var(--border-color);
  padding: 15px;
  background-color: var(--accent-bg);
}

.sidebar-footer .developer-credit {
  position: static;
  background: none;
  border: none;
  padding: 0;
  border-radius: 0;
  text-align: center;
}

.sidebar-footer .developer-credit p {
  margin: 3px 0;
  font-size: 11px;
  color: var(--secondary-text);
}

.sidebar-footer .developer-credit strong {
  color: var(--accent-color);
}

/* تحسينات رسالة الترحيب */
.welcome-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
}

.developer-btn {
  background-color: transparent;
  color: var(--accent-color);
  border: 2px solid var(--accent-color);
  padding: 12px 25px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.developer-btn:hover {
  background-color: var(--accent-color);
  color: white;
  transform: translateY(-2px);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .developer-info-modal {
    width: 95vw;
    max-height: 95vh;
  }
  
  .developer-header {
    padding: 20px;
    flex-direction: column;
    text-align: center;
  }
  
  .developer-content {
    padding: 20px;
  }
  
  .social-links {
    gap: 10px;
  }
  
  .social-item {
    padding: 12px;
  }
  
  .app-features {
    grid-template-columns: 1fr;
  }
  
  .welcome-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .developer-info-compact {
    bottom: 10px;
    right: 10px;
  }
}

/* أنماط الواجهة الهجينة */
.hybrid-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #0f0f0f;
}

.youtube-webview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  margin: 10px;
}

.webview-navigation {
  display: flex;
  align-items: center;
  background: #1a1a1a;
  padding: 8px 12px;
  border-bottom: 1px solid #333;
  gap: 8px;
}

.nav-btn {
  background: #333;
  border: none;
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s;
}

.nav-btn:hover:not(:disabled) {
  background: #555;
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.url-display {
  flex: 1;
  background: #2a2a2a;
  color: #ccc;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.loading-indicator {
  color: #4CAF50;
  font-size: 12px;
  margin-left: 10px;
}

.youtube-webview {
  flex: 1;
  width: 100%;
  height: 100%;
  border: none;
  background: #000;
}

/* تحسينات شريط الحالة */
.status-bar {
  display: flex;
  align-items: center;
  background: #1a1a1a;
  padding: 8px 16px;
  border-top: 1px solid #333;
  gap: 20px;
  font-size: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ccc;
}

.status-item span:first-child {
  font-size: 14px;
}

/* تحسينات الثيم الداكن */
.dark-theme {
  background: #0f0f0f;
  color: #fff;
}

.dark-theme .header {
  background: #1a1a1a;
  border-bottom: 1px solid #333;
}

.dark-theme .sidebar {
  background: #1a1a1a;
  border-right: 1px solid #333;
}

.dark-theme .main-content {
  background: #0f0f0f;
}

/* تحسينات متجاوبة */
@media (max-width: 768px) {
  .webview-navigation {
    padding: 6px 8px;
    gap: 6px;
  }

  .nav-btn {
    padding: 4px 8px;
    font-size: 12px;
  }

  .url-display {
    font-size: 11px;
    padding: 4px 8px;
  }

  .status-bar {
    padding: 6px 12px;
    gap: 15px;
    font-size: 11px;
  }
}

/* تحسينات الأداء */
.youtube-webview-container {
  will-change: transform;
}

.youtube-webview {
  will-change: contents;
}

