import React from 'react';
import './Home.css';

const HomeSimple = () => {
  // Real YouTube videos with verified IDs - these are actual videos that exist
  const realVideos = [
    {
      id: 'dQw4w9WgXcQ',
      title: '<PERSON> - Never Gonna Give You Up (Official Video)',
      channel: '<PERSON>',
      duration: '3:33',
      views: '1.4B',
      thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
    },
    {
      id: 'kJQP7kiw5Fk',
      title: '<PERSON>cito ft. Daddy Yankee',
      channel: '<PERSON>',
      duration: '4:42',
      views: '8.1B',
      thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/maxresdefault.jpg'
    },
    {
      id: 'fJ9rUzIMcZQ',
      title: 'Wiz <PERSON>hali<PERSON> - See You Again ft. <PERSON>',
      channel: 'Wiz <PERSON><PERSON>',
      duration: '3:57',
      views: '5.9B',
      thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/maxresdefault.jpg'
    },
    {
      id: 'JGwWNGJdvx8',
      title: 'Ed Sheeran - Shape of You',
      channel: 'Ed Sheeran',
      duration: '3:53',
      views: '5.8B',
      thumbnail: 'https://img.youtube.com/vi/JGwWNGJdvx8/maxresdefault.jpg'
    },
    {
      id: 'RgKAFK5djSk',
      title: 'PSY - GANGNAM STYLE',
      channel: 'officialpsy',
      duration: '4:12',
      views: '4.7B',
      thumbnail: 'https://img.youtube.com/vi/RgKAFK5djSk/maxresdefault.jpg'
    },
    {
      id: 'CevxZvSJLk8',
      title: 'Katy Perry - Roar (Official)',
      channel: 'Katy Perry',
      duration: '3:43',
      views: '3.7B',
      thumbnail: 'https://img.youtube.com/vi/CevxZvSJLk8/maxresdefault.jpg'
    }
  ];

  const handleVideoClick = (videoId) => {
    // Open YouTube video in new window
    window.open(`https://www.youtube.com/watch?v=${videoId}`, '_blank');
  };

  return (
    <div className="home-page">
      <div className="home-container">
        <header className="home-header">
          <h1 className="home-title">مرحباً بك في مشغل يوتيوب المتقدم</h1>
          <p className="home-subtitle">اكتشف أفضل الفيديوهات مع تجربة مشاهدة خالية من الإعلانات</p>
        </header>

        <section className="featured-section">
          <h2 className="section-title">الفيديوهات المميزة</h2>
          <div className="videos-grid">
            {realVideos.map((video) => (
              <div 
                key={video.id} 
                className="video-card"
                onClick={() => handleVideoClick(video.id)}
                style={{ cursor: 'pointer' }}
              >
                <div className="video-thumbnail">
                  <img 
                    src={video.thumbnail} 
                    alt={video.title}
                    onError={(e) => {
                      e.target.src = `https://img.youtube.com/vi/${video.id}/mqdefault.jpg`;
                    }}
                  />
                  <div className="video-duration">{video.duration}</div>
                </div>
                <div className="video-info">
                  <h3 className="video-title">{video.title}</h3>
                  <div className="video-meta">
                    <span className="video-channel">{video.channel}</span>
                    <span className="video-views">{video.views} مشاهدة</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        <section className="features-section">
          <h2 className="section-title">الميزات الرئيسية</h2>
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">🚫</div>
              <h3>مانع الإعلانات</h3>
              <p>تجربة مشاهدة خالية من الإعلانات المزعجة</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">🎵</div>
              <h3>جودة عالية</h3>
              <p>استمتع بالفيديوهات بأعلى جودة متاحة</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">📱</div>
              <h3>واجهة عربية</h3>
              <p>تصميم مُحسّن للغة العربية مع دعم RTL</p>
            </div>
            <div className="feature-card">
              <div className="feature-icon">⚡</div>
              <h3>سرعة فائقة</h3>
              <p>تحميل سريع وأداء محسّن</p>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
};

export default HomeSimple;
