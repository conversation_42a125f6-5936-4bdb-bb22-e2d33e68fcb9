"use strict";(self.webpackChunkyoutube_player_pro=self.webpackChunkyoutube_player_pro||[]).push([[781],{7494:(e,t,s)=>{s.d(t,{A:()=>i});const i=new class{constructor(){this.apiKey={NODE_ENV:"production",PUBLIC_URL:".",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_YOUTUBE_API_KEY||"DEMO_KEY",this.baseURL="https://www.googleapis.com/youtube/v3",this.useProxy=!{NODE_ENV:"production",PUBLIC_URL:".",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_YOUTUBE_API_KEY,this.proxyURL="https://youtube-proxy-api.herokuapp.com"}async searchVideos(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:25,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";try{if(this.useProxy)return await this.searchWithProxy(e,t,s);const i=new URLSearchParams({part:"snippet",q:e,type:"video",maxResults:t.toString(),key:this.apiKey,order:"relevance",safeSearch:"moderate",regionCode:"SA",relevanceLanguage:"ar"});s&&i.append("pageToken",s);const r=await fetch(`${this.baseURL}/search?${i}`);if(!r.ok)throw new Error(`YouTube API error: ${r.status}`);const a=await r.json();return this.transformSearchResults(a)}catch(i){return console.error("YouTube search error:",i),this.getMockSearchResults(e)}}async getVideoDetails(e){try{if(this.useProxy)return await this.getVideoDetailsWithProxy(e);const t=new URLSearchParams({part:"snippet,statistics,contentDetails",id:e,key:this.apiKey}),s=await fetch(`${this.baseURL}/videos?${t}`);if(!s.ok)throw new Error(`YouTube API error: ${s.status}`);const i=await s.json();if(i.items&&i.items.length>0)return this.transformVideoDetails(i.items[0]);throw new Error("Video not found")}catch(t){return console.error("YouTube video details error:",t),this.getMockVideoDetails(e)}}async searchWithProxy(e,t,s){try{const i=new URLSearchParams({q:e,maxResults:t.toString(),type:"video"});s&&i.append("pageToken",s);const r=await fetch(`${this.proxyURL}/search?${i}`);if(!r.ok)throw new Error(`Proxy API error: ${r.status}`);const a=await r.json();return this.transformSearchResults(a)}catch(i){return console.error("Proxy search error:",i),this.getMockSearchResults(e)}}async getVideoDetailsWithProxy(e){try{const t=await fetch(`${this.proxyURL}/video/${e}`);if(!t.ok)throw new Error(`Proxy API error: ${t.status}`);const s=await t.json();return this.transformVideoDetails(s)}catch(t){return console.error("Proxy video details error:",t),this.getMockVideoDetails(e)}}transformSearchResults(e){var t;if(!e.items)return{videos:[],nextPageToken:null,totalResults:0};return{videos:e.items.map(e=>{var t,s,i,r;return{id:e.id.videoId||e.id,title:e.snippet.title,channel:e.snippet.channelTitle,description:e.snippet.description,thumbnail:(null===(t=e.snippet.thumbnails.medium)||void 0===t?void 0:t.url)||(null===(s=e.snippet.thumbnails.default)||void 0===s?void 0:s.url),publishedAt:e.snippet.publishedAt,duration:this.parseDuration(null===(i=e.contentDetails)||void 0===i?void 0:i.duration),views:this.formatViews(null===(r=e.statistics)||void 0===r?void 0:r.viewCount),channelId:e.snippet.channelId}}),nextPageToken:e.nextPageToken||null,totalResults:(null===(t=e.pageInfo)||void 0===t?void 0:t.totalResults)||0}}transformVideoDetails(e){var t,s;return{id:e.id,title:e.snippet.title,channel:e.snippet.channelTitle,description:e.snippet.description,thumbnail:(null===(t=e.snippet.thumbnails.maxres)||void 0===t?void 0:t.url)||(null===(s=e.snippet.thumbnails.high)||void 0===s?void 0:s.url),publishedAt:e.snippet.publishedAt,duration:this.parseDuration(e.contentDetails.duration),views:this.formatViews(e.statistics.viewCount),likes:this.formatViews(e.statistics.likeCount),channelId:e.snippet.channelId,tags:e.snippet.tags||[]}}parseDuration(e){if(!e)return 0;const t=e.match(/PT(\d+H)?(\d+M)?(\d+S)?/);if(!t)return 0;return 3600*(parseInt(t[1])||0)+60*(parseInt(t[2])||0)+(parseInt(t[3])||0)}formatViews(e){if(!e)return"0";const t=parseInt(e);return t>=1e6?`${(t/1e6).toFixed(1)}M`:t>=1e3?`${(t/1e3).toFixed(1)}K`:t.toString()}formatDuration(e){if(!e)return"0:00";const t=Math.floor(e/3600),s=Math.floor(e%3600/60),i=e%60;return t>0?`${t}:${s.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`:`${s}:${i.toString().padStart(2,"0")}`}getMockSearchResults(e){const t=[{id:"dQw4w9WgXcQ",title:`${e} - \u0646\u062a\u064a\u062c\u0629 \u0627\u0644\u0628\u062d\u062b \u0627\u0644\u0623\u0648\u0644\u0649`,channel:"\u0642\u0646\u0627\u0629 \u062a\u062c\u0631\u064a\u0628\u064a\u0629",description:"\u0648\u0635\u0641 \u0645\u0641\u0635\u0644 \u0644\u0644\u0641\u064a\u062f\u064a\u0648 \u0627\u0644\u0623\u0648\u0644 \u0641\u064a \u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0628\u062d\u062b...",thumbnail:"https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg",duration:"3:32",views:"1.2M",publishedAt:"2023-01-15T10:00:00Z",channelId:"UC123456789"},{id:"kJQP7kiw5Fk",title:`\u0634\u0631\u062d ${e} \u0628\u0627\u0644\u062a\u0641\u0635\u064a\u0644`,channel:"\u0642\u0646\u0627\u0629 \u0627\u0644\u062a\u0639\u0644\u064a\u0645",description:"\u0634\u0631\u062d \u0634\u0627\u0645\u0644 \u0648\u0645\u0641\u0635\u0644 \u062d\u0648\u0644 \u0627\u0644\u0645\u0648\u0636\u0648\u0639 \u0627\u0644\u0645\u0637\u0644\u0648\u0628...",thumbnail:"https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg",duration:"4:42",views:"850K",publishedAt:"2023-02-20T15:30:00Z",channelId:"UC987654321"},{id:"fJ9rUzIMcZQ",title:`\u0623\u0641\u0636\u0644 ${e} \u0644\u0647\u0630\u0627 \u0627\u0644\u0639\u0627\u0645`,channel:"\u0642\u0646\u0627\u0629 \u0627\u0644\u0645\u0631\u0627\u062c\u0639\u0627\u062a",description:"\u0645\u0631\u0627\u062c\u0639\u0629 \u0634\u0627\u0645\u0644\u0629 \u0644\u0623\u0641\u0636\u0644 \u0627\u0644\u062e\u064a\u0627\u0631\u0627\u062a \u0627\u0644\u0645\u062a\u0627\u062d\u0629...",thumbnail:"https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg",duration:"5:55",views:"2.1M",publishedAt:"2023-03-10T12:15:00Z",channelId:"UC456789123"}];return{videos:t,nextPageToken:null,totalResults:t.length}}getMockVideoDetails(e){return{id:e,title:"\u0641\u064a\u062f\u064a\u0648 \u064a\u0648\u062a\u064a\u0648\u0628 \u062a\u062c\u0631\u064a\u0628\u064a",channel:"\u0642\u0646\u0627\u0629 \u062a\u062c\u0631\u064a\u0628\u064a\u0629",description:"\u0648\u0635\u0641 \u062a\u062c\u0631\u064a\u0628\u064a \u0644\u0644\u0641\u064a\u062f\u064a\u0648...",thumbnail:`https://img.youtube.com/vi/${e}/maxresdefault.jpg`,duration:"3:45",views:"1.5M",likes:"25K",publishedAt:"2023-01-01T00:00:00Z",channelId:"UC123456789",tags:["\u062a\u062c\u0631\u064a\u0628\u064a","\u0641\u064a\u062f\u064a\u0648","\u064a\u0648\u062a\u064a\u0648\u0628"]}}async getTrendingVideos(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"SA",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:25;try{if(this.useProxy)return await this.getTrendingWithProxy(e,t);const s=new URLSearchParams({part:"snippet,statistics,contentDetails",chart:"mostPopular",regionCode:e,maxResults:t.toString(),key:this.apiKey,videoCategoryId:"0"}),i=await fetch(`${this.baseURL}/videos?${s}`);if(!i.ok)throw new Error(`YouTube API error: ${i.status}`);const r=await i.json();return this.transformSearchResults(r)}catch(s){return console.error("YouTube trending error:",s),this.getMockSearchResults("\u0627\u0644\u0623\u0643\u062b\u0631 \u0645\u0634\u0627\u0647\u062f\u0629")}}async getTrendingWithProxy(e,t){try{const s=new URLSearchParams({regionCode:e,maxResults:t.toString()}),i=await fetch(`${this.proxyURL}/trending?${s}`);if(!i.ok)throw new Error(`Proxy API error: ${i.status}`);const r=await i.json();return this.transformSearchResults(r)}catch(s){return console.error("Proxy trending error:",s),this.getMockSearchResults("\u0627\u0644\u0623\u0643\u062b\u0631 \u0645\u0634\u0627\u0647\u062f\u0629")}}}},8781:(e,t,s)=>{s.r(t),s.d(t,{default:()=>h});var i=s(5043),r=s(1228),a=s(663),n=s(8680),o=s(6481),l=s(7494),c=s(579);const h=e=>{let{routeParams:t}=e;const[s,h]=(0,i.useState)([]),[d,u]=(0,i.useState)(!1),[p,m]=(0,i.useState)(null),{search:{query:v,history:x},setSearchQuery:y,addToSearchHistory:g,setSearchResults:w}=(0,a.A)(),{navigate:f}=(0,n.Ay)(),j=(null===t||void 0===t?void 0:t.query)||v||"";(0,i.useEffect)(()=>{j&&(y(j),S(j))},[j]);const S=async e=>{if(e.trim()){u(!0),m(null);try{const t=await l.A.searchVideos(e,20);h(t.videos),w(t.videos),g(e)}catch(t){m("\u0641\u0634\u0644 \u0641\u064a \u0627\u0644\u0628\u062d\u062b. \u064a\u0631\u062c\u0649 \u0627\u0644\u0645\u062d\u0627\u0648\u0644\u0629 \u0645\u0631\u0629 \u0623\u062e\u0631\u0649."),console.error("Search error:",t)}finally{u(!1)}}},P=e=>{const t=new Date(e),s=new Date,i=Math.abs(s-t),r=Math.ceil(i/864e5);return 1===r?"\u0623\u0645\u0633":r<7?`\u0645\u0646\u0630 ${r} \u0623\u064a\u0627\u0645`:r<30?`\u0645\u0646\u0630 ${Math.floor(r/7)} \u0623\u0633\u0627\u0628\u064a\u0639`:r<365?`\u0645\u0646\u0630 ${Math.floor(r/30)} \u0623\u0634\u0647\u0631`:`\u0645\u0646\u0630 ${Math.floor(r/365)} \u0633\u0646\u0648\u0627\u062a`};return(0,c.jsxs)("div",{className:"search-page",children:[(0,c.jsxs)("div",{className:"search-header",children:[(0,c.jsx)("h1",{children:"\u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0628\u062d\u062b"}),v&&(0,c.jsxs)("p",{className:"search-query",children:["\u0627\u0644\u0628\u062d\u062b \u0639\u0646: ",(0,c.jsxs)("strong",{children:['"',v,'"']})]})]}),d&&(0,c.jsx)("div",{className:"search-loading",children:(0,c.jsx)(o.A,{text:"\u062c\u0627\u0631\u064a \u0627\u0644\u0628\u062d\u062b..."})}),p&&(0,c.jsxs)("div",{className:"search-error",children:[(0,c.jsx)("div",{className:"error-icon",children:(0,c.jsx)("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"currentColor",children:(0,c.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}),(0,c.jsx)("h3",{children:"\u062e\u0637\u0623 \u0641\u064a \u0627\u0644\u0628\u062d\u062b"}),(0,c.jsx)("p",{children:p}),(0,c.jsx)("button",{className:"retry-button",onClick:()=>S(v),children:"\u0625\u0639\u0627\u062f\u0629 \u0627\u0644\u0645\u062d\u0627\u0648\u0644\u0629"})]}),!d&&!p&&s.length>0&&(0,c.jsxs)(r.P.div,{className:"search-results",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},children:[(0,c.jsx)("div",{className:"results-info",children:(0,c.jsxs)("span",{children:["\u062a\u0645 \u0627\u0644\u0639\u062b\u0648\u0631 \u0639\u0644\u0649 ",s.length," \u0646\u062a\u064a\u062c\u0629"]})}),(0,c.jsx)("div",{className:"results-list",children:s.map((e,t)=>(0,c.jsxs)(r.P.div,{className:"result-item",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},onClick:()=>{return t=e.id,void f("player",{videoId:t});var t},children:[(0,c.jsxs)("div",{className:"result-thumbnail",children:[(0,c.jsx)("img",{src:e.thumbnail,alt:e.title,loading:"lazy"}),(0,c.jsx)("div",{className:"video-duration",children:e.duration}),(0,c.jsx)("div",{className:"play-overlay",children:(0,c.jsx)("svg",{width:"32",height:"32",viewBox:"0 0 24 24",fill:"currentColor",children:(0,c.jsx)("path",{d:"M8 5v14l11-7z"})})})]}),(0,c.jsxs)("div",{className:"result-content",children:[(0,c.jsx)("h3",{className:"result-title",children:e.title}),(0,c.jsxs)("div",{className:"result-meta",children:[(0,c.jsx)("span",{className:"result-channel",children:e.channel}),(0,c.jsxs)("span",{className:"result-views",children:[e.views," \u0645\u0634\u0627\u0647\u062f\u0629"]}),(0,c.jsx)("span",{className:"result-date",children:P(e.publishedAt)})]}),(0,c.jsx)("p",{className:"result-description",children:e.description})]})]},e.id))})]}),!d&&!p&&0===s.length&&v&&(0,c.jsxs)("div",{className:"search-empty",children:[(0,c.jsx)("div",{className:"empty-icon",children:(0,c.jsx)("svg",{width:"64",height:"64",viewBox:"0 0 24 24",fill:"currentColor",children:(0,c.jsx)("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"})})}),(0,c.jsx)("h2",{children:"\u0644\u0627 \u062a\u0648\u062c\u062f \u0646\u062a\u0627\u0626\u062c"}),(0,c.jsx)("p",{children:"\u0644\u0645 \u0646\u062a\u0645\u0643\u0646 \u0645\u0646 \u0627\u0644\u0639\u062b\u0648\u0631 \u0639\u0644\u0649 \u0623\u064a \u0641\u064a\u062f\u064a\u0648\u0647\u0627\u062a \u062a\u0637\u0627\u0628\u0642 \u0628\u062d\u062b\u0643"}),(0,c.jsxs)("div",{className:"search-suggestions",children:[(0,c.jsx)("h4",{children:"\u062c\u0631\u0628:"}),(0,c.jsxs)("ul",{children:[(0,c.jsx)("li",{children:"\u0627\u0644\u062a\u0623\u0643\u062f \u0645\u0646 \u0635\u062d\u0629 \u0627\u0644\u0643\u062a\u0627\u0628\u0629"}),(0,c.jsx)("li",{children:"\u0627\u0633\u062a\u062e\u062f\u0627\u0645 \u0643\u0644\u0645\u0627\u062a \u0645\u062e\u062a\u0644\u0641\u0629"}),(0,c.jsx)("li",{children:"\u0627\u0633\u062a\u062e\u062f\u0627\u0645 \u0643\u0644\u0645\u0627\u062a \u0623\u0643\u062b\u0631 \u0639\u0645\u0648\u0645\u064a\u0629"})]})]})]}),!v&&x.length>0&&(0,c.jsxs)("div",{className:"search-history",children:[(0,c.jsx)("h2",{children:"\u0639\u0645\u0644\u064a\u0627\u062a \u0627\u0644\u0628\u062d\u062b \u0627\u0644\u0633\u0627\u0628\u0642\u0629"}),(0,c.jsx)("div",{className:"history-list",children:x.slice(0,10).map((e,t)=>(0,c.jsxs)(r.P.button,{className:"history-item",onClick:()=>{var t;(t=e).trim()&&(y(t),S(t))},initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.05*t},children:[(0,c.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor",children:(0,c.jsx)("path",{d:"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"})}),(0,c.jsx)("span",{children:e})]},`history-${e}-${t}`))})]})]})}}}]);
//# sourceMappingURL=781.d35ec3e3.chunk.js.map