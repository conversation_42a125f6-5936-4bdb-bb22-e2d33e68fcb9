{"version": 3, "file": "static/css/695.d882fe01.chunk.css", "mappings": "AACA,WAGE,aAAc,CADd,gBAAiB,CADjB,yBAGF,CAGA,cAGE,2BAAuB,CACvB,kBAAmB,CAHnB,YAAa,CAEb,sBAAuB,CADvB,6BAA8B,CAG9B,gCAAiC,CACjC,gBACF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,qBACF,CAEA,YAGE,yBAA0B,CAF1B,8BAA+B,CAC/B,mCAAoC,CAEpC,oCAAqC,CACrC,QACF,CAEA,kBAEE,2BAA4B,CAD5B,6BAA8B,CAE9B,sCAAuC,CACvC,QACF,CAEA,cACE,YAAa,CAEb,cAAe,CADf,qBAEF,CAEA,aAEE,kBAAmB,CAGnB,WAAY,CACZ,8BAA+B,CAI/B,cAAe,CATf,YAAa,CAQb,sCAAuC,CAFvC,6BAA8B,CAC9B,qCAAsC,CALtC,qBAAsB,CAStB,eAAgB,CARhB,2CAA4C,CAO5C,iCAEF,CAEA,qBACE,qCAAsC,CACtC,UACF,CAEA,2BACE,qCAAsC,CACtC,2BACF,CAEA,uBACE,wBAA6B,CAE7B,sCAAuC,CADvC,yBAEF,CAEA,6BACE,qCAAsC,CACtC,gCACF,CAEA,YAGE,kBAAmB,CAFnB,YAAa,CACb,sBAEF,CAEA,oBAGE,mFAAsF,CAEtF,sCAAuC,CADvC,8BAA+B,CAK/B,2BAA4B,CAH5B,YAAa,CACb,qBAAsB,CALtB,YAAa,CAMb,eAAgB,CAPhB,WASF,CAEA,sBAKE,kDAAqD,CACrD,0BAA2B,CAL3B,QAMF,CAEA,8CANE,kBAAmB,CADnB,YAAa,CAEb,sBAaF,CARA,wBAEE,uCAAwC,CAIxC,qBAAsB,CALtB,WAAY,CAMZ,2BACF,CAEA,gBAGE,qCAAsC,CACtC,gCAAiC,CAFjC,UAAW,CADX,SAIF,CAGA,eACE,gCACF,CAEA,eAGE,yBAA0B,CAF1B,8BAA+B,CAC/B,uCAAwC,CAExC,+BAAgC,CAChC,iBACF,CAEA,YAGE,0BAAsB,CAFtB,YAAa,CAEb,qBAAsB,CADtB,wDAEF,CAEA,WAEE,kBAAmB,CAGnB,yCAA0C,CAE1C,sCAAuC,CADvC,8BAA+B,CAL/B,YAAa,CAEb,qBAAsB,CACtB,yBAA0B,CAI1B,iCACF,CAEA,iBAEE,gCAAiC,CADjC,2BAEF,CAEA,WAKE,kBAAmB,CAFnB,8BAA+B,CAI/B,UAAY,CAHZ,YAAa,CAFb,WAAY,CAIZ,sBAAuB,CALvB,UAOF,CAEA,kBACE,2EACF,CAEA,eACE,+DACF,CAEA,gBACE,4DACF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,qBACF,CAEA,aAGE,yBAA0B,CAF1B,8BAA+B,CAC/B,mCAAoC,CAEpC,aACF,CAEA,YAEE,2BAA4B,CAD5B,6BAA8B,CAE9B,aACF,CAGA,kBACE,gCACF,CAEA,gBAEE,qBAAsB,CAItB,qBAAsB,CADtB,gBAEF,CAEA,iDANE,kBAAmB,CAFnB,YAAa,CAGb,sBASF,CAEA,yBAME,iCAAkC,CAHlC,0CAAuC,CACvC,sCAA0C,CAC1C,iBAAkB,CADlB,qCAA0C,CAF1C,WAAY,CADZ,UAMF,CAEA,gBACE,GAAK,sBAAyB,CAC9B,GAAO,uBAA2B,CACpC,CAEA,kBACE,2BAA4B,CAC5B,6BAA8B,CAC9B,QACF,CAEA,aAGE,0BAAsB,CAFtB,YAAa,CAEb,qBAAsB,CADtB,wDAEF,CAEA,YACE,yCAA0C,CAG1C,sCAAuC,CAFvC,8BAA+B,CAG/B,cAAe,CAFf,eAAgB,CAGhB,iCACF,CAEA,kBAEE,gCAAiC,CADjC,2BAEF,CAEA,iBAGE,YAAa,CACb,eAAgB,CAHhB,iBAAkB,CAClB,UAGF,CAEA,qBAEE,WAAY,CACZ,gBAAiB,CACjB,iCAAkC,CAHlC,UAIF,CAEA,uCACE,qBACF,CAEA,gBAIE,sBAAoC,CAGpC,8BAA+B,CAL/B,wBAAyB,CAGzB,UAAY,CAGZ,6BAA8B,CAC9B,qCAAsC,CANtC,sBAAuB,CAGvB,2CAA4C,CAL5C,iBASF,CAEA,eAUE,kBAAmB,CAHnB,0BAAoC,CACpC,gCAAiC,CAIjC,UAAY,CAHZ,YAAa,CAHb,WAAY,CAKZ,sBAAuB,CARvB,QAAS,CAUT,SAAU,CAZV,iBAAkB,CAClB,OAAQ,CAER,8BAAgC,CAUhC,iCAAkC,CATlC,UAUF,CAEA,iCACE,SACF,CAEA,YACE,yBACF,CAEA,aAOE,oBAAqB,CACrB,2BAA4B,CAL5B,yBAA0B,CAG1B,mBAAoB,CALpB,6BAA8B,CAC9B,qCAAsC,CAGtC,oCAAqC,CAIrC,eACF,CAEA,4BARE,8BAYF,CAJA,eAEE,2BAA4B,CAD5B,6BAGF,CAEA,aAEE,0BAA2B,CAD3B,6BAA8B,CAE9B,QACF,CAGA,kBACE,gCACF,CAEA,eAGE,0BAAsB,CAFtB,YAAa,CAEb,qBAAsB,CADtB,wDAEF,CAEA,cAEE,yCAA0C,CAE1C,sCAAuC,CADvC,8BAA+B,CAF/B,yBAA0B,CAI1B,iBAAkB,CAClB,iCACF,CAEA,oBAEE,gCAAiC,CADjC,2BAEF,CAEA,cAOE,kBAAmB,CAHnB,2EAA8E,CAC9E,8BAA+B,CAI/B,UAAY,CAHZ,YAAa,CAJb,WAAY,CAMZ,sBAAuB,CALvB,+BAAgC,CAFhC,UASF,CAEA,eAGE,yBAA0B,CAF1B,6BAA8B,CAC9B,uCAAwC,CAExC,8BACF,CAEA,qBAEE,2BAA4B,CAD5B,6BAA8B,CAE9B,sCAAuC,CACvC,QACF,CAGA,0BACE,cAEE,qBAAsB,CADtB,yBAA0B,CAE1B,iBACF,CAEA,oBAEE,YAAa,CADb,WAEF,CACF,CAEA,yBACE,WACE,yBACF,CAEA,YACE,8BACF,CAEA,kBACE,6BACF,CAUA,wCACE,yBACF,CACF,CAEA,yBACE,cACE,qBACF,CAEA,aAEE,sBAAuB,CADvB,UAEF,CAEA,oBAEE,YAAa,CADb,WAEF,CAEA,eACE,6BACF,CACF", "sources": ["components/pages/Home.css"], "sourcesContent": ["/* Home page styles */\n.home-page {\n  padding: var(--spacing-lg);\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n/* Hero Section */\n.hero-section {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-3xl);\n  align-items: center;\n  margin-bottom: var(--spacing-3xl);\n  min-height: 400px;\n}\n\n.hero-content {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n\n.hero-title {\n  font-size: var(--font-size-4xl);\n  font-weight: var(--font-weight-bold);\n  color: var(--text-primary);\n  line-height: var(--line-height-tight);\n  margin: 0;\n}\n\n.hero-description {\n  font-size: var(--font-size-lg);\n  color: var(--text-secondary);\n  line-height: var(--line-height-relaxed);\n  margin: 0;\n}\n\n.hero-actions {\n  display: flex;\n  gap: var(--spacing-md);\n  flex-wrap: wrap;\n}\n\n.hero-button {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-md) var(--spacing-xl);\n  border: none;\n  border-radius: var(--radius-md);\n  font-size: var(--font-size-md);\n  font-weight: var(--font-weight-medium);\n  font-family: var(--font-family-primary);\n  cursor: pointer;\n  transition: var(--transition-fast);\n  min-height: 48px;\n}\n\n.hero-button.primary {\n  background-color: var(--primary-color);\n  color: white;\n}\n\n.hero-button.primary:hover {\n  background-color: var(--primary-hover);\n  box-shadow: var(--shadow-md);\n}\n\n.hero-button.secondary {\n  background-color: transparent;\n  color: var(--text-primary);\n  border: 2px solid var(--border-primary);\n}\n\n.hero-button.secondary:hover {\n  background-color: var(--surface-hover);\n  border-color: var(--border-hover);\n}\n\n.hero-image {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.hero-video-preview {\n  width: 300px;\n  height: 200px;\n  background: linear-gradient(135deg, var(--surface-secondary), var(--surface-tertiary));\n  border-radius: var(--radius-xl);\n  border: 2px solid var(--border-primary);\n  display: flex;\n  flex-direction: column;\n  overflow: hidden;\n  box-shadow: var(--shadow-lg);\n}\n\n.video-preview-screen {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: linear-gradient(135deg, #1a1a1a, #2a2a2a);\n  color: var(--primary-color);\n}\n\n.video-preview-controls {\n  height: 40px;\n  background-color: var(--surface-primary);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-sm);\n  padding: 0 var(--spacing-md);\n}\n\n.control-button {\n  width: 8px;\n  height: 8px;\n  background-color: var(--text-tertiary);\n  border-radius: var(--radius-full);\n}\n\n/* Stats Section */\n.stats-section {\n  margin-bottom: var(--spacing-3xl);\n}\n\n.section-title {\n  font-size: var(--font-size-2xl);\n  font-weight: var(--font-weight-semibold);\n  color: var(--text-primary);\n  margin-bottom: var(--spacing-xl);\n  text-align: center;\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: var(--spacing-lg);\n}\n\n.stat-card {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  padding: var(--spacing-lg);\n  background-color: var(--surface-secondary);\n  border-radius: var(--radius-lg);\n  border: 1px solid var(--border-primary);\n  transition: var(--transition-fast);\n}\n\n.stat-card:hover {\n  box-shadow: var(--shadow-md);\n  border-color: var(--border-hover);\n}\n\n.stat-icon {\n  width: 48px;\n  height: 48px;\n  border-radius: var(--radius-md);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n}\n\n.stat-icon.videos {\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\n}\n\n.stat-icon.ads {\n  background: linear-gradient(135deg, var(--success-color), #059669);\n}\n\n.stat-icon.time {\n  background: linear-gradient(135deg, var(--info-color), #0284c7);\n}\n\n.stat-content {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n\n.stat-number {\n  font-size: var(--font-size-2xl);\n  font-weight: var(--font-weight-bold);\n  color: var(--text-primary);\n  line-height: 1;\n}\n\n.stat-label {\n  font-size: var(--font-size-sm);\n  color: var(--text-secondary);\n  line-height: 1;\n}\n\n/* Featured Videos */\n.featured-section {\n  margin-bottom: var(--spacing-3xl);\n}\n\n.videos-loading {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-height: 200px;\n  gap: var(--spacing-md);\n}\n\n.videos-loading .loading-spinner {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.videos-loading .spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid var(--border-primary);\n  border-top: 3px solid var(--primary-color);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n.videos-loading p {\n  color: var(--text-secondary);\n  font-size: var(--font-size-sm);\n  margin: 0;\n}\n\n.videos-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--spacing-lg);\n}\n\n.video-card {\n  background-color: var(--surface-secondary);\n  border-radius: var(--radius-lg);\n  overflow: hidden;\n  border: 1px solid var(--border-primary);\n  cursor: pointer;\n  transition: var(--transition-fast);\n}\n\n.video-card:hover {\n  box-shadow: var(--shadow-lg);\n  border-color: var(--border-hover);\n}\n\n.video-thumbnail {\n  position: relative;\n  width: 100%;\n  height: 160px;\n  overflow: hidden;\n}\n\n.video-thumbnail img {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: var(--transition-fast);\n}\n\n.video-card:hover .video-thumbnail img {\n  transform: scale(1.05);\n}\n\n.video-duration {\n  position: absolute;\n  bottom: var(--spacing-sm);\n  left: var(--spacing-sm);\n  background-color: rgba(0, 0, 0, 0.8);\n  color: white;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: var(--font-size-xs);\n  font-weight: var(--font-weight-medium);\n}\n\n.video-overlay {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  width: 64px;\n  height: 64px;\n  background-color: rgba(0, 0, 0, 0.7);\n  border-radius: var(--radius-full);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n  opacity: 0;\n  transition: var(--transition-fast);\n}\n\n.video-card:hover .video-overlay {\n  opacity: 1;\n}\n\n.video-info {\n  padding: var(--spacing-md);\n}\n\n.video-title {\n  font-size: var(--font-size-md);\n  font-weight: var(--font-weight-medium);\n  color: var(--text-primary);\n  margin: 0 0 var(--spacing-xs) 0;\n  line-height: var(--line-height-tight);\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.video-channel {\n  font-size: var(--font-size-sm);\n  color: var(--text-secondary);\n  margin: 0 0 var(--spacing-xs) 0;\n}\n\n.video-views {\n  font-size: var(--font-size-xs);\n  color: var(--text-tertiary);\n  margin: 0;\n}\n\n/* Features Section */\n.features-section {\n  margin-bottom: var(--spacing-3xl);\n}\n\n.features-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: var(--spacing-lg);\n}\n\n.feature-card {\n  padding: var(--spacing-xl);\n  background-color: var(--surface-secondary);\n  border-radius: var(--radius-lg);\n  border: 1px solid var(--border-primary);\n  text-align: center;\n  transition: var(--transition-fast);\n}\n\n.feature-card:hover {\n  box-shadow: var(--shadow-lg);\n  border-color: var(--border-hover);\n}\n\n.feature-icon {\n  width: 64px;\n  height: 64px;\n  margin: 0 auto var(--spacing-md);\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));\n  border-radius: var(--radius-lg);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: white;\n}\n\n.feature-title {\n  font-size: var(--font-size-lg);\n  font-weight: var(--font-weight-semibold);\n  color: var(--text-primary);\n  margin: 0 0 var(--spacing-sm) 0;\n}\n\n.feature-description {\n  font-size: var(--font-size-sm);\n  color: var(--text-secondary);\n  line-height: var(--line-height-relaxed);\n  margin: 0;\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .hero-section {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-xl);\n    text-align: center;\n  }\n  \n  .hero-video-preview {\n    width: 250px;\n    height: 160px;\n  }\n}\n\n@media (max-width: 768px) {\n  .home-page {\n    padding: var(--spacing-md);\n  }\n  \n  .hero-title {\n    font-size: var(--font-size-3xl);\n  }\n  \n  .hero-description {\n    font-size: var(--font-size-md);\n  }\n  \n  .stats-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .videos-grid {\n    grid-template-columns: 1fr;\n  }\n  \n  .features-grid {\n    grid-template-columns: 1fr;\n  }\n}\n\n@media (max-width: 640px) {\n  .hero-actions {\n    flex-direction: column;\n  }\n  \n  .hero-button {\n    width: 100%;\n    justify-content: center;\n  }\n  \n  .hero-video-preview {\n    width: 200px;\n    height: 130px;\n  }\n  \n  .section-title {\n    font-size: var(--font-size-xl);\n  }\n}\n"], "names": [], "sourceRoot": ""}