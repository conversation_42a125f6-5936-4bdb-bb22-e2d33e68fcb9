/* About page styles */
.about-page {
  padding: var(--spacing-lg);
  max-width: 800px;
  margin: 0 auto;
}

.about-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.about-header {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--surface-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.app-logo {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.about-header h1 {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.version {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  margin: 0;
}

.about-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.about-section {
  background-color: var(--surface-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-primary);
}

.about-section h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-primary);
  padding-bottom: var(--spacing-sm);
}

.about-section p {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.features-list li {
  position: relative;
  padding-right: var(--spacing-lg);
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.features-list li::before {
  content: '✓';
  position: absolute;
  right: 0;
  color: var(--success-color);
  font-weight: var(--font-weight-bold);
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.tech-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  background-color: var(--surface-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.tech-item strong {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.tech-item span {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

.contact-info {
  background-color: var(--surface-primary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.contact-info p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xs) 0;
}

.contact-info p:last-child {
  margin-bottom: 0;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* Responsive design */
@media (max-width: 768px) {
  .about-page {
    padding: var(--spacing-md);
  }
  
  .about-header {
    padding: var(--spacing-lg);
  }
  
  .about-header h1 {
    font-size: var(--font-size-2xl);
  }
  
  .about-section {
    padding: var(--spacing-md);
  }
  
  .tech-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .about-page {
    padding: var(--spacing-sm);
  }
  
  .about-header {
    padding: var(--spacing-md);
  }
  
  .app-logo {
    width: 60px;
    height: 60px;
  }
  
  .about-header h1 {
    font-size: var(--font-size-xl);
  }
  
  .about-section h2 {
    font-size: var(--font-size-lg);
  }
}
