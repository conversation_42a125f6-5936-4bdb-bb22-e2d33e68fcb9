import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Main app store with state management
const useAppStore = create(
  persist(
    immer((set, get) => ({
      // App state
      isLoading: false,
      error: null,
      theme: 'dark',
      language: 'ar',
      
      // User preferences
      settings: {
        quality: 'auto',
        autoplay: false,
        volume: 100,
        muted: false,
        hideControls: false,
        adBlock: true,
        notifications: true,
        saveHistory: true,
        darkTheme: true,
        rtlLayout: true,
        fontSize: 'medium',
        animationsEnabled: true
      },
      
      // Current video state
      currentVideo: {
        id: null,
        title: '',
        channel: '',
        duration: 0,
        currentTime: 0,
        isPlaying: false,
        isPaused: false,
        isBuffering: false,
        quality: 'auto',
        volume: 100,
        muted: false,
        fullscreen: false
      },
      
      // Search state
      search: {
        query: '',
        results: [],
        isSearching: false,
        history: [],
        suggestions: []
      },
      
      // Sidebar state
      sidebar: {
        isOpen: true,
        activeTab: 'search',
        width: 300
      },
      
      // Statistics
      stats: {
        videosWatched: 0,
        adsBlocked: 0,
        timeSpent: 0,
        sessionsCount: 0,
        lastSession: null
      },
      
      // Developer monetization
      developer: {
        revenue: 0,
        impressions: 0,
        clicks: 0,
        conversionRate: 0
      },
      
      // Actions
      setLoading: (loading) => set((state) => {
        state.isLoading = loading;
      }),
      
      setError: (error) => set((state) => {
        state.error = error;
      }),
      
      clearError: () => set((state) => {
        state.error = null;
      }),
      
      setTheme: (theme) => set((state) => {
        state.theme = theme;
        state.settings.darkTheme = theme === 'dark';
      }),
      
      setLanguage: (language) => set((state) => {
        state.language = language;
      }),
      
      updateSettings: (newSettings) => set((state) => {
        state.settings = { ...state.settings, ...newSettings };
      }),
      
      resetSettings: () => set((state) => {
        state.settings = {
          quality: 'auto',
          autoplay: false,
          volume: 100,
          muted: false,
          hideControls: false,
          adBlock: true,
          notifications: true,
          saveHistory: true,
          darkTheme: true,
          rtlLayout: true,
          fontSize: 'medium',
          animationsEnabled: true
        };
      }),
      
      // Video actions
      setCurrentVideo: (video) => set((state) => {
        state.currentVideo = { ...state.currentVideo, ...video };
      }),
      
      updateVideoState: (updates) => set((state) => {
        state.currentVideo = { ...state.currentVideo, ...updates };
      }),
      
      clearCurrentVideo: () => set((state) => {
        state.currentVideo = {
          id: null,
          title: '',
          channel: '',
          duration: 0,
          currentTime: 0,
          isPlaying: false,
          isPaused: false,
          isBuffering: false,
          quality: 'auto',
          volume: 100,
          muted: false,
          fullscreen: false
        };
      }),
      
      // Search actions
      setSearchQuery: (query) => set((state) => {
        state.search.query = query;
      }),
      
      setSearchResults: (results) => set((state) => {
        state.search.results = results;
      }),
      
      setSearching: (isSearching) => set((state) => {
        state.search.isSearching = isSearching;
      }),
      
      addToSearchHistory: (query) => set((state) => {
        if (query && !state.search.history.includes(query)) {
          state.search.history.unshift(query);
          if (state.search.history.length > 10) {
            state.search.history = state.search.history.slice(0, 10);
          }
        }
      }),
      
      clearSearchHistory: () => set((state) => {
        state.search.history = [];
      }),
      
      // Sidebar actions
      toggleSidebar: () => set((state) => {
        state.sidebar.isOpen = !state.sidebar.isOpen;
      }),
      
      setSidebarTab: (tab) => set((state) => {
        state.sidebar.activeTab = tab;
      }),
      
      setSidebarWidth: (width) => set((state) => {
        state.sidebar.width = width;
      }),
      
      // Statistics actions
      incrementVideosWatched: () => set((state) => {
        state.stats.videosWatched += 1;
      }),
      
      incrementAdsBlocked: (count = 1) => set((state) => {
        state.stats.adsBlocked += count;
      }),
      
      addTimeSpent: (seconds) => set((state) => {
        state.stats.timeSpent += seconds;
      }),
      
      incrementSessions: () => set((state) => {
        state.stats.sessionsCount += 1;
        state.stats.lastSession = new Date().toISOString();
      }),
      
      resetStats: () => set((state) => {
        state.stats = {
          videosWatched: 0,
          adsBlocked: 0,
          timeSpent: 0,
          sessionsCount: 0,
          lastSession: null
        };
      }),
      
      // Developer monetization actions
      addRevenue: (amount) => set((state) => {
        state.developer.revenue += amount;
      }),
      
      incrementImpressions: () => set((state) => {
        state.developer.impressions += 1;
      }),
      
      incrementClicks: () => set((state) => {
        state.developer.clicks += 1;
        state.developer.conversionRate = state.developer.clicks / state.developer.impressions;
      }),
      
      // Utility actions
      exportData: () => {
        const state = get();
        return {
          settings: state.settings,
          stats: state.stats,
          searchHistory: state.search.history,
          exportDate: new Date().toISOString()
        };
      },
      
      importData: (data) => set((state) => {
        if (data.settings) {
          state.settings = { ...state.settings, ...data.settings };
        }
        if (data.stats) {
          state.stats = { ...state.stats, ...data.stats };
        }
        if (data.searchHistory) {
          state.search.history = data.searchHistory;
        }
      }),
      
      resetApp: () => set((state) => {
        // Reset to initial state
        const initialState = useAppStore.getState();
        Object.keys(initialState).forEach(key => {
          if (typeof initialState[key] !== 'function') {
            state[key] = initialState[key];
          }
        });
      })
    })),
    {
      name: 'youtube-player-pro-storage',
      storage: createJSONStorage(() => {
        // Use electron storage if available, otherwise localStorage
        if (window.electronAPI?.storage) {
          return {
            getItem: (key) => window.electronAPI.storage.get(key),
            setItem: (key, value) => window.electronAPI.storage.set(key, value),
            removeItem: (key) => window.electronAPI.storage.remove(key)
          };
        }
        return localStorage;
      }),
      partialize: (state) => ({
        settings: state.settings,
        stats: state.stats,
        search: {
          history: state.search.history
        },
        sidebar: state.sidebar,
        theme: state.theme,
        language: state.language
      })
    }
  )
);

export default useAppStore;
