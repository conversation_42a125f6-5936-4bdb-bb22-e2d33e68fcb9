import React, { useState } from 'react';

const Header = ({ onVideoLoad, onSettingsClick, onDeveloperClick, stats }) => {
  const [searchInput, setSearchInput] = useState('');

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchInput.trim()) {
      onVideoLoad(searchInput.trim());
      setSearchInput('');
    }
  };

  const handleMinimize = () => {
    if (window.require) {
      const { ipcRenderer } = window.require('electron');
      ipcRenderer.invoke('minimize-window');
    }
  };

  const handleMaximize = () => {
    if (window.require) {
      const { ipc<PERSON>ender<PERSON> } = window.require('electron');
      ipcRenderer.invoke('maximize-window');
    }
  };

  const handleClose = () => {
    if (window.require) {
      const { ipcRenderer } = window.require('electron');
      ipcRenderer.invoke('close-window');
    }
  };

  return (
    <header className="header">
      <div className="header-right">
        <div className="logo">
          <span>🌐</span>
          <span>مشغل يوتيوب المتقدم</span>
        </div>
        
        <form onSubmit={handleSearch} className="search-container">
          <input
            type="text"
            className="search-input"
            placeholder="ابحث في يوتيوب أو الصق رابط الفيديو..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
          />
          <button type="submit" className="search-btn">
            🔍
          </button>
        </form>
      </div>

      <div className="header-left">
        {/* إحصائيات سريعة */}
        {stats && (
          <div className="header-stats">
            <span className="stat-item" title="إعلانات محجوبة">
              🛡️ {stats.adsBlocked}
            </span>
            <span className="stat-item" title="فيديوهات مشاهدة">
              🎥 {stats.videosWatched}
            </span>
          </div>
        )}

        <button
          className="header-btn"
          onClick={onDeveloperClick}
          title="معلومات المطور"
        >
          👨‍💻
        </button>

        <button
          className="header-btn"
          onClick={onSettingsClick}
          title="الإعدادات"
        >
          ⚙️
        </button>
        
        <button 
          className="header-btn"
          onClick={handleMinimize}
          title="تصغير"
        >
          ➖
        </button>
        
        <button 
          className="header-btn"
          onClick={handleMaximize}
          title="تكبير/استعادة"
        >
          ⬜
        </button>
        
        <button 
          className="header-btn"
          onClick={handleClose}
          title="إغلاق"
        >
          ❌
        </button>
      </div>
    </header>
  );
};

export default Header;

