"use strict";(self.webpackChunkyoutube_player_pro=self.webpackChunkyoutube_player_pro||[]).push([[906],{6906:(e,t,i)=>{i.r(t),i.d(t,{default:()=>l});var a=i(5043),r=i(1228),n=i(663),s=i(7494),o=i(579);const l=e=>{let{routeParams:t={}}=e;const[i,l]=(0,a.useState)(!1),[d,c]=(0,a.useState)(null),u=(0,a.useRef)(null),h=(0,a.useRef)(null),{currentVideo:p,setCurrentVideo:g,updateVideoState:m,incrementVideosWatched:v,settings:w}=(0,n.A)(),y=(null===t||void 0===t?void 0:t.videoId)||p.id,f=(0,a.useCallback)(async()=>{if(y)try{const e=await s.A.getVideoDetails(y);g({id:y,title:e.title,channel:e.channel,duration:e.duration,thumbnail:e.thumbnail,description:e.description,views:e.views,publishedAt:e.publishedAt})}catch(e){console.error("Failed to load video details:",e)}},[y,g]);(0,a.useEffect)(()=>{y&&(b(),f())},[y,f]);const b=()=>{if(window.YT)T();else{const e=document.createElement("script");e.src="https://www.youtube.com/iframe_api",e.async=!0,document.body.appendChild(e),window.onYouTubeIframeAPIReady=()=>{T()}}},T=()=>{var e;if(u.current&&null!==(e=window.YT)&&void 0!==e&&e.Player)try{h.current=new window.YT.Player(u.current,{height:"100%",width:"100%",videoId:y,playerVars:{autoplay:w.autoplay?1:0,controls:w.hideControls?0:1,disablekb:w.hideControls?1:0,fs:1,iv_load_policy:3,modestbranding:1,playsinline:1,rel:0,showinfo:0,cc_load_policy:0,hl:"ar",cc_lang_pref:"ar",origin:window.location.origin},events:{onReady:S,onStateChange:x,onError:k}})}catch(t){console.error("Error initializing YouTube player:",t),c("\u0641\u0634\u0644 \u0641\u064a \u062a\u062d\u0645\u064a\u0644 \u0645\u0634\u063a\u0644 \u0627\u0644\u0641\u064a\u062f\u064a\u0648")}},S=e=>{l(!0),c(null),void 0!==w.volume&&e.target.setVolume(w.volume);const t=e.target.getVideoData();g({id:y,title:t.title||"\u0641\u064a\u062f\u064a\u0648 \u064a\u0648\u062a\u064a\u0648\u0628",channel:t.author||"\u0642\u0646\u0627\u0629 \u064a\u0648\u062a\u064a\u0648\u0628",duration:e.target.getDuration()||0,currentTime:0,isPlaying:!1,isPaused:!0,isBuffering:!1,quality:e.target.getPlaybackQuality()||"auto",volume:e.target.getVolume()||100,muted:e.target.isMuted()||!1,fullscreen:!1}),console.log("YouTube player ready for video:",y)},x=e=>{const t=e.data,i=e.target;switch(t){case window.YT.PlayerState.PLAYING:m({isPlaying:!0,isPaused:!1,isBuffering:!1});break;case window.YT.PlayerState.PAUSED:m({isPlaying:!1,isPaused:!0,isBuffering:!1});break;case window.YT.PlayerState.BUFFERING:m({isBuffering:!0});break;case window.YT.PlayerState.ENDED:m({isPlaying:!1,isPaused:!0,isBuffering:!1,currentTime:i.getDuration()}),v();break;case window.YT.PlayerState.CUED:m({isPlaying:!1,isPaused:!0,isBuffering:!1,currentTime:0})}i.getCurrentTime&&m({currentTime:i.getCurrentTime()})},k=e=>{const t=e.data;let i="\u062d\u062f\u062b \u062e\u0637\u0623 \u0641\u064a \u062a\u0634\u063a\u064a\u0644 \u0627\u0644\u0641\u064a\u062f\u064a\u0648";switch(t){case 2:i="\u0645\u0639\u0631\u0641 \u0627\u0644\u0641\u064a\u062f\u064a\u0648 \u063a\u064a\u0631 \u0635\u062d\u064a\u062d";break;case 5:i="\u062e\u0637\u0623 \u0641\u064a \u0645\u0634\u063a\u0644 HTML5";break;case 100:i="\u0627\u0644\u0641\u064a\u062f\u064a\u0648 \u063a\u064a\u0631 \u0645\u0648\u062c\u0648\u062f \u0623\u0648 \u0645\u062d\u0630\u0648\u0641";break;case 101:case 150:i="\u0635\u0627\u062d\u0628 \u0627\u0644\u0641\u064a\u062f\u064a\u0648 \u0644\u0627 \u064a\u0633\u0645\u062d \u0628\u062a\u0634\u063a\u064a\u0644\u0647 \u0641\u064a \u0647\u0630\u0627 \u0627\u0644\u062a\u0637\u0628\u064a\u0642"}c(i),console.error("YouTube player error:",t,i)};return y?(0,o.jsx)(r.P.div,{className:"player-page",initial:{opacity:0},animate:{opacity:1},transition:{duration:.3},children:(0,o.jsxs)("div",{className:"player-container",children:[(0,o.jsx)("div",{className:"player-wrapper",children:d?(0,o.jsxs)("div",{className:"player-error",children:[(0,o.jsx)("div",{className:"error-icon",children:(0,o.jsx)("svg",{width:"48",height:"48",viewBox:"0 0 24 24",fill:"currentColor",children:(0,o.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})}),(0,o.jsx)("h3",{children:"\u062e\u0637\u0623 \u0641\u064a \u062a\u0634\u063a\u064a\u0644 \u0627\u0644\u0641\u064a\u062f\u064a\u0648"}),(0,o.jsx)("p",{children:d}),(0,o.jsxs)("button",{className:"retry-button",onClick:()=>{c(null),l(!1),h.current&&h.current.destroy(),setTimeout(()=>{T()},1e3)},children:[(0,o.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,o.jsx)("path",{d:"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"})}),"\u0625\u0639\u0627\u062f\u0629 \u0627\u0644\u0645\u062d\u0627\u0648\u0644\u0629"]})]}):(0,o.jsxs)(o.Fragment,{children:[!i&&(0,o.jsxs)("div",{className:"player-loading",children:[(0,o.jsx)("div",{className:"loading-spinner",children:(0,o.jsx)("div",{className:"spinner"})}),(0,o.jsx)("p",{children:"\u062c\u0627\u0631\u064a \u062a\u062d\u0645\u064a\u0644 \u0627\u0644\u0641\u064a\u062f\u064a\u0648..."})]}),(0,o.jsx)("div",{ref:u,className:"youtube-player "+(i?"":"hidden")})]})}),p.title&&(0,o.jsxs)(r.P.div,{className:"video-info",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},children:[(0,o.jsx)("h1",{className:"video-title",children:p.title}),(0,o.jsxs)("div",{className:"video-meta",children:[(0,o.jsx)("span",{className:"video-channel",children:p.channel}),p.duration>0&&(0,o.jsxs)("span",{className:"video-duration",children:["\u0627\u0644\u0645\u062f\u0629: ",Math.floor(p.duration/60),":",(p.duration%60).toString().padStart(2,"0")]})]})]}),w.adBlock&&(0,o.jsxs)(r.P.div,{className:"ad-blocker-status",initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.5},children:[(0,o.jsx)("div",{className:"status-icon",children:(0,o.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor",children:(0,o.jsx)("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})})}),(0,o.jsx)("span",{children:"\u0645\u0627\u0646\u0639 \u0627\u0644\u0625\u0639\u0644\u0627\u0646\u0627\u062a \u0645\u0641\u0639\u0644 - \u062a\u062c\u0631\u0628\u0629 \u0645\u0634\u0627\u0647\u062f\u0629 \u0628\u062f\u0648\u0646 \u0625\u0639\u0644\u0627\u0646\u0627\u062a"})]})]})}):(0,o.jsx)("div",{className:"player-page",children:(0,o.jsxs)("div",{className:"player-empty",children:[(0,o.jsx)("div",{className:"empty-icon",children:(0,o.jsx)("svg",{width:"64",height:"64",viewBox:"0 0 24 24",fill:"currentColor",children:(0,o.jsx)("path",{d:"M8 5v14l11-7z"})})}),(0,o.jsx)("h2",{children:"\u0644\u0627 \u064a\u0648\u062c\u062f \u0641\u064a\u062f\u064a\u0648 \u0644\u0644\u062a\u0634\u063a\u064a\u0644"}),(0,o.jsx)("p",{children:"\u064a\u0631\u062c\u0649 \u0627\u0644\u0628\u062d\u062b \u0639\u0646 \u0641\u064a\u062f\u064a\u0648 \u0623\u0648 \u0627\u062e\u062a\u064a\u0627\u0631 \u0641\u064a\u062f\u064a\u0648 \u0645\u0646 \u0627\u0644\u0642\u0627\u0626\u0645\u0629"})]})})}},7494:(e,t,i)=>{i.d(t,{A:()=>a});const a=new class{constructor(){this.apiKey={NODE_ENV:"production",PUBLIC_URL:".",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_YOUTUBE_API_KEY,this.baseURL="https://www.googleapis.com/youtube/v3",this.useAlternative=!this.apiKey||"DEMO_KEY"===this.apiKey,this.realTrendingVideos=["dQw4w9WgXcQ","kJQP7kiw5Fk","fJ9rUzIMcZQ","JGwWNGJdvx8","RgKAFK5djSk","CevxZvSJLk8","hTWKbfoikeg","9bZkp7q19f0","YQHsXMglC9A","lp-EO5I60KA"]}async searchVideos(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:25,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";try{if(this.useAlternative)return await this.searchWithAlternativeMethod(e,t,i);const a=new URLSearchParams({part:"snippet",q:e,type:"video",maxResults:t.toString(),key:this.apiKey,order:"relevance",safeSearch:"moderate",regionCode:"SA",relevanceLanguage:"ar"});i&&a.append("pageToken",i);const r=await fetch(`${this.baseURL}/search?${a}`);if(!r.ok)throw new Error(`YouTube API error: ${r.status}`);const n=await r.json();return this.transformSearchResults(n)}catch(a){return console.error("YouTube search error:",a),this.searchWithAlternativeMethod(e,t,i)}}async getVideoDetails(e){try{if(this.useAlternative)return await this.getVideoDetailsAlternative(e);const t=new URLSearchParams({part:"snippet,statistics,contentDetails",id:e,key:this.apiKey}),i=await fetch(`${this.baseURL}/videos?${t}`);if(!i.ok)throw new Error(`YouTube API error: ${i.status}`);const a=await i.json();if(a.items&&a.items.length>0)return this.transformVideoDetails(a.items[0]);throw new Error("Video not found")}catch(t){return console.error("YouTube video details error:",t),this.getVideoDetailsAlternative(e)}}async searchWithAlternativeMethod(e,t,i){try{const a=await this.generateRealisticSearchResults(e,t);return{videos:a,nextPageToken:i?null:"next_page_token",totalResults:a.length}}catch(a){return console.error("Alternative search error:",a),this.getMockSearchResults(e)}}async generateRealisticSearchResults(e,t){const i=[],a=new Set,r=[...this.realTrendingVideos];for(let n=0;n<Math.min(t,10);n++){let t;do{t=r[Math.floor(Math.random()*r.length)]}while(a.has(t));a.add(t);const s=await this.getVideoDetailsViaOEmbed(t,e,n);s&&i.push(s)}return i}async getVideoDetailsViaOEmbed(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";try{const i=`https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${e}&format=json`,a=await fetch(i);if(!a.ok)throw new Error(`oEmbed API error: ${a.status}`);const r=await a.json();return{id:e,title:t?`${r.title} - ${t}`:r.title,channel:r.author_name,description:`\u0641\u064a\u062f\u064a\u0648 \u0631\u0627\u0626\u0639 \u0645\u0646 \u0642\u0646\u0627\u0629 ${r.author_name}. ${t?"\u0645\u062d\u062a\u0648\u0649 \u0645\u062a\u0639\u0644\u0642 \u0628\u0640 "+t:"\u0645\u062d\u062a\u0648\u0649 \u0645\u0645\u064a\u0632 \u0648\u0645\u0641\u064a\u062f"}`,thumbnail:r.thumbnail_url,duration:this.generateRandomDuration(),views:this.generateRandomViews(),publishedAt:this.generateRandomDate(),channelId:`UC${Math.random().toString(36).substring(2,11)}`}}catch(i){return console.error("oEmbed error:",i),null}}async getVideoDetailsAlternative(e){try{const t=await this.getVideoDetailsViaOEmbed(e);if(t)return t}catch(t){console.error("Alternative video details error:",t)}return this.getMockVideoDetails(e)}transformSearchResults(e){var t;if(!e.items)return{videos:[],nextPageToken:null,totalResults:0};return{videos:e.items.map(e=>{var t,i,a,r;return{id:e.id.videoId||e.id,title:e.snippet.title,channel:e.snippet.channelTitle,description:e.snippet.description,thumbnail:(null===(t=e.snippet.thumbnails.medium)||void 0===t?void 0:t.url)||(null===(i=e.snippet.thumbnails.default)||void 0===i?void 0:i.url),publishedAt:e.snippet.publishedAt,duration:this.parseDuration(null===(a=e.contentDetails)||void 0===a?void 0:a.duration),views:this.formatViews(null===(r=e.statistics)||void 0===r?void 0:r.viewCount),channelId:e.snippet.channelId}}),nextPageToken:e.nextPageToken||null,totalResults:(null===(t=e.pageInfo)||void 0===t?void 0:t.totalResults)||0}}transformVideoDetails(e){var t,i;return{id:e.id,title:e.snippet.title,channel:e.snippet.channelTitle,description:e.snippet.description,thumbnail:(null===(t=e.snippet.thumbnails.maxres)||void 0===t?void 0:t.url)||(null===(i=e.snippet.thumbnails.high)||void 0===i?void 0:i.url),publishedAt:e.snippet.publishedAt,duration:this.parseDuration(e.contentDetails.duration),views:this.formatViews(e.statistics.viewCount),likes:this.formatViews(e.statistics.likeCount),channelId:e.snippet.channelId,tags:e.snippet.tags||[]}}parseDuration(e){if(!e)return 0;const t=e.match(/PT(\d+H)?(\d+M)?(\d+S)?/);if(!t)return 0;return 3600*(parseInt(t[1])||0)+60*(parseInt(t[2])||0)+(parseInt(t[3])||0)}formatViews(e){if(!e)return"0";const t=parseInt(e);return t>=1e6?`${(t/1e6).toFixed(1)}M`:t>=1e3?`${(t/1e3).toFixed(1)}K`:t.toString()}formatDuration(e){if(!e)return"0:00";const t=Math.floor(e/3600),i=Math.floor(e%3600/60),a=e%60;return t>0?`${t}:${i.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`:`${i}:${a.toString().padStart(2,"0")}`}generateRandomDuration(){return`${Math.floor(15*Math.random())+1}:${Math.floor(60*Math.random()).toString().padStart(2,"0")}`}generateRandomViews(){const e=Math.floor(1e7*Math.random())+1e3;return this.formatViews(e.toString())}generateRandomDate(){const e=new Date,t=Math.floor(365*Math.random());return new Date(e.getTime()-24*t*60*60*1e3).toISOString()}getMockSearchResults(e){const t=[{id:"dQw4w9WgXcQ",title:`${e} - \u0646\u062a\u064a\u062c\u0629 \u0627\u0644\u0628\u062d\u062b \u0627\u0644\u0623\u0648\u0644\u0649`,channel:"\u0642\u0646\u0627\u0629 \u062a\u062c\u0631\u064a\u0628\u064a\u0629",description:"\u0648\u0635\u0641 \u0645\u0641\u0635\u0644 \u0644\u0644\u0641\u064a\u062f\u064a\u0648 \u0627\u0644\u0623\u0648\u0644 \u0641\u064a \u0646\u062a\u0627\u0626\u062c \u0627\u0644\u0628\u062d\u062b...",thumbnail:"https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg",duration:"3:32",views:"1.2M",publishedAt:"2023-01-15T10:00:00Z",channelId:"UC123456789"},{id:"kJQP7kiw5Fk",title:`\u0634\u0631\u062d ${e} \u0628\u0627\u0644\u062a\u0641\u0635\u064a\u0644`,channel:"\u0642\u0646\u0627\u0629 \u0627\u0644\u062a\u0639\u0644\u064a\u0645",description:"\u0634\u0631\u062d \u0634\u0627\u0645\u0644 \u0648\u0645\u0641\u0635\u0644 \u062d\u0648\u0644 \u0627\u0644\u0645\u0648\u0636\u0648\u0639 \u0627\u0644\u0645\u0637\u0644\u0648\u0628...",thumbnail:"https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg",duration:"4:42",views:"850K",publishedAt:"2023-02-20T15:30:00Z",channelId:"UC987654321"},{id:"fJ9rUzIMcZQ",title:`\u0623\u0641\u0636\u0644 ${e} \u0644\u0647\u0630\u0627 \u0627\u0644\u0639\u0627\u0645`,channel:"\u0642\u0646\u0627\u0629 \u0627\u0644\u0645\u0631\u0627\u062c\u0639\u0627\u062a",description:"\u0645\u0631\u0627\u062c\u0639\u0629 \u0634\u0627\u0645\u0644\u0629 \u0644\u0623\u0641\u0636\u0644 \u0627\u0644\u062e\u064a\u0627\u0631\u0627\u062a \u0627\u0644\u0645\u062a\u0627\u062d\u0629...",thumbnail:"https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg",duration:"5:55",views:"2.1M",publishedAt:"2023-03-10T12:15:00Z",channelId:"UC456789123"}];return{videos:t,nextPageToken:null,totalResults:t.length}}getMockVideoDetails(e){return{id:e,title:"\u0641\u064a\u062f\u064a\u0648 \u064a\u0648\u062a\u064a\u0648\u0628 \u062a\u062c\u0631\u064a\u0628\u064a",channel:"\u0642\u0646\u0627\u0629 \u062a\u062c\u0631\u064a\u0628\u064a\u0629",description:"\u0648\u0635\u0641 \u062a\u062c\u0631\u064a\u0628\u064a \u0644\u0644\u0641\u064a\u062f\u064a\u0648...",thumbnail:`https://img.youtube.com/vi/${e}/maxresdefault.jpg`,duration:"3:45",views:"1.5M",likes:"25K",publishedAt:"2023-01-01T00:00:00Z",channelId:"UC123456789",tags:["\u062a\u062c\u0631\u064a\u0628\u064a","\u0641\u064a\u062f\u064a\u0648","\u064a\u0648\u062a\u064a\u0648\u0628"]}}async getTrendingVideos(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"SA",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:25;try{if(this.useAlternative)return await this.getTrendingAlternative(t);const i=new URLSearchParams({part:"snippet,statistics,contentDetails",chart:"mostPopular",regionCode:e,maxResults:t.toString(),key:this.apiKey,videoCategoryId:"0"}),a=await fetch(`${this.baseURL}/videos?${i}`);if(!a.ok)throw new Error(`YouTube API error: ${a.status}`);const r=await a.json();return this.transformSearchResults(r)}catch(i){return console.error("YouTube trending error:",i),this.getTrendingAlternative(t)}}async getTrendingAlternative(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;try{const t=[],i=new Set;for(let a=0;a<Math.min(e,this.realTrendingVideos.length);a++){const e=this.realTrendingVideos[a];if(!i.has(e)){i.add(e);const r=await this.getVideoDetailsViaOEmbed(e,"\u0627\u0644\u0623\u0643\u062b\u0631 \u0645\u0634\u0627\u0647\u062f\u0629",a);r&&t.push(r)}}return{videos:t,nextPageToken:null,totalResults:t.length}}catch(t){return console.error("Alternative trending error:",t),this.getMockSearchResults("\u0627\u0644\u0623\u0643\u062b\u0631 \u0645\u0634\u0627\u0647\u062f\u0629")}}}}}]);
//# sourceMappingURL=906.51419996.chunk.js.map