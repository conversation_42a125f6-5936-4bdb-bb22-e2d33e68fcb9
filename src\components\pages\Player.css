/* Player page styles */
.player-page {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
  height: 100%;
}

.player-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  height: 100%;
}

.player-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  background-color: var(--surface-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--border-primary);
}

.youtube-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.youtube-player.hidden {
  opacity: 0;
}

.player-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-secondary);
  color: var(--text-secondary);
  gap: var(--spacing-md);
}

.loading-spinner .spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.player-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-secondary);
  color: var(--text-primary);
  gap: var(--spacing-md);
  text-align: center;
  padding: var(--spacing-lg);
}

.error-icon {
  color: var(--error-color);
}

.retry-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-fast);
}

.retry-button:hover {
  background-color: var(--primary-hover);
}

.player-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
  color: var(--text-secondary);
  gap: var(--spacing-md);
}

.empty-icon {
  color: var(--text-tertiary);
}

.video-info {
  background-color: var(--surface-secondary);
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.video-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  line-height: var(--line-height-tight);
}

.video-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.video-channel {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.video-duration {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

.ad-blocker-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background-color: var(--success-bg);
  color: var(--success-color);
  border-radius: var(--radius-md);
  border: 1px solid var(--success-color);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .player-page {
    padding: var(--spacing-md);
  }
  
  .video-info {
    padding: var(--spacing-md);
  }
  
  .video-title {
    font-size: var(--font-size-lg);
  }
  
  .video-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}

@media (max-width: 640px) {
  .player-page {
    padding: var(--spacing-sm);
  }
  
  .ad-blocker-status {
    padding: var(--spacing-sm);
    font-size: var(--font-size-xs);
  }
}
