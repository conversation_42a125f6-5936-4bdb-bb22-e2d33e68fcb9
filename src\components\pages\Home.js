import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import useAppStore from '../../stores/appStore';
import useRouter from '../../services/router';

import './Home.css';

const Home = () => {
  const [featuredVideos, setFeaturedVideos] = useState([]);
  const [isLoadingVideos, setIsLoadingVideos] = useState(true);

  const { stats, currentVideo } = useAppStore();
  const { navigate } = useRouter();

  // Load trending videos on component mount
  useEffect(() => {
    const loadTrendingVideos = () => {
      try {
        setIsLoadingVideos(true);

        // Real YouTube videos with verified IDs - these are actual videos that exist
        const realVideos = [
          {
            id: 'dQw4w9WgXcQ',
            title: '<PERSON> - Never Gonna Give You Up (Official Video)',
            channel: '<PERSON>',
            duration: '3:33',
            views: '1.4B',
            thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
          },
          {
            id: 'kJQP7kiw5Fk',
            title: '<PERSON> - <PERSON>pacito ft. <PERSON>',
            channel: 'Luis Fonsi',
            duration: '4:42',
            views: '8.1B',
            thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/maxresdefault.jpg'
          },
          {
            id: 'fJ9rUzIMcZQ',
            title: 'Wiz Khalifa - See You Again ft. Charlie Puth',
            channel: 'Wiz Khalifa',
            duration: '3:57',
            views: '5.9B',
            thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/maxresdefault.jpg'
          },
          {
            id: 'JGwWNGJdvx8',
            title: 'Ed Sheeran - Shape of You',
            channel: 'Ed Sheeran',
            duration: '3:53',
            views: '5.8B',
            thumbnail: 'https://img.youtube.com/vi/JGwWNGJdvx8/maxresdefault.jpg'
          },
          {
            id: 'RgKAFK5djSk',
            title: 'PSY - GANGNAM STYLE',
            channel: 'officialpsy',
            duration: '4:12',
            views: '4.7B',
            thumbnail: 'https://img.youtube.com/vi/RgKAFK5djSk/maxresdefault.jpg'
          },
          {
            id: 'CevxZvSJLk8',
            title: 'Katy Perry - Roar (Official)',
            channel: 'Katy Perry',
            duration: '3:43',
            views: '3.7B',
            thumbnail: 'https://img.youtube.com/vi/CevxZvSJLk8/maxresdefault.jpg'
          }
        ];

        // Set the real videos directly
        setFeaturedVideos(realVideos);

      } catch (error) {
        console.error('Failed to load trending videos:', error);
      } finally {
        setIsLoadingVideos(false);
      }
    };

    loadTrendingVideos();
  }, []);

  const handleVideoClick = (videoId) => {
    navigate('player', { videoId });
  };

  const handleSearchClick = () => {
    navigate('search');
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <motion.div 
      className="home-page"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Hero Section */}
      <motion.section className="hero-section" variants={itemVariants}>
        <div className="hero-content">
          <motion.h1 
            className="hero-title"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            مرحباً بك في مشغل يوتيوب المتقدم
          </motion.h1>
          <motion.p 
            className="hero-description"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            استمتع بمشاهدة فيديوهات يوتيوب بدون إعلانات مع واجهة عربية متقدمة وميزات احترافية
          </motion.p>
          <motion.div 
            className="hero-actions"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <motion.button
              className="hero-button primary"
              onClick={handleSearchClick}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              </svg>
              ابدأ البحث
            </motion.button>
            <motion.button
              className="hero-button secondary"
              onClick={() => navigate('about')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              تعرف على المزيد
            </motion.button>
          </motion.div>
        </div>
        <motion.div 
          className="hero-image"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.8 }}
        >
          <div className="hero-video-preview">
            <div className="video-preview-screen">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </div>
            <div className="video-preview-controls">
              <div className="control-button"></div>
              <div className="control-button"></div>
              <div className="control-button"></div>
            </div>
          </div>
        </motion.div>
      </motion.section>

      {/* Stats Section */}
      <motion.section className="stats-section" variants={itemVariants}>
        <h2 className="section-title">إحصائياتك</h2>
        <div className="stats-grid">
          <motion.div 
            className="stat-card"
            whileHover={{ scale: 1.02 }}
          >
            <div className="stat-icon videos">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </div>
            <div className="stat-content">
              <span className="stat-number">{stats.videosWatched}</span>
              <span className="stat-label">فيديو مشاهد</span>
            </div>
          </motion.div>

          <motion.div 
            className="stat-card"
            whileHover={{ scale: 1.02 }}
          >
            <div className="stat-icon ads">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <div className="stat-content">
              <span className="stat-number">{stats.adsBlocked}</span>
              <span className="stat-label">إعلان محجوب</span>
            </div>
          </motion.div>

          <motion.div 
            className="stat-card"
            whileHover={{ scale: 1.02 }}
          >
            <div className="stat-icon time">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
                <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
              </svg>
            </div>
            <div className="stat-content">
              <span className="stat-number">{Math.floor(stats.timeSpent / 60)}</span>
              <span className="stat-label">دقيقة مشاهدة</span>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Featured Videos */}
      <motion.section className="featured-section" variants={itemVariants}>
        <h2 className="section-title">فيديوهات مميزة</h2>
        {isLoadingVideos ? (
          <div className="videos-loading">
            <div className="loading-spinner">
              <div className="spinner"></div>
            </div>
            <p>جاري تحميل الفيديوهات المميزة...</p>
          </div>
        ) : (
          <div className="videos-grid">
            {featuredVideos.map((video, index) => (
            <motion.div
              key={video.id}
              className="video-card"
              variants={itemVariants}
              whileHover={{ scale: 1.02, y: -4 }}
              onClick={() => handleVideoClick(video.id)}
            >
              <div className="video-thumbnail">
                <img src={video.thumbnail} alt={video.title} loading="lazy" />
                <div className="video-duration">{video.duration}</div>
                <div className="video-overlay">
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8 5v14l11-7z"/>
                  </svg>
                </div>
              </div>
              <div className="video-info">
                <h3 className="video-title">{video.title}</h3>
                <p className="video-channel">{video.channel}</p>
                <p className="video-views">{video.views} مشاهدة</p>
              </div>
            </motion.div>
          ))}
          </div>
        )}
      </motion.section>

      {/* Features Section */}
      <motion.section className="features-section" variants={itemVariants}>
        <h2 className="section-title">المميزات الرئيسية</h2>
        <div className="features-grid">
          <motion.div className="feature-card" whileHover={{ scale: 1.02 }}>
            <div className="feature-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
            </div>
            <h3 className="feature-title">مانع إعلانات قوي</h3>
            <p className="feature-description">
              يحجب جميع أنواع الإعلانات لتجربة مشاهدة سلسة ومريحة
            </p>
          </motion.div>

          <motion.div className="feature-card" whileHover={{ scale: 1.02 }}>
            <div className="feature-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8z"/>
              </svg>
            </div>
            <h3 className="feature-title">واجهة عربية متقدمة</h3>
            <p className="feature-description">
              تصميم عربي كامل مع دعم RTL وثيم داكن مريح للعينين
            </p>
          </motion.div>

          <motion.div className="feature-card" whileHover={{ scale: 1.02 }}>
            <div className="feature-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h3 className="feature-title">أداء محسن</h3>
            <p className="feature-description">
              تشغيل سريع وسلس مع استهلاك أقل للموارد والذاكرة
            </p>
          </motion.div>
        </div>
      </motion.section>
    </motion.div>
  );
};

export default Home;
