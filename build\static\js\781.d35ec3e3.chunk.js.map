{"version": 3, "file": "static/js/781.d35ec3e3.chunk.js", "mappings": "wIA0UA,MACA,EADmB,IAvUnB,MACEA,WAAAA,GAGEC,KAAKC,OAASC,CAAAA,SAAAA,aAAAA,WAAAA,IAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,2BAA6B,WACvDH,KAAKI,QAAU,wCAGfJ,KAAKK,UAAYH,CAAAA,SAAAA,aAAAA,WAAAA,IAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,0BAC7BH,KAAKM,SAAW,yCAClB,CAGA,kBAAMC,CAAaC,GAAyC,IAAlCC,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAIG,EAASH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACrD,IACE,GAAIV,KAAKK,SACP,aAAaL,KAAKc,gBAAgBN,EAAOC,EAAYI,GAGvD,MAAME,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,UACNC,EAAGV,EACHW,KAAM,QACNV,WAAYA,EAAWW,WACvBC,IAAKrB,KAAKC,OACVqB,MAAO,YACPC,WAAY,WACZC,WAAY,KACZC,kBAAmB,OAGjBZ,GACFE,EAAOW,OAAO,YAAab,GAG7B,MAAMc,QAAiBC,MAAM,GAAG5B,KAAKI,kBAAkBW,KAEvD,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMC,QAAaL,EAASM,OAG5B,OAAOjC,KAAKkC,uBAAuBF,EACrC,CAAE,MAAOG,GAGP,OAFAC,QAAQD,MAAM,wBAAyBA,GAEhCnC,KAAKqC,qBAAqB7B,EACnC,CACF,CAGA,qBAAM8B,CAAgBC,GACpB,IACE,GAAIvC,KAAKK,SACP,aAAaL,KAAKwC,yBAAyBD,GAG7C,MAAMxB,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,oCACNwB,GAAIF,EACJlB,IAAKrB,KAAKC,SAGN0B,QAAiBC,MAAM,GAAG5B,KAAKI,kBAAkBW,KAEvD,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMC,QAAaL,EAASM,OAE5B,GAAID,EAAKU,OAASV,EAAKU,MAAM/B,OAAS,EACpC,OAAOX,KAAK2C,sBAAsBX,EAAKU,MAAM,IAG/C,MAAM,IAAIZ,MAAM,kBAClB,CAAE,MAAOK,GAEP,OADAC,QAAQD,MAAM,+BAAgCA,GACvCnC,KAAK4C,oBAAoBL,EAClC,CACF,CAGA,qBAAMzB,CAAgBN,EAAOC,EAAYI,GACvC,IACE,MAAME,EAAS,IAAIC,gBAAgB,CACjCE,EAAGV,EACHC,WAAYA,EAAWW,WACvBD,KAAM,UAGJN,GACFE,EAAOW,OAAO,YAAab,GAG7B,MAAMc,QAAiBC,MAAM,GAAG5B,KAAKM,mBAAmBS,KAExD,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,oBAAoBH,EAASI,UAG/C,MAAMC,QAAaL,EAASM,OAC5B,OAAOjC,KAAKkC,uBAAuBF,EACrC,CAAE,MAAOG,GAEP,OADAC,QAAQD,MAAM,sBAAuBA,GAC9BnC,KAAKqC,qBAAqB7B,EACnC,CACF,CAGA,8BAAMgC,CAAyBD,GAC7B,IACE,MAAMZ,QAAiBC,MAAM,GAAG5B,KAAKM,kBAAkBiC,KAEvD,IAAKZ,EAASE,GACZ,MAAM,IAAIC,MAAM,oBAAoBH,EAASI,UAG/C,MAAMC,QAAaL,EAASM,OAC5B,OAAOjC,KAAK2C,sBAAsBX,EACpC,CAAE,MAAOG,GAEP,OADAC,QAAQD,MAAM,6BAA8BA,GACrCnC,KAAK4C,oBAAoBL,EAClC,CACF,CAGAL,sBAAAA,CAAuBF,GAAO,IAADa,EAC3B,IAAKb,EAAKU,MACR,MAAO,CAAEI,OAAQ,GAAIC,cAAe,KAAMC,aAAc,GAe1D,MAAO,CACLF,OAbad,EAAKU,MAAMO,IAAIC,IAAI,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,MAAK,CACrCb,GAAIS,EAAKT,GAAGF,SAAWW,EAAKT,GAC5Bc,MAAOL,EAAKM,QAAQD,MACpBE,QAASP,EAAKM,QAAQE,aACtBC,YAAaT,EAAKM,QAAQG,YAC1BC,WAAyC,QAA9BT,EAAAD,EAAKM,QAAQK,WAAWC,cAAM,IAAAX,OAAA,EAA9BA,EAAgCY,OAAsC,QAAnCX,EAAIF,EAAKM,QAAQK,WAAWG,eAAO,IAAAZ,OAAA,EAA/BA,EAAiCW,KACnFE,YAAaf,EAAKM,QAAQS,YAC1BC,SAAUlE,KAAKmE,cAAiC,QAApBd,EAACH,EAAKkB,sBAAc,IAAAf,OAAA,EAAnBA,EAAqBa,UAClDG,MAAOrE,KAAKsE,YAA2B,QAAhBhB,EAACJ,EAAKqB,kBAAU,IAAAjB,OAAA,EAAfA,EAAiBkB,WACzCC,UAAWvB,EAAKM,QAAQiB,aAKxB1B,cAAef,EAAKe,eAAiB,KACrCC,cAA2B,QAAbH,EAAAb,EAAK0C,gBAAQ,IAAA7B,OAAA,EAAbA,EAAeG,eAAgB,EAEjD,CAGAL,qBAAAA,CAAsBO,GAAO,IAADyB,EAAAC,EAC1B,MAAO,CACLnC,GAAIS,EAAKT,GACTc,MAAOL,EAAKM,QAAQD,MACpBE,QAASP,EAAKM,QAAQE,aACtBC,YAAaT,EAAKM,QAAQG,YAC1BC,WAAyC,QAA9Be,EAAAzB,EAAKM,QAAQK,WAAWgB,cAAM,IAAAF,OAAA,EAA9BA,EAAgCZ,OAAmC,QAAhCa,EAAI1B,EAAKM,QAAQK,WAAWiB,YAAI,IAAAF,OAAA,EAA5BA,EAA8Bb,KAChFE,YAAaf,EAAKM,QAAQS,YAC1BC,SAAUlE,KAAKmE,cAAcjB,EAAKkB,eAAeF,UACjDG,MAAOrE,KAAKsE,YAAYpB,EAAKqB,WAAWC,WACxCO,MAAO/E,KAAKsE,YAAYpB,EAAKqB,WAAWS,WACxCP,UAAWvB,EAAKM,QAAQiB,UACxBQ,KAAM/B,EAAKM,QAAQyB,MAAQ,GAE/B,CAGAd,aAAAA,CAAcD,GACZ,IAAKA,EAAU,OAAO,EAEtB,MAAMgB,EAAQhB,EAASgB,MAAM,2BAC7B,IAAKA,EAAO,OAAO,EAMnB,OAAe,MAJDC,SAASD,EAAM,KAAO,GAIJ,IAHhBC,SAASD,EAAM,KAAO,IACtBC,SAASD,EAAM,KAAO,EAGxC,CAGAZ,WAAAA,CAAYE,GACV,IAAKA,EAAW,MAAO,IAEvB,MAAMY,EAAQD,SAASX,GACvB,OAAIY,GAAS,IACJ,IAAIA,EAAQ,KAASC,QAAQ,MAC3BD,GAAS,IACX,IAAIA,EAAQ,KAAMC,QAAQ,MAE5BD,EAAMhE,UACf,CAGAkE,cAAAA,CAAeC,GACb,IAAKA,EAAS,MAAO,OAErB,MAAMC,EAAQC,KAAKC,MAAMH,EAAU,MAC7BI,EAAUF,KAAKC,MAAOH,EAAU,KAAQ,IACxCK,EAAOL,EAAU,GAEvB,OAAIC,EAAQ,EACH,GAAGA,KAASG,EAAQvE,WAAWyE,SAAS,EAAG,QAAQD,EAAKxE,WAAWyE,SAAS,EAAG,OAEjF,GAAGF,KAAWC,EAAKxE,WAAWyE,SAAS,EAAG,MACnD,CAGAxD,oBAAAA,CAAqB7B,GACnB,MAAMsF,EAAa,CACjB,CACErD,GAAI,cACJc,MAAO,GAAG/C,yGACViD,QAAS,sEACTE,YAAa,sMACbC,UAAW,uDACXM,SAAU,OACVG,MAAO,OACPJ,YAAa,uBACbQ,UAAW,eAEb,CACEhC,GAAI,cACJc,MAAO,sBAAO/C,qDACdiD,QAAS,sEACTE,YAAa,yLACbC,UAAW,uDACXM,SAAU,OACVG,MAAO,OACPJ,YAAa,uBACbQ,UAAW,eAEb,CACEhC,GAAI,cACJc,MAAO,4BAAQ/C,4DACfiD,QAAS,kFACTE,YAAa,oMACbC,UAAW,uDACXM,SAAU,OACVG,MAAO,OACPJ,YAAa,uBACbQ,UAAW,gBAIf,MAAO,CACL3B,OAAQgD,EACR/C,cAAe,KACfC,aAAc8C,EAAWnF,OAE7B,CAGAiC,mBAAAA,CAAoBL,GAClB,MAAO,CACLE,GAAIF,EACJgB,MAAO,2GACPE,QAAS,sEACTE,YAAa,wGACbC,UAAW,8BAA8BrB,sBACzC2B,SAAU,OACVG,MAAO,OACPU,MAAO,MACPd,YAAa,uBACbQ,UAAW,cACXQ,KAAM,CAAC,uCAAU,iCAAS,wCAE9B,CAGA,uBAAMc,GAAuD,IAArCvE,EAAUd,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KAAMD,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACtD,IACE,GAAIV,KAAKK,SACP,aAAaL,KAAKgG,qBAAqBxE,EAAYf,GAGrD,MAAMM,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,oCACNgF,MAAO,cACPzE,aACAf,WAAYA,EAAWW,WACvBC,IAAKrB,KAAKC,OACViG,gBAAiB,MAGbvE,QAAiBC,MAAM,GAAG5B,KAAKI,kBAAkBW,KAEvD,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMC,QAAaL,EAASM,OAC5B,OAAOjC,KAAKkC,uBAAuBF,EACrC,CAAE,MAAOG,GAEP,OADAC,QAAQD,MAAM,0BAA2BA,GAClCnC,KAAKqC,qBAAqB,4EACnC,CACF,CAGA,0BAAM2D,CAAqBxE,EAAYf,GACrC,IACE,MAAMM,EAAS,IAAIC,gBAAgB,CACjCQ,aACAf,WAAYA,EAAWW,aAGnBO,QAAiBC,MAAM,GAAG5B,KAAKM,qBAAqBS,KAE1D,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,oBAAoBH,EAASI,UAG/C,MAAMC,QAAaL,EAASM,OAC5B,OAAOjC,KAAKkC,uBAAuBF,EACrC,CAAE,MAAOG,GAEP,OADAC,QAAQD,MAAM,wBAAyBA,GAChCnC,KAAKqC,qBAAqB,4EACnC,CACF,E,uHC9TF,MA0MA,EA1Me8D,IAAsB,IAArB,YAAEC,GAAaD,EAC7B,MAAOE,EAAeC,IAAoBC,EAAAA,EAAAA,UAAS,KAC5CC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCpE,EAAOuE,IAAYH,EAAAA,EAAAA,UAAS,OAGjCI,QAAQ,MAAEnG,EAAK,QAAEoG,GAAS,eAC1BC,EAAc,mBACdC,EACAR,iBAAkBS,IAChBC,EAAAA,EAAAA,MAEE,SAAEC,IAAaC,EAAAA,EAAAA,MAEfC,GAA0B,OAAXf,QAAW,IAAXA,OAAW,EAAXA,EAAa5F,QAASA,GAAS,IAEpD4G,EAAAA,EAAAA,WAAU,KACJD,IACFN,EAAeM,GACfE,EAAcF,KAEf,CAACA,IAEJ,MAAME,EAAgBC,UACpB,GAAKC,EAAYC,OAAjB,CAEAf,GAAa,GACbC,EAAS,MAET,IAEE,MAAMe,QAAgBC,EAAAA,EAAWnH,aAAagH,EAAa,IAC3DjB,EAAiBmB,EAAQ3E,QACzBiE,EAAsBU,EAAQ3E,QAC9BgE,EAAmBS,EACrB,CAAE,MAAOI,GACPjB,EAAS,0LACTtE,QAAQD,MAAM,gBAAiBwF,EACjC,CAAC,QACClB,GAAa,EACf,CAhB+B,GAgC3BmB,EAAcC,IAClB,MAAMC,EAAO,IAAIC,KAAKF,GAChBG,EAAM,IAAID,KACVE,EAAWxC,KAAKyC,IAAIF,EAAMF,GAC1BK,EAAW1C,KAAK2C,KAAKH,EAAQ,OAEnC,OAAiB,IAAbE,EAAuB,qBACvBA,EAAW,EAAU,sBAAOA,6BAC5BA,EAAW,GAAW,sBAAO1C,KAAKC,MAAMyC,EAAW,0CACnDA,EAAW,IAAY,sBAAO1C,KAAKC,MAAMyC,EAAW,+BACjD,sBAAO1C,KAAKC,MAAMyC,EAAW,uCAGtC,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAE1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,kEACH/H,IACC6H,EAAAA,EAAAA,MAAA,KAAGC,UAAU,eAAcC,SAAA,CAAC,iDAChBF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAAQ,IAAE/H,EAAM,aAM/BgG,IACCgC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BC,EAAAA,EAAAA,KAACC,EAAAA,EAAc,CAACC,KAAK,iEAKxBvG,IACCkG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,eAAcC,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,+HAGZP,EAAAA,EAAAA,KAAA,MAAAD,SAAI,oEACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAIpG,KACJqG,EAAAA,EAAAA,KAAA,UACEF,UAAU,eACVU,QAASA,IAAM3B,EAAc7G,GAAO+H,SACrC,wFAOH/B,IAAcrE,GAASkE,EAAc1F,OAAS,IAC9C0H,EAAAA,EAAAA,MAACY,EAAAA,EAAOC,IAAG,CACTZ,UAAU,iBACVa,QAAS,CAAEC,QAAS,GACpBC,QAAS,CAAED,QAAS,GACpBE,WAAY,CAAEpF,SAAU,IAAMqE,SAAA,EAE9BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,wEAAelC,EAAc1F,OAAO,wCAG5C6H,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,SAC1BlC,EAAcpD,IAAI,CAACsG,EAAOC,KACzBnB,EAAAA,EAAAA,MAACY,EAAAA,EAAOC,IAAG,CAETZ,UAAU,cACVa,QAAS,CAAEC,QAAS,EAAGK,EAAG,IAC1BJ,QAAS,CAAED,QAAS,EAAGK,EAAG,GAC1BH,WAAY,CAAEI,MAAe,GAARF,GACrBR,QAASA,KAAMW,OAlFHpH,EAkFoBgH,EAAM9G,QAjFlDwE,EAAS,SAAU,CAAE1E,YADGA,OAkF8BgG,SAAA,EAE1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,OAAKoB,IAAKL,EAAM3F,UAAWiG,IAAKN,EAAMhG,MAAOuG,QAAQ,UACrDtB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,SAAEgB,EAAMrF,YACvCsE,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,UAC3BC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,0BAKdV,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,eAAcC,SAAEgB,EAAMhG,SACpC8E,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,iBAAgBC,SAAEgB,EAAM9F,WACxC4E,EAAAA,EAAAA,MAAA,QAAMC,UAAU,eAAcC,SAAA,CAAEgB,EAAMlF,MAAM,4CAC5CmE,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEX,EAAW2B,EAAMtF,mBAElDuE,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBAAoBC,SAAEgB,EAAM5F,mBAxBtC4F,EAAM9G,WAiCnB+D,IAAcrE,GAAkC,IAAzBkE,EAAc1F,QAAgBH,IACrD6H,EAAAA,EAAAA,MAAA,OAAKC,UAAU,eAAcC,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,oPAGZP,EAAAA,EAAAA,KAAA,MAAAD,SAAI,0EACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,4OACHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qBAAoBC,SAAA,EACjCC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,yBACJF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,qHACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,oHACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,sJAOV/H,GAASoG,EAAQjG,OAAS,IAC1B0H,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,oHACJC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,SAC1B3B,EAAQmD,MAAM,EAAG,IAAI9G,IAAI,CAACC,EAAMsG,KAC/BnB,EAAAA,EAAAA,MAACY,EAAAA,EAAOe,OAAM,CAEZ1B,UAAU,eACVU,QAASA,KAvIKiB,SAuIoB/G,GAtIjCsE,SACXX,EAAeoD,GACf5C,EAAc4C,KAqIJd,QAAS,CAAEC,QAAS,EAAGc,GAAI,IAC3Bb,QAAS,CAAED,QAAS,EAAGc,EAAG,GAC1BZ,WAAY,CAAEI,MAAe,IAARF,GAAejB,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,wOAEVP,EAAAA,EAAAA,KAAA,QAAAD,SAAOrF,MAVF,WAAWA,KAAQsG,c", "sources": ["services/youtubeAPI.js", "components/pages/Search.js"], "sourcesContent": ["// YouTube Data API service\n// This service handles all interactions with YouTube's real API\n\nclass YouTubeAPIService {\n  constructor() {\n    // For production, you would get this from environment variables\n    // For now, we'll use a demo key or implement a proxy server\n    this.apiKey = process.env.REACT_APP_YOUTUBE_API_KEY || 'DEMO_KEY';\n    this.baseURL = 'https://www.googleapis.com/youtube/v3';\n    \n    // Fallback to a proxy server if no API key is available\n    this.useProxy = !process.env.REACT_APP_YOUTUBE_API_KEY;\n    this.proxyURL = 'https://youtube-proxy-api.herokuapp.com'; // Example proxy\n  }\n\n  // Search for videos using YouTube Data API\n  async searchVideos(query, maxResults = 25, pageToken = '') {\n    try {\n      if (this.useProxy) {\n        return await this.searchWithProxy(query, maxResults, pageToken);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet',\n        q: query,\n        type: 'video',\n        maxResults: maxResults.toString(),\n        key: this.apiKey,\n        order: 'relevance',\n        safeSearch: 'moderate',\n        regionCode: 'SA', // Saudi Arabia for Arabic content\n        relevanceLanguage: 'ar'\n      });\n\n      if (pageToken) {\n        params.append('pageToken', pageToken);\n      }\n\n      const response = await fetch(`${this.baseURL}/search?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      \n      // Transform the data to our format\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('YouTube search error:', error);\n      // Fallback to mock data if API fails\n      return this.getMockSearchResults(query);\n    }\n  }\n\n  // Get video details by ID\n  async getVideoDetails(videoId) {\n    try {\n      if (this.useProxy) {\n        return await this.getVideoDetailsWithProxy(videoId);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet,statistics,contentDetails',\n        id: videoId,\n        key: this.apiKey\n      });\n\n      const response = await fetch(`${this.baseURL}/videos?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      \n      if (data.items && data.items.length > 0) {\n        return this.transformVideoDetails(data.items[0]);\n      }\n      \n      throw new Error('Video not found');\n    } catch (error) {\n      console.error('YouTube video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Search with proxy server (fallback method)\n  async searchWithProxy(query, maxResults, pageToken) {\n    try {\n      const params = new URLSearchParams({\n        q: query,\n        maxResults: maxResults.toString(),\n        type: 'video'\n      });\n\n      if (pageToken) {\n        params.append('pageToken', pageToken);\n      }\n\n      const response = await fetch(`${this.proxyURL}/search?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`Proxy API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('Proxy search error:', error);\n      return this.getMockSearchResults(query);\n    }\n  }\n\n  // Get video details with proxy\n  async getVideoDetailsWithProxy(videoId) {\n    try {\n      const response = await fetch(`${this.proxyURL}/video/${videoId}`);\n      \n      if (!response.ok) {\n        throw new Error(`Proxy API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformVideoDetails(data);\n    } catch (error) {\n      console.error('Proxy video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Transform YouTube API search results to our format\n  transformSearchResults(data) {\n    if (!data.items) {\n      return { videos: [], nextPageToken: null, totalResults: 0 };\n    }\n\n    const videos = data.items.map(item => ({\n      id: item.id.videoId || item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails?.duration),\n      views: this.formatViews(item.statistics?.viewCount),\n      channelId: item.snippet.channelId\n    }));\n\n    return {\n      videos,\n      nextPageToken: data.nextPageToken || null,\n      totalResults: data.pageInfo?.totalResults || 0\n    };\n  }\n\n  // Transform video details to our format\n  transformVideoDetails(item) {\n    return {\n      id: item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.maxres?.url || item.snippet.thumbnails.high?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails.duration),\n      views: this.formatViews(item.statistics.viewCount),\n      likes: this.formatViews(item.statistics.likeCount),\n      channelId: item.snippet.channelId,\n      tags: item.snippet.tags || []\n    };\n  }\n\n  // Parse ISO 8601 duration to seconds\n  parseDuration(duration) {\n    if (!duration) return 0;\n    \n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/);\n    if (!match) return 0;\n    \n    const hours = parseInt(match[1]) || 0;\n    const minutes = parseInt(match[2]) || 0;\n    const seconds = parseInt(match[3]) || 0;\n    \n    return hours * 3600 + minutes * 60 + seconds;\n  }\n\n  // Format view count\n  formatViews(viewCount) {\n    if (!viewCount) return '0';\n    \n    const count = parseInt(viewCount);\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  }\n\n  // Format duration from seconds to MM:SS or HH:MM:SS\n  formatDuration(seconds) {\n    if (!seconds) return '0:00';\n    \n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n    \n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n  // Mock search results (fallback)\n  getMockSearchResults(query) {\n    const mockVideos = [\n      {\n        id: 'dQw4w9WgXcQ',\n        title: `${query} - نتيجة البحث الأولى`,\n        channel: 'قناة تجريبية',\n        description: 'وصف مفصل للفيديو الأول في نتائج البحث...',\n        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',\n        duration: '3:32',\n        views: '1.2M',\n        publishedAt: '2023-01-15T10:00:00Z',\n        channelId: 'UC123456789'\n      },\n      {\n        id: 'kJQP7kiw5Fk',\n        title: `شرح ${query} بالتفصيل`,\n        channel: 'قناة التعليم',\n        description: 'شرح شامل ومفصل حول الموضوع المطلوب...',\n        thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg',\n        duration: '4:42',\n        views: '850K',\n        publishedAt: '2023-02-20T15:30:00Z',\n        channelId: 'UC987654321'\n      },\n      {\n        id: 'fJ9rUzIMcZQ',\n        title: `أفضل ${query} لهذا العام`,\n        channel: 'قناة المراجعات',\n        description: 'مراجعة شاملة لأفضل الخيارات المتاحة...',\n        thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',\n        duration: '5:55',\n        views: '2.1M',\n        publishedAt: '2023-03-10T12:15:00Z',\n        channelId: 'UC456789123'\n      }\n    ];\n\n    return {\n      videos: mockVideos,\n      nextPageToken: null,\n      totalResults: mockVideos.length\n    };\n  }\n\n  // Mock video details (fallback)\n  getMockVideoDetails(videoId) {\n    return {\n      id: videoId,\n      title: 'فيديو يوتيوب تجريبي',\n      channel: 'قناة تجريبية',\n      description: 'وصف تجريبي للفيديو...',\n      thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n      duration: '3:45',\n      views: '1.5M',\n      likes: '25K',\n      publishedAt: '2023-01-01T00:00:00Z',\n      channelId: 'UC123456789',\n      tags: ['تجريبي', 'فيديو', 'يوتيوب']\n    };\n  }\n\n  // Get trending videos\n  async getTrendingVideos(regionCode = 'SA', maxResults = 25) {\n    try {\n      if (this.useProxy) {\n        return await this.getTrendingWithProxy(regionCode, maxResults);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet,statistics,contentDetails',\n        chart: 'mostPopular',\n        regionCode,\n        maxResults: maxResults.toString(),\n        key: this.apiKey,\n        videoCategoryId: '0' // All categories\n      });\n\n      const response = await fetch(`${this.baseURL}/videos?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('YouTube trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n\n  // Get trending with proxy\n  async getTrendingWithProxy(regionCode, maxResults) {\n    try {\n      const params = new URLSearchParams({\n        regionCode,\n        maxResults: maxResults.toString()\n      });\n\n      const response = await fetch(`${this.proxyURL}/trending?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`Proxy API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('Proxy trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n}\n\n// Create and export a singleton instance\nconst youtubeAPI = new YouTubeAPIService();\nexport default youtubeAPI;\n", "import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport useRouter from '../../services/router';\nimport LoadingSpinner from '../common/LoadingSpinner';\nimport youtubeAPI from '../../services/youtubeAPI';\nimport './Search.css';\n\nconst Search = ({ routeParams }) => {\n  const [searchResults, setSearchResults] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  \n  const { \n    search: { query, history },\n    setSearchQuery,\n    addToSearchHistory,\n    setSearchResults: setStoreSearchResults\n  } = useAppStore();\n  \n  const { navigate } = useRouter();\n  \n  const initialQuery = routeParams?.query || query || '';\n\n  useEffect(() => {\n    if (initialQuery) {\n      setSearchQuery(initialQuery);\n      performSearch(initialQuery);\n    }\n  }, [initialQuery]);\n\n  const performSearch = async (searchQuery) => {\n    if (!searchQuery.trim()) return;\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Use real YouTube API\n      const results = await youtubeAPI.searchVideos(searchQuery, 20);\n      setSearchResults(results.videos);\n      setStoreSearchResults(results.videos);\n      addToSearchHistory(searchQuery);\n    } catch (err) {\n      setError('فشل في البحث. يرجى المحاولة مرة أخرى.');\n      console.error('Search error:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  const handleVideoClick = (videoId) => {\n    navigate('player', { videoId });\n  };\n\n  const handleSearchSubmit = (newQuery) => {\n    if (newQuery.trim()) {\n      setSearchQuery(newQuery);\n      performSearch(newQuery);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) return 'أمس';\n    if (diffDays < 7) return `منذ ${diffDays} أيام`;\n    if (diffDays < 30) return `منذ ${Math.floor(diffDays / 7)} أسابيع`;\n    if (diffDays < 365) return `منذ ${Math.floor(diffDays / 30)} أشهر`;\n    return `منذ ${Math.floor(diffDays / 365)} سنوات`;\n  };\n\n  return (\n    <div className=\"search-page\">\n      {/* Search Header */}\n      <div className=\"search-header\">\n        <h1>نتائج البحث</h1>\n        {query && (\n          <p className=\"search-query\">\n            البحث عن: <strong>\"{query}\"</strong>\n          </p>\n        )}\n      </div>\n\n      {/* Loading State */}\n      {isLoading && (\n        <div className=\"search-loading\">\n          <LoadingSpinner text=\"جاري البحث...\" />\n        </div>\n      )}\n\n      {/* Error State */}\n      {error && (\n        <div className=\"search-error\">\n          <div className=\"error-icon\">\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          </div>\n          <h3>خطأ في البحث</h3>\n          <p>{error}</p>\n          <button \n            className=\"retry-button\"\n            onClick={() => performSearch(query)}\n          >\n            إعادة المحاولة\n          </button>\n        </div>\n      )}\n\n      {/* Search Results */}\n      {!isLoading && !error && searchResults.length > 0 && (\n        <motion.div \n          className=\"search-results\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.3 }}\n        >\n          <div className=\"results-info\">\n            <span>تم العثور على {searchResults.length} نتيجة</span>\n          </div>\n          \n          <div className=\"results-list\">\n            {searchResults.map((video, index) => (\n              <motion.div\n                key={video.id}\n                className=\"result-item\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 }}\n                onClick={() => handleVideoClick(video.id)}\n              >\n                <div className=\"result-thumbnail\">\n                  <img src={video.thumbnail} alt={video.title} loading=\"lazy\" />\n                  <div className=\"video-duration\">{video.duration}</div>\n                  <div className=\"play-overlay\">\n                    <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                      <path d=\"M8 5v14l11-7z\"/>\n                    </svg>\n                  </div>\n                </div>\n                \n                <div className=\"result-content\">\n                  <h3 className=\"result-title\">{video.title}</h3>\n                  <div className=\"result-meta\">\n                    <span className=\"result-channel\">{video.channel}</span>\n                    <span className=\"result-views\">{video.views} مشاهدة</span>\n                    <span className=\"result-date\">{formatDate(video.publishedAt)}</span>\n                  </div>\n                  <p className=\"result-description\">{video.description}</p>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      )}\n\n      {/* Empty State */}\n      {!isLoading && !error && searchResults.length === 0 && query && (\n        <div className=\"search-empty\">\n          <div className=\"empty-icon\">\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\n            </svg>\n          </div>\n          <h2>لا توجد نتائج</h2>\n          <p>لم نتمكن من العثور على أي فيديوهات تطابق بحثك</p>\n          <div className=\"search-suggestions\">\n            <h4>جرب:</h4>\n            <ul>\n              <li>التأكد من صحة الكتابة</li>\n              <li>استخدام كلمات مختلفة</li>\n              <li>استخدام كلمات أكثر عمومية</li>\n            </ul>\n          </div>\n        </div>\n      )}\n\n      {/* Search History */}\n      {!query && history.length > 0 && (\n        <div className=\"search-history\">\n          <h2>عمليات البحث السابقة</h2>\n          <div className=\"history-list\">\n            {history.slice(0, 10).map((item, index) => (\n              <motion.button\n                key={`history-${item}-${index}`}\n                className=\"history-item\"\n                onClick={() => handleSearchSubmit(item)}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.05 }}\n              >\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z\"/>\n                </svg>\n                <span>{item}</span>\n              </motion.button>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Search;\n"], "names": ["constructor", "this", "<PERSON><PERSON><PERSON><PERSON>", "process", "REACT_APP_YOUTUBE_API_KEY", "baseURL", "useProxy", "proxyURL", "searchVideos", "query", "maxResults", "arguments", "length", "undefined", "pageToken", "searchWithProxy", "params", "URLSearchParams", "part", "q", "type", "toString", "key", "order", "safeSearch", "regionCode", "relevanceLanguage", "append", "response", "fetch", "ok", "Error", "status", "data", "json", "transformSearchResults", "error", "console", "getMockSearchResults", "getVideoDetails", "videoId", "getVideoDetailsWithProxy", "id", "items", "transformVideoDetails", "getMockVideoDetails", "_data$pageInfo", "videos", "nextPageToken", "totalResults", "map", "item", "_item$snippet$thumbna", "_item$snippet$thumbna2", "_item$contentDetails", "_item$statistics", "title", "snippet", "channel", "channelTitle", "description", "thumbnail", "thumbnails", "medium", "url", "default", "publishedAt", "duration", "parseDuration", "contentDetails", "views", "formatViews", "statistics", "viewCount", "channelId", "pageInfo", "_item$snippet$thumbna3", "_item$snippet$thumbna4", "maxres", "high", "likes", "likeCount", "tags", "match", "parseInt", "count", "toFixed", "formatDuration", "seconds", "hours", "Math", "floor", "minutes", "secs", "padStart", "mockVideos", "getTrendingVideos", "getTrendingWithProxy", "chart", "videoCategoryId", "_ref", "routeParams", "searchResults", "setSearchResults", "useState", "isLoading", "setIsLoading", "setError", "search", "history", "setSearch<PERSON>uery", "addToSearchHistory", "setStoreSearchResults", "useAppStore", "navigate", "useRouter", "initialQuery", "useEffect", "performSearch", "async", "searchQuery", "trim", "results", "youtubeAPI", "err", "formatDate", "dateString", "date", "Date", "now", "diffTime", "abs", "diffDays", "ceil", "_jsxs", "className", "children", "_jsx", "LoadingSpinner", "text", "width", "height", "viewBox", "fill", "d", "onClick", "motion", "div", "initial", "opacity", "animate", "transition", "video", "index", "y", "delay", "handleVideoClick", "src", "alt", "loading", "slice", "button", "<PERSON><PERSON><PERSON><PERSON>", "x"], "sourceRoot": ""}