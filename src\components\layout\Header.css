/* Header component styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: var(--header-height);
  background-color: var(--surface-primary);
  border-bottom: 1px solid var(--border-primary);
  z-index: var(--z-fixed);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 var(--spacing-md);
  max-width: 100%;
}

/* Left section */
.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-shrink: 0;
}

.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--text-secondary);
  transition: var(--transition-fast);
}

.sidebar-toggle:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
  user-select: none;
}

.logo:hover {
  background-color: var(--surface-hover);
}

.logo-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: var(--radius-md);
  color: white;
}

.logo-text {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.logo-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.logo-subtitle {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Center section */
.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  padding: 0 var(--spacing-lg);
  max-width: 800px;
  margin: 0 auto;
}

.search-container {
  width: 100%;
  max-width: 600px;
  transition: var(--transition-normal);
}

.search-container.focused {
  transform: scale(1.02);
}

/* Right section */
.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}

.current-video-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--surface-secondary);
  border-radius: var(--radius-md);
  max-width: 200px;
  overflow: hidden;
}

.video-thumbnail {
  position: relative;
  width: 32px;
  height: 18px;
  border-radius: var(--radius-sm);
  overflow: hidden;
  flex-shrink: 0;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.video-details {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.video-channel {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.header-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background-color: transparent;
  color: var(--text-secondary);
  transition: var(--transition-fast);
}

.header-button:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
}

.header-button:active {
  transform: scale(0.95);
}

.settings-button:hover {
  color: var(--primary-color);
}

/* Video progress bar */
.video-progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--surface-tertiary);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  transition: width 0.1s ease;
}

/* Responsive design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 var(--spacing-sm);
  }
  
  .logo-text {
    display: none;
  }
  
  .current-video-info {
    max-width: 150px;
  }
  
  .video-details {
    display: none;
  }
  
  .search-container {
    max-width: 400px;
  }
}

@media (max-width: 640px) {
  .header-left {
    gap: var(--spacing-sm);
  }
  
  .header-right {
    gap: var(--spacing-xs);
  }
  
  .current-video-info {
    padding: var(--spacing-xs);
  }
  
  .search-container {
    max-width: 300px;
  }
}

/* Dark theme specific adjustments */
[data-theme="dark"] .header {
  background-color: rgba(26, 26, 26, 0.95);
}

/* Light theme specific adjustments */
[data-theme="light"] .header {
  background-color: rgba(255, 255, 255, 0.95);
  border-bottom-color: var(--border-secondary);
}

/* Animation for logo */
@keyframes logoGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 0, 0, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 0, 0, 0.6);
  }
}

.logo-icon:hover {
  animation: logoGlow 2s ease-in-out infinite;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .header,
  .search-container,
  .current-video-info,
  .header-button,
  .logo {
    transition: none;
  }
  
  .logo-icon:hover {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .header {
    border-bottom-width: 2px;
  }
  
  .header-button:focus {
    outline: 3px solid var(--primary-color);
  }
}
