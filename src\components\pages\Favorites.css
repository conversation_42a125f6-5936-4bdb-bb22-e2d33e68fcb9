/* Favorites page styles */
.favorites-page {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.favorites-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.favorites-container h1 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}

.favorites-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  gap: var(--spacing-md);
  background-color: var(--surface-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3xl);
  border: 1px solid var(--border-primary);
}

.empty-icon {
  color: var(--text-tertiary);
}

.favorites-empty h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.favorites-empty p {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

/* Responsive design */
@media (max-width: 768px) {
  .favorites-page {
    padding: var(--spacing-md);
  }
  
  .favorites-empty {
    padding: var(--spacing-xl);
  }
  
  .favorites-container h1 {
    font-size: var(--font-size-xl);
  }
  
  .favorites-empty h2 {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 640px) {
  .favorites-page {
    padding: var(--spacing-sm);
  }
  
  .favorites-empty {
    padding: var(--spacing-lg);
  }
}
