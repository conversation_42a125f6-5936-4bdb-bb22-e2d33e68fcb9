/* Search page styles */
.search-page {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.search-header {
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.search-header h1 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.search-query {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin: 0;
}

.search-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.search-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
  gap: var(--spacing-md);
}

.error-icon {
  color: var(--error-color);
}

.retry-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-fast);
}

.retry-button:hover {
  background-color: var(--primary-hover);
}

.search-results {
  margin-bottom: var(--spacing-xl);
}

.results-info {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--surface-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.result-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--surface-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  cursor: pointer;
  transition: var(--transition-fast);
}

.result-item:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-hover);
  transform: translateY(-2px);
}

.result-thumbnail {
  position: relative;
  width: 200px;
  height: 112px;
  flex-shrink: 0;
  border-radius: var(--radius-md);
  overflow: hidden;
}

.result-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-duration {
  position: absolute;
  bottom: var(--spacing-xs);
  left: var(--spacing-xs);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 2px var(--spacing-xs);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.play-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 48px;
  height: 48px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: var(--transition-fast);
}

.result-item:hover .play-overlay {
  opacity: 1;
}

.result-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.result-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin: 0;
  line-height: var(--line-height-tight);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.result-channel {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.result-views {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.result-date {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.result-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.search-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  gap: var(--spacing-md);
}

.empty-icon {
  color: var(--text-tertiary);
}

.search-suggestions {
  margin-top: var(--spacing-lg);
  text-align: right;
}

.search-suggestions h4 {
  font-size: var(--font-size-md);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.search-suggestions ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.search-suggestions li {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
  position: relative;
  padding-right: var(--spacing-md);
}

.search-suggestions li::before {
  content: '•';
  position: absolute;
  right: 0;
  color: var(--primary-color);
}

.search-history {
  margin-bottom: var(--spacing-xl);
}

.search-history h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.history-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--surface-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  font-family: var(--font-family-primary);
  text-align: right;
  cursor: pointer;
  transition: var(--transition-fast);
}

.history-item:hover {
  background-color: var(--surface-hover);
  color: var(--text-primary);
  border-color: var(--border-hover);
}

.history-item span {
  flex: 1;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-page {
    padding: var(--spacing-md);
  }
  
  .result-item {
    flex-direction: column;
  }
  
  .result-thumbnail {
    width: 100%;
    height: 200px;
  }
  
  .result-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}

@media (max-width: 640px) {
  .search-page {
    padding: var(--spacing-sm);
  }
  
  .result-item {
    padding: var(--spacing-sm);
  }
  
  .result-thumbnail {
    height: 160px;
  }
  
  .search-header h1 {
    font-size: var(--font-size-xl);
  }
}
