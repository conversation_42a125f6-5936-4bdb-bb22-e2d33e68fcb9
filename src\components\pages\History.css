/* History page styles */
.history-page {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

.history-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.history-container h1 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}

.history-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
  gap: var(--spacing-md);
  background-color: var(--surface-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3xl);
  border: 1px solid var(--border-primary);
}

.empty-icon {
  color: var(--text-tertiary);
}

.history-empty h2 {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.history-empty p {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin: 0;
  line-height: var(--line-height-relaxed);
}

.stats-summary {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background-color: var(--surface-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.stats-summary p {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0;
}

.stats-summary strong {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
}

/* Responsive design */
@media (max-width: 768px) {
  .history-page {
    padding: var(--spacing-md);
  }
  
  .history-empty {
    padding: var(--spacing-xl);
  }
  
  .history-container h1 {
    font-size: var(--font-size-xl);
  }
  
  .history-empty h2 {
    font-size: var(--font-size-lg);
  }
}

@media (max-width: 640px) {
  .history-page {
    padding: var(--spacing-sm);
  }
  
  .history-empty {
    padding: var(--spacing-lg);
  }
}
