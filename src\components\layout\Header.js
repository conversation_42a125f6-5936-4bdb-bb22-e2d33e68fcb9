import React, { useState } from 'react';
import { motion } from 'framer-motion';
import useAppStore from '../../stores/appStore';
import useRouter from '../../services/router';
import SearchBar from '../common/SearchBar';
import ThemeToggle from '../common/ThemeToggle';
import WindowControls from '../common/WindowControls';
import './Header.css';

const Header = () => {
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const { currentVideo, sidebar, toggleSidebar } = useAppStore();
  const { currentRoute, navigate } = useRouter();

  const handleLogoClick = () => {
    navigate('home');
  };

  const handleSearchSubmit = (query) => {
    if (query.trim()) {
      navigate('search', { query: query.trim() });
    }
  };

  return (
    <motion.header 
      className="header"
      initial={{ y: -60 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="header-content">
        {/* Left section - Logo and navigation */}
        <div className="header-left">
          <button
            className="sidebar-toggle"
            onClick={toggleSidebar}
            aria-label={sidebar.isOpen ? 'إخفاء الشريط الجانبي' : 'إظهار الشريط الجانبي'}
          >
            <motion.div
              animate={{ rotate: sidebar.isOpen ? 0 : 180 }}
              transition={{ duration: 0.2 }}
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
              </svg>
            </motion.div>
          </button>

          <motion.div 
            className="logo"
            onClick={handleLogoClick}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <div className="logo-icon">
              <svg width="32" height="32" viewBox="0 0 24 24" fill="currentColor">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </div>
            <div className="logo-text">
              <span className="logo-title">YouTube Player</span>
              <span className="logo-subtitle">Pro</span>
            </div>
          </motion.div>
        </div>

        {/* Center section - Search */}
        <div className="header-center">
          <motion.div
            className={`search-container ${isSearchFocused ? 'focused' : ''}`}
            animate={{ 
              width: isSearchFocused ? '100%' : '60%',
              maxWidth: isSearchFocused ? '800px' : '600px'
            }}
            transition={{ duration: 0.2 }}
          >
            <SearchBar
              onSubmit={handleSearchSubmit}
              onFocus={() => setIsSearchFocused(true)}
              onBlur={() => setIsSearchFocused(false)}
              placeholder="ابحث في يوتيوب..."
              autoFocus={currentRoute === 'search'}
            />
          </motion.div>
        </div>

        {/* Right section - Controls */}
        <div className="header-right">
          {/* Current video info */}
          {currentVideo.id && (
            <motion.div 
              className="current-video-info"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
            >
              <div className="video-thumbnail">
                <img 
                  src={`https://img.youtube.com/vi/${currentVideo.id}/mqdefault.jpg`}
                  alt={currentVideo.title}
                  loading="lazy"
                />
                <div className="play-indicator">
                  {currentVideo.isPlaying ? (
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                    </svg>
                  ) : (
                    <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                  )}
                </div>
              </div>
              <div className="video-details">
                <div className="video-title" title={currentVideo.title}>
                  {currentVideo.title}
                </div>
                <div className="video-channel" title={currentVideo.channel}>
                  {currentVideo.channel}
                </div>
              </div>
            </motion.div>
          )}

          {/* Theme toggle */}
          <ThemeToggle />

          {/* Settings button */}
          <motion.button
            className="header-button settings-button"
            onClick={() => navigate('settings')}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            aria-label="الإعدادات"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
            </svg>
          </motion.button>

          {/* Window controls (for Electron) */}
          {window.electronAPI && <WindowControls />}
        </div>
      </div>

      {/* Progress bar for current video */}
      {currentVideo.id && currentVideo.duration > 0 && (
        <motion.div 
          className="video-progress-bar"
          initial={{ scaleX: 0 }}
          animate={{ scaleX: 1 }}
          style={{ originX: 0 }}
        >
          <motion.div 
            className="progress-fill"
            animate={{ 
              width: `${(currentVideo.currentTime / currentVideo.duration) * 100}%` 
            }}
            transition={{ duration: 0.1 }}
          />
        </motion.div>
      )}
    </motion.header>
  );
};

export default Header;
