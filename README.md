# YouTube Player Pro 🎥

مشغل يوتيوب احترافي مع واجهة عربية متقدمة ومانع إعلانات قوي ونظام ربح مدمج

## ✨ المميزات

### 🎯 المميزات الأساسية
- **واجهة عربية كاملة** مع دعم RTL
- **ثيم داكن متقدم** مع إمكانية التخصيص
- **مانع إعلانات قوي** يحجب جميع أنواع الإعلانات
- **مشغل فيديو محسن** مع تحكم كامل في الجودة
- **بحث متقدم** مع نتائج سريعة ودقيقة
- **حفظ التفضيلات** تلقائياً

### 🚀 المميزات المتقدمة
- **نظام ربح للمطور** مع إحصائيات مفصلة
- **إدارة حالة متقدمة** باستخدام Zustand
- **تحسين الأداء** مع React Query
- **واجهة متجاوبة** تعمل على جميع الأحجام
- **اختبارات شاملة** مع تغطية عالية
- **أمان محسن** مع أفضل الممارسات

## 🛠️ التقنيات المستخدمة

- **Frontend**: React 18, React Router, Framer Motion
- **Desktop**: Electron 23
- **State Management**: Zustand, React Query
- **Styling**: CSS Modules, CSS Variables
- **Testing**: Jest, React Testing Library
- **Build**: Webpack, Electron Builder
- **Code Quality**: ESLint, Prettier

## 📦 التثبيت والتشغيل

### متطلبات النظام
- Node.js 16+ 
- npm 8+
- Windows 10+, macOS 10.15+, أو Linux

### خطوات التثبيت

```bash
# استنساخ المشروع
git clone https://github.com/yourusername/youtube-player-pro.git
cd youtube-player-pro

# تثبيت التبعيات
npm install

# تشغيل في وضع التطوير
npm run electron:dev

# بناء للإنتاج
npm run electron:dist
```

## 🎮 كيفية الاستخدام

1. **البحث**: استخدم شريط البحث للعثور على الفيديوهات
2. **التشغيل**: انقر على أي فيديو لتشغيله
3. **الإعدادات**: اضبط الجودة والثيم والمميزات الأخرى
4. **مانع الإعلانات**: يعمل تلقائياً لحجب جميع الإعلانات

## 🧪 الاختبارات

```bash
# تشغيل الاختبارات
npm test

# تشغيل الاختبارات مع التغطية
npm run test:coverage

# تشغيل الاختبارات في وضع المراقبة
npm run test:watch
```

## 🔧 التطوير

```bash
# تشغيل في وضع التطوير
npm start

# فحص الكود
npm run lint

# إصلاح مشاكل الكود
npm run lint:fix

# تنسيق الكود
npm run format

# تحليل حجم الحزمة
npm run analyze
```

## 📱 البناء والتوزيع

```bash
# بناء لجميع المنصات
npm run electron:dist

# بناء لـ Windows فقط
npm run electron:dist-win

# بناء لـ macOS فقط
npm run electron:dist-mac

# بناء لـ Linux فقط
npm run electron:dist-linux
```

## 🤝 المساهمة

نرحب بجميع المساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

- **البريد الإلكتروني**: <EMAIL>
- **GitHub Issues**: [إنشاء مشكلة جديدة](https://github.com/yourusername/youtube-player-pro/issues)
- **التوثيق**: [الوثائق الكاملة](https://docs.youtubeplayerpro.com)

## 🙏 شكر خاص

شكر خاص لجميع المساهمين والمطورين الذين ساعدوا في تطوير هذا المشروع.

---

**صنع بـ ❤️ من أجل المجتمع العربي**
