{"version": 3, "file": "static/js/906.9d0acfa8.chunk.js", "mappings": "2LAKA,MAmSA,EAnSeA,IAAsB,IAArB,YAAEC,GAAaD,EAC7B,MAAOE,EAAeC,IAAoBC,EAAAA,EAAAA,WAAS,IAC5CC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,MACzCG,GAAYC,EAAAA,EAAAA,QAAO,MACnBC,GAAoBD,EAAAA,EAAAA,QAAO,OAE3B,aACJE,EAAY,gBACZC,EAAe,iBACfC,EAAgB,uBAChBC,EAAsB,SACtBC,IACEC,EAAAA,EAAAA,KAEEC,GAAqB,OAAXf,QAAW,IAAXA,OAAW,EAAXA,EAAae,UAAWN,EAAaO,IAErDC,EAAAA,EAAAA,WAAU,KACJF,GACFG,KAED,CAACH,IAEJ,MAAMG,EAAiBA,KACrB,GAAKC,OAAOC,GAUVC,QAVc,CACd,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAM,qCACbH,EAAOI,OAAQ,EACfH,SAASI,KAAKC,YAAYN,GAE1BH,OAAOU,wBAA0B,KAC/BR,IAEJ,GAKIA,EAAmBA,KACvB,GAAIf,EAAUwB,SAAWX,OAAOC,IAAMD,OAAOC,GAAGW,OAC9C,IACEvB,EAAkBsB,QAAU,IAAIX,OAAOC,GAAGW,OAAOzB,EAAUwB,QAAS,CAClEE,OAAQ,OACRC,MAAO,OACPlB,QAASA,EACTmB,WAAY,CACVC,SAAUtB,EAASsB,SAAW,EAAI,EAClCC,SAAUvB,EAASwB,aAAe,EAAI,EACtCC,UAAWzB,EAASwB,aAAe,EAAI,EACvCE,GAAI,EACJC,eAAgB,EAChBC,eAAgB,EAChBC,YAAa,EACbC,IAAK,EACLC,SAAU,EACVC,eAAgB,EAChBC,GAAI,KACJC,aAAc,KACdC,OAAQ7B,OAAO8B,SAASD,QAE1BE,OAAQ,CACNC,QAASC,EACTC,cAAeC,EACfC,QAASC,IAGf,CAAE,MAAOC,GACPC,QAAQD,MAAM,qCAAsCA,GACpDpD,EAAe,qIACjB,GAIE+C,EAAiBO,IACrBzD,GAAiB,GACjBG,EAAe,WAGSuD,IAApB/C,EAASgD,QACXF,EAAMG,OAAOC,UAAUlD,EAASgD,QAIlC,MAAMG,EAAYL,EAAMG,OAAOG,eAC/BvD,EAAgB,CACdM,GAAID,EACJmD,MAAOF,EAAUE,OAAS,sEAC1BC,QAASH,EAAUI,QAAU,gEAC7BC,SAAUV,EAAMG,OAAOQ,eAAiB,EACxCC,YAAa,EACbC,WAAW,EACXC,UAAU,EACVC,aAAa,EACbC,QAAShB,EAAMG,OAAOc,sBAAwB,OAC9Cf,OAAQF,EAAMG,OAAOe,aAAe,IACpCC,MAAOnB,EAAMG,OAAOiB,YAAa,EACjCC,YAAY,IAGdtB,QAAQuB,IAAI,kCAAmClE,IAG3CuC,EAAuBK,IAC3B,MAAMuB,EAAQvB,EAAMwB,KACdC,EAASzB,EAAMG,OAErB,OAAQoB,GACN,KAAK/D,OAAOC,GAAGiE,YAAYC,QACzB3E,EAAiB,CACf6D,WAAW,EACXC,UAAU,EACVC,aAAa,IAEf,MAEF,KAAKvD,OAAOC,GAAGiE,YAAYE,OACzB5E,EAAiB,CACf6D,WAAW,EACXC,UAAU,EACVC,aAAa,IAEf,MAEF,KAAKvD,OAAOC,GAAGiE,YAAYG,UACzB7E,EAAiB,CACf+D,aAAa,IAEf,MAEF,KAAKvD,OAAOC,GAAGiE,YAAYI,MACzB9E,EAAiB,CACf6D,WAAW,EACXC,UAAU,EACVC,aAAa,EACbH,YAAaa,EAAOd,gBAEtB1D,IACA,MAEF,KAAKO,OAAOC,GAAGiE,YAAYK,KACzB/E,EAAiB,CACf6D,WAAW,EACXC,UAAU,EACVC,aAAa,EACbH,YAAa,IAMfa,EAAOO,gBACThF,EAAiB,CACf4D,YAAaa,EAAOO,oBAKpBnC,EAAiBG,IACrB,MAAMiC,EAAYjC,EAAMwB,KACxB,IAAIU,EAAe,+HAEnB,OAAQD,GACN,KAAK,EACHC,EAAe,kHACf,MACF,KAAK,EACHA,EAAe,iEACf,MACF,KAAK,IACHA,EAAe,2IACf,MACF,KAAK,IACL,KAAK,IACHA,EAAe,kOAInBxF,EAAewF,GACfnC,QAAQD,MAAM,wBAAyBmC,EAAWC,IAcpD,OAAK9E,GAiBH+E,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,cACVC,QAAS,CAAEC,QAAS,GACpBC,QAAS,CAAED,QAAS,GACpBE,WAAY,CAAEhC,SAAU,IAAMiC,UAE9BC,EAAAA,EAAAA,MAAA,OAAKN,UAAU,mBAAkBK,SAAA,EAC/BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iBAAgBK,SAC5BlG,GACCmG,EAAAA,EAAAA,MAAA,OAAKN,UAAU,eAAcK,SAAA,EAC3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,aAAYK,UACzBR,EAAAA,EAAAA,KAAA,OAAK7D,MAAM,KAAKD,OAAO,KAAKwE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,+HAGZZ,EAAAA,EAAAA,KAAA,MAAAQ,SAAI,+GACJR,EAAAA,EAAAA,KAAA,KAAAQ,SAAIlG,KACJmG,EAAAA,EAAAA,MAAA,UAAQN,UAAU,eAAeU,QA7CzBC,KAClBvG,EAAe,MACfH,GAAiB,GACbM,EAAkBsB,SACpBtB,EAAkBsB,QAAQ+E,UAE5BC,WAAW,KACTzF,KACC,MAqC6DiF,SAAA,EACpDR,EAAAA,EAAAA,KAAA,OAAK7D,MAAM,KAAKD,OAAO,KAAKwE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,iNACJ,yFAKVH,EAAAA,EAAAA,MAAAQ,EAAAA,SAAA,CAAAT,SAAA,EACIrG,IACAsG,EAAAA,EAAAA,MAAA,OAAKN,UAAU,iBAAgBK,SAAA,EAC7BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,kBAAiBK,UAC9BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,eAEjBH,EAAAA,EAAAA,KAAA,KAAAQ,SAAG,8GAGPR,EAAAA,EAAAA,KAAA,OACEkB,IAAK1G,EACL2F,UAAW,mBAAmBhG,EAA2B,GAAX,iBAOrDQ,EAAayD,QACZqC,EAAAA,EAAAA,MAACR,EAAAA,EAAOC,IAAG,CACTC,UAAU,aACVC,QAAS,CAAEC,QAAS,EAAGc,EAAG,IAC1Bb,QAAS,CAAED,QAAS,EAAGc,EAAG,GAC1BZ,WAAY,CAAEa,MAAO,IAAMZ,SAAA,EAE3BR,EAAAA,EAAAA,KAAA,MAAIG,UAAU,cAAaK,SAAE7F,EAAayD,SAC1CqC,EAAAA,EAAAA,MAAA,OAAKN,UAAU,aAAYK,SAAA,EACzBR,EAAAA,EAAAA,KAAA,QAAMG,UAAU,gBAAeK,SAAE7F,EAAa0D,UAC7C1D,EAAa4D,SAAW,IACvBkC,EAAAA,EAAAA,MAAA,QAAMN,UAAU,iBAAgBK,SAAA,CAAC,mCACvBa,KAAKC,MAAM3G,EAAa4D,SAAW,IAAI,KAAG5D,EAAa4D,SAAW,IAAIgD,WAAWC,SAAS,EAAG,cAQ9GzG,EAAS0G,UACRhB,EAAAA,EAAAA,MAACR,EAAAA,EAAOC,IAAG,CACTC,UAAU,oBACVC,QAAS,CAAEC,QAAS,EAAGqB,MAAO,IAC9BpB,QAAS,CAAED,QAAS,EAAGqB,MAAO,GAC9BnB,WAAY,CAAEa,MAAO,IAAMZ,SAAA,EAE3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaK,UAC1BR,EAAAA,EAAAA,KAAA,OAAK7D,MAAM,KAAKD,OAAO,KAAKwE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,sGAGZZ,EAAAA,EAAAA,KAAA,QAAAQ,SAAM,gQA1FZR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaK,UAC1BC,EAAAA,EAAAA,MAAA,OAAKN,UAAU,eAAcK,SAAA,EAC3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,aAAYK,UACzBR,EAAAA,EAAAA,KAAA,OAAK7D,MAAM,KAAKD,OAAO,KAAKwE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,uBAGZZ,EAAAA,EAAAA,KAAA,MAAAQ,SAAI,qHACJR,EAAAA,EAAAA,KAAA,KAAAQ,SAAG,sP", "sources": ["components/pages/Player.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport './Player.css';\n\nconst Player = ({ routeParams }) => {\n  const [isPlayerReady, setIsPlayerReady] = useState(false);\n  const [playerError, setPlayerError] = useState(null);\n  const playerRef = useRef(null);\n  const playerInstanceRef = useRef(null);\n  \n  const { \n    currentVideo, \n    setCurrentVideo, \n    updateVideoState,\n    incrementVideosWatched,\n    settings \n  } = useAppStore();\n\n  const videoId = routeParams?.videoId || currentVideo.id;\n\n  useEffect(() => {\n    if (videoId) {\n      loadYouTubeAPI();\n    }\n  }, [videoId]);\n\n  const loadYouTubeAPI = () => {\n    if (!window.YT) {\n      const script = document.createElement('script');\n      script.src = 'https://www.youtube.com/iframe_api';\n      script.async = true;\n      document.body.appendChild(script);\n\n      window.onYouTubeIframeAPIReady = () => {\n        initializePlayer();\n      };\n    } else {\n      initializePlayer();\n    }\n  };\n\n  const initializePlayer = () => {\n    if (playerRef.current && window.YT && window.YT.Player) {\n      try {\n        playerInstanceRef.current = new window.YT.Player(playerRef.current, {\n          height: '100%',\n          width: '100%',\n          videoId: videoId,\n          playerVars: {\n            autoplay: settings.autoplay ? 1 : 0,\n            controls: settings.hideControls ? 0 : 1,\n            disablekb: settings.hideControls ? 1 : 0,\n            fs: 1,\n            iv_load_policy: 3,\n            modestbranding: 1,\n            playsinline: 1,\n            rel: 0,\n            showinfo: 0,\n            cc_load_policy: 0,\n            hl: 'ar',\n            cc_lang_pref: 'ar',\n            origin: window.location.origin\n          },\n          events: {\n            onReady: onPlayerReady,\n            onStateChange: onPlayerStateChange,\n            onError: onPlayerError\n          }\n        });\n      } catch (error) {\n        console.error('Error initializing YouTube player:', error);\n        setPlayerError('فشل في تحميل مشغل الفيديو');\n      }\n    }\n  };\n\n  const onPlayerReady = (event) => {\n    setIsPlayerReady(true);\n    setPlayerError(null);\n    \n    // Set initial volume\n    if (settings.volume !== undefined) {\n      event.target.setVolume(settings.volume);\n    }\n    \n    // Get video info\n    const videoData = event.target.getVideoData();\n    setCurrentVideo({\n      id: videoId,\n      title: videoData.title || 'فيديو يوتيوب',\n      channel: videoData.author || 'قناة يوتيوب',\n      duration: event.target.getDuration() || 0,\n      currentTime: 0,\n      isPlaying: false,\n      isPaused: true,\n      isBuffering: false,\n      quality: event.target.getPlaybackQuality() || 'auto',\n      volume: event.target.getVolume() || 100,\n      muted: event.target.isMuted() || false,\n      fullscreen: false\n    });\n\n    console.log('YouTube player ready for video:', videoId);\n  };\n\n  const onPlayerStateChange = (event) => {\n    const state = event.data;\n    const player = event.target;\n    \n    switch (state) {\n      case window.YT.PlayerState.PLAYING:\n        updateVideoState({\n          isPlaying: true,\n          isPaused: false,\n          isBuffering: false\n        });\n        break;\n        \n      case window.YT.PlayerState.PAUSED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false\n        });\n        break;\n        \n      case window.YT.PlayerState.BUFFERING:\n        updateVideoState({\n          isBuffering: true\n        });\n        break;\n        \n      case window.YT.PlayerState.ENDED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false,\n          currentTime: player.getDuration()\n        });\n        incrementVideosWatched();\n        break;\n        \n      case window.YT.PlayerState.CUED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false,\n          currentTime: 0\n        });\n        break;\n    }\n\n    // Update current time\n    if (player.getCurrentTime) {\n      updateVideoState({\n        currentTime: player.getCurrentTime()\n      });\n    }\n  };\n\n  const onPlayerError = (event) => {\n    const errorCode = event.data;\n    let errorMessage = 'حدث خطأ في تشغيل الفيديو';\n    \n    switch (errorCode) {\n      case 2:\n        errorMessage = 'معرف الفيديو غير صحيح';\n        break;\n      case 5:\n        errorMessage = 'خطأ في مشغل HTML5';\n        break;\n      case 100:\n        errorMessage = 'الفيديو غير موجود أو محذوف';\n        break;\n      case 101:\n      case 150:\n        errorMessage = 'صاحب الفيديو لا يسمح بتشغيله في هذا التطبيق';\n        break;\n    }\n    \n    setPlayerError(errorMessage);\n    console.error('YouTube player error:', errorCode, errorMessage);\n  };\n\n  const handleRetry = () => {\n    setPlayerError(null);\n    setIsPlayerReady(false);\n    if (playerInstanceRef.current) {\n      playerInstanceRef.current.destroy();\n    }\n    setTimeout(() => {\n      initializePlayer();\n    }, 1000);\n  };\n\n  if (!videoId) {\n    return (\n      <div className=\"player-page\">\n        <div className=\"player-empty\">\n          <div className=\"empty-icon\">\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M8 5v14l11-7z\"/>\n            </svg>\n          </div>\n          <h2>لا يوجد فيديو للتشغيل</h2>\n          <p>يرجى البحث عن فيديو أو اختيار فيديو من القائمة</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <motion.div \n      className=\"player-page\"\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <div className=\"player-container\">\n        <div className=\"player-wrapper\">\n          {playerError ? (\n            <div className=\"player-error\">\n              <div className=\"error-icon\">\n                <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                </svg>\n              </div>\n              <h3>خطأ في تشغيل الفيديو</h3>\n              <p>{playerError}</p>\n              <button className=\"retry-button\" onClick={handleRetry}>\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"/>\n                </svg>\n                إعادة المحاولة\n              </button>\n            </div>\n          ) : (\n            <>\n              {!isPlayerReady && (\n                <div className=\"player-loading\">\n                  <div className=\"loading-spinner\">\n                    <div className=\"spinner\"></div>\n                  </div>\n                  <p>جاري تحميل الفيديو...</p>\n                </div>\n              )}\n              <div \n                ref={playerRef}\n                className={`youtube-player ${!isPlayerReady ? 'hidden' : ''}`}\n              />\n            </>\n          )}\n        </div>\n\n        {/* Video Info */}\n        {currentVideo.title && (\n          <motion.div \n            className=\"video-info\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n          >\n            <h1 className=\"video-title\">{currentVideo.title}</h1>\n            <div className=\"video-meta\">\n              <span className=\"video-channel\">{currentVideo.channel}</span>\n              {currentVideo.duration > 0 && (\n                <span className=\"video-duration\">\n                  المدة: {Math.floor(currentVideo.duration / 60)}:{(currentVideo.duration % 60).toString().padStart(2, '0')}\n                </span>\n              )}\n            </div>\n          </motion.div>\n        )}\n\n        {/* Ad Blocker Status */}\n        {settings.adBlock && (\n          <motion.div \n            className=\"ad-blocker-status\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.5 }}\n          >\n            <div className=\"status-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <span>مانع الإعلانات مفعل - تجربة مشاهدة بدون إعلانات</span>\n          </motion.div>\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\nexport default Player;\n"], "names": ["_ref", "routeParams", "isPlayerReady", "setIsPlayerReady", "useState", "playerError", "setPlayerError", "playerRef", "useRef", "playerInstanceRef", "currentVideo", "setCurrentVideo", "updateVideoState", "incrementVideosWatched", "settings", "useAppStore", "videoId", "id", "useEffect", "loadYouTubeAPI", "window", "YT", "initializePlayer", "script", "document", "createElement", "src", "async", "body", "append<PERSON><PERSON><PERSON>", "onYouTubeIframeAPIReady", "current", "Player", "height", "width", "playerVars", "autoplay", "controls", "hideControls", "disablekb", "fs", "iv_load_policy", "modestbranding", "playsinline", "rel", "showinfo", "cc_load_policy", "hl", "cc_lang_pref", "origin", "location", "events", "onReady", "onPlayerReady", "onStateChange", "onPlayerStateChange", "onError", "onPlayerError", "error", "console", "event", "undefined", "volume", "target", "setVolume", "videoData", "getVideoData", "title", "channel", "author", "duration", "getDuration", "currentTime", "isPlaying", "isPaused", "isBuffering", "quality", "getPlaybackQuality", "getVolume", "muted", "isMuted", "fullscreen", "log", "state", "data", "player", "PlayerState", "PLAYING", "PAUSED", "BUFFERING", "ENDED", "CUED", "getCurrentTime", "errorCode", "errorMessage", "_jsx", "motion", "div", "className", "initial", "opacity", "animate", "transition", "children", "_jsxs", "viewBox", "fill", "d", "onClick", "handleRetry", "destroy", "setTimeout", "_Fragment", "ref", "y", "delay", "Math", "floor", "toString", "padStart", "adBlock", "scale"], "sourceRoot": ""}