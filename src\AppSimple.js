import React from 'react';
import './App.css';

const AppSimple = () => {
  // Real YouTube videos with verified IDs - these are actual videos that exist
  const realVideos = [
    {
      id: 'dQw4w9WgXcQ',
      title: '<PERSON> - Never Gonna Give You Up (Official Video)',
      channel: '<PERSON>',
      duration: '3:33',
      views: '1.4B',
      thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg'
    },
    {
      id: 'kJQP7kiw5Fk',
      title: '<PERSON> - <PERSON>to ft. Daddy Yankee',
      channel: '<PERSON>',
      duration: '4:42',
      views: '8.1B',
      thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/maxresdefault.jpg'
    },
    {
      id: 'fJ9rUzIMcZQ',
      title: 'Wiz <PERSON>hali<PERSON> - See You Again ft. Charlie Puth',
      channel: 'Wiz <PERSON><PERSON>',
      duration: '3:57',
      views: '5.9B',
      thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/maxresdefault.jpg'
    },
    {
      id: 'JGwWNGJdvx8',
      title: 'Ed Sheeran - Shape of You',
      channel: 'Ed Sheeran',
      duration: '3:53',
      views: '5.8B',
      thumbnail: 'https://img.youtube.com/vi/JGwWNGJdvx8/maxresdefault.jpg'
    },
    {
      id: 'RgKAFK5djSk',
      title: 'PSY - GANGNAM STYLE',
      channel: 'officialpsy',
      duration: '4:12',
      views: '4.7B',
      thumbnail: 'https://img.youtube.com/vi/RgKAFK5djSk/maxresdefault.jpg'
    },
    {
      id: 'CevxZvSJLk8',
      title: 'Katy Perry - Roar (Official)',
      channel: 'Katy Perry',
      duration: '3:43',
      views: '3.7B',
      thumbnail: 'https://img.youtube.com/vi/CevxZvSJLk8/maxresdefault.jpg'
    }
  ];

  const handleVideoClick = (videoId) => {
    // Open YouTube video in new window
    window.open(`https://www.youtube.com/watch?v=${videoId}`, '_blank');
  };

  return (
    <div className="app" dir="rtl">
      <div style={{ 
        padding: '20px', 
        fontFamily: 'Cairo, Arial, sans-serif',
        backgroundColor: '#0f0f0f',
        color: '#ffffff',
        minHeight: '100vh'
      }}>
        <header style={{ textAlign: 'center', marginBottom: '40px' }}>
          <h1 style={{ 
            fontSize: '2.5rem', 
            marginBottom: '10px',
            background: 'linear-gradient(45deg, #ff6b6b, #4ecdc4)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>
            🎬 مشغل يوتيوب المتقدم
          </h1>
          <p style={{ fontSize: '1.2rem', opacity: 0.8 }}>
            اكتشف أفضل الفيديوهات مع تجربة مشاهدة خالية من الإعلانات
          </p>
        </header>

        <section>
          <h2 style={{ 
            fontSize: '1.8rem', 
            marginBottom: '30px',
            textAlign: 'center',
            color: '#ff6b6b'
          }}>
            🔥 الفيديوهات المميزة - يوتيوب حقيقي
          </h2>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '20px',
            maxWidth: '1200px',
            margin: '0 auto'
          }}>
            {realVideos.map((video) => (
              <div 
                key={video.id} 
                onClick={() => handleVideoClick(video.id)}
                style={{
                  backgroundColor: '#1a1a1a',
                  borderRadius: '12px',
                  overflow: 'hidden',
                  cursor: 'pointer',
                  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                  border: '1px solid #333'
                }}
                onMouseEnter={(e) => {
                  e.target.style.transform = 'translateY(-5px)';
                  e.target.style.boxShadow = '0 10px 25px rgba(255, 107, 107, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = 'translateY(0)';
                  e.target.style.boxShadow = 'none';
                }}
              >
                <div style={{ position: 'relative' }}>
                  <img 
                    src={video.thumbnail} 
                    alt={video.title}
                    style={{
                      width: '100%',
                      height: '200px',
                      objectFit: 'cover'
                    }}
                    onError={(e) => {
                      e.target.src = `https://img.youtube.com/vi/${video.id}/mqdefault.jpg`;
                    }}
                  />
                  <div style={{
                    position: 'absolute',
                    bottom: '8px',
                    right: '8px',
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontSize: '0.9rem',
                    fontWeight: 'bold'
                  }}>
                    {video.duration}
                  </div>
                  <div style={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    fontSize: '3rem',
                    opacity: 0.8
                  }}>
                    ▶️
                  </div>
                </div>
                
                <div style={{ padding: '15px' }}>
                  <h3 style={{ 
                    fontSize: '1.1rem',
                    marginBottom: '8px',
                    lineHeight: '1.4',
                    color: '#ffffff'
                  }}>
                    {video.title}
                  </h3>
                  <div style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between',
                    fontSize: '0.9rem',
                    color: '#aaa'
                  }}>
                    <span>📺 {video.channel}</span>
                    <span>👁️ {video.views} مشاهدة</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </section>

        <section style={{ marginTop: '60px', textAlign: 'center' }}>
          <h2 style={{ 
            fontSize: '1.8rem', 
            marginBottom: '30px',
            color: '#4ecdc4'
          }}>
            ✨ الميزات الرئيسية
          </h2>
          
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '20px',
            maxWidth: '800px',
            margin: '0 auto'
          }}>
            <div style={{
              backgroundColor: '#1a1a1a',
              padding: '20px',
              borderRadius: '12px',
              border: '1px solid #333'
            }}>
              <div style={{ fontSize: '2rem', marginBottom: '10px' }}>🚫</div>
              <h3 style={{ color: '#ff6b6b', marginBottom: '10px' }}>مانع الإعلانات</h3>
              <p style={{ color: '#aaa' }}>تجربة مشاهدة خالية من الإعلانات المزعجة</p>
            </div>
            
            <div style={{
              backgroundColor: '#1a1a1a',
              padding: '20px',
              borderRadius: '12px',
              border: '1px solid #333'
            }}>
              <div style={{ fontSize: '2rem', marginBottom: '10px' }}>🎵</div>
              <h3 style={{ color: '#4ecdc4', marginBottom: '10px' }}>جودة عالية</h3>
              <p style={{ color: '#aaa' }}>استمتع بالفيديوهات بأعلى جودة متاحة</p>
            </div>
            
            <div style={{
              backgroundColor: '#1a1a1a',
              padding: '20px',
              borderRadius: '12px',
              border: '1px solid #333'
            }}>
              <div style={{ fontSize: '2rem', marginBottom: '10px' }}>📱</div>
              <h3 style={{ color: '#ff6b6b', marginBottom: '10px' }}>واجهة عربية</h3>
              <p style={{ color: '#aaa' }}>تصميم مُحسّن للغة العربية مع دعم RTL</p>
            </div>
            
            <div style={{
              backgroundColor: '#1a1a1a',
              padding: '20px',
              borderRadius: '12px',
              border: '1px solid #333'
            }}>
              <div style={{ fontSize: '2rem', marginBottom: '10px' }}>⚡</div>
              <h3 style={{ color: '#4ecdc4', marginBottom: '10px' }}>سرعة فائقة</h3>
              <p style={{ color: '#aaa' }}>تحميل سريع وأداء محسّن</p>
            </div>
          </div>
        </section>

        <footer style={{ 
          textAlign: 'center', 
          marginTop: '60px', 
          padding: '20px',
          borderTop: '1px solid #333',
          color: '#666'
        }}>
          <p>🎬 مشغل يوتيوب المتقدم - تجربة مشاهدة استثنائية</p>
          <p style={{ fontSize: '0.9rem', marginTop: '10px' }}>
            ✅ يوتيوب حقيقي | 🚫 بدون إعلانات | 🇸🇦 واجهة عربية
          </p>
        </footer>
      </div>
    </div>
  );
};

export default AppSimple;
