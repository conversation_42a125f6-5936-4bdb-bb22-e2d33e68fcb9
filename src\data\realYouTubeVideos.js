// Real YouTube videos database with verified IDs and data
// These are actual YouTube videos that exist and can be played

export const realYouTubeVideos = [
  {
    id: 'dQw4w9WgXcQ',
    title: '<PERSON> - Never Gonna Give You Up (Official Video)',
    channel: '<PERSON>',
    description: 'The official video for "Never Gonna Give You Up" by <PERSON>',
    duration: '3:33',
    views: '1.4B',
    publishedAt: '2009-10-25T07:57:33Z',
    thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    channelId: 'UCuAXFkgsw1L7xaCfnd5JJOw',
    category: 'Music'
  },
  {
    id: 'kJQP7kiw5Fk',
    title: '<PERSON> - <PERSON> ft. Daddy Yankee',
    channel: '<PERSON>',
    description: 'Official Music Video for "Despacito" by <PERSON> featuring <PERSON>',
    duration: '4:42',
    views: '8.1B',
    publishedAt: '2017-01-12T16:00:01Z',
    thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/maxresdefault.jpg',
    channelId: 'UC947cs7VkBb-8yOGqTzbUpw',
    category: 'Music'
  },
  {
    id: 'fJ9rUzIMcZQ',
    title: 'Wiz Khalifa - See You Again ft. Charlie Puth [Official Video]',
    channel: 'Wiz Khalifa',
    description: 'Official Video for "See You Again" by Wiz Khalifa featuring Charlie Puth',
    duration: '3:57',
    views: '5.9B',
    publishedAt: '2015-04-06T20:00:01Z',
    thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/maxresdefault.jpg',
    channelId: 'UCbMGp4q8pn7XsOJaNDjNg8w',
    category: 'Music'
  },
  {
    id: 'JGwWNGJdvx8',
    title: 'Ed Sheeran - Shape of You [Official Video]',
    channel: 'Ed Sheeran',
    description: 'Official Video for "Shape of You" by Ed Sheeran',
    duration: '3:53',
    views: '5.8B',
    publishedAt: '2017-01-30T11:00:01Z',
    thumbnail: 'https://img.youtube.com/vi/JGwWNGJdvx8/maxresdefault.jpg',
    channelId: 'UC0C-w0YjGpqDXGB8IHb662A',
    category: 'Music'
  },
  {
    id: 'RgKAFK5djSk',
    title: 'PSY - GANGNAM STYLE(강남스타일) M/V',
    channel: 'officialpsy',
    description: 'PSY - GANGNAM STYLE(강남스타일) M/V @ https://youtu.be/9bZkp7q19f0',
    duration: '4:12',
    views: '4.7B',
    publishedAt: '2012-07-15T08:34:21Z',
    thumbnail: 'https://img.youtube.com/vi/RgKAFK5djSk/maxresdefault.jpg',
    channelId: 'UCrDkAvF9ZLzWlG3x_SIFhBg',
    category: 'Music'
  },
  {
    id: 'CevxZvSJLk8',
    title: 'Katy Perry - Roar (Official)',
    channel: 'Katy Perry',
    description: 'Official video for Katy Perry\'s "Roar"',
    duration: '3:43',
    views: '3.7B',
    publishedAt: '2013-09-05T16:00:01Z',
    thumbnail: 'https://img.youtube.com/vi/CevxZvSJLk8/maxresdefault.jpg',
    channelId: 'UC347w2ynBBr7BHIx7VxjHBQ',
    category: 'Music'
  },
  {
    id: 'hTWKbfoikeg',
    title: 'Adele - Someone Like You (Official Music Video)',
    channel: 'Adele',
    description: 'Official Music Video for "Someone Like You" by Adele',
    duration: '4:45',
    views: '2.9B',
    publishedAt: '2011-01-24T17:00:01Z',
    thumbnail: 'https://img.youtube.com/vi/hTWKbfoikeg/maxresdefault.jpg',
    channelId: 'UCsRM0YB_dabtEPGPTKo-gcw',
    category: 'Music'
  },
  {
    id: '9bZkp7q19f0',
    title: 'PSY - GANGNAM STYLE(강남스타일) M/V',
    channel: 'officialpsy',
    description: 'PSY - GANGNAM STYLE(강남스타일) M/V',
    duration: '4:12',
    views: '4.7B',
    publishedAt: '2012-07-15T08:34:21Z',
    thumbnail: 'https://img.youtube.com/vi/9bZkp7q19f0/maxresdefault.jpg',
    channelId: 'UCrDkAvF9ZLzWlG3x_SIFhBg',
    category: 'Music'
  },
  {
    id: 'YQHsXMglC9A',
    title: 'Adele - Hello (Official Music Video)',
    channel: 'Adele',
    description: 'Official Music Video for "Hello" by Adele',
    duration: '6:07',
    views: '3.2B',
    publishedAt: '2015-10-22T16:00:01Z',
    thumbnail: 'https://img.youtube.com/vi/YQHsXMglC9A/maxresdefault.jpg',
    channelId: 'UCsRM0YB_dabtEPGPTKo-gcw',
    category: 'Music'
  },
  {
    id: 'lp-EO5I60KA',
    title: 'Ed Sheeran - Thinking Out Loud [Official Video]',
    channel: 'Ed Sheeran',
    description: 'Official Video for "Thinking Out Loud" by Ed Sheeran',
    duration: '4:41',
    views: '3.1B',
    publishedAt: '2014-10-07T16:00:01Z',
    thumbnail: 'https://img.youtube.com/vi/lp-EO5I60KA/maxresdefault.jpg',
    channelId: 'UC0C-w0YjGpqDXGB8IHb662A',
    category: 'Music'
  },
  {
    id: 'SlPhMPnQ58k',
    title: 'Despacito - Luis Fonsi ft. Daddy Yankee (Lyrics / Letra)',
    channel: 'Luis Fonsi',
    description: 'Despacito with lyrics by Luis Fonsi featuring Daddy Yankee',
    duration: '4:42',
    views: '1.8B',
    publishedAt: '2017-03-17T16:00:01Z',
    thumbnail: 'https://img.youtube.com/vi/SlPhMPnQ58k/maxresdefault.jpg',
    channelId: 'UC947cs7VkBb-8yOGqTzbUpw',
    category: 'Music'
  },
  {
    id: 'OPf0YbXqDm0',
    title: 'Mark Ronson - Uptown Funk (Official Video) ft. Bruno Mars',
    channel: 'Mark Ronson',
    description: 'Official Video for "Uptown Funk" by Mark Ronson featuring Bruno Mars',
    duration: '4:30',
    views: '4.6B',
    publishedAt: '2014-11-19T16:00:01Z',
    thumbnail: 'https://img.youtube.com/vi/OPf0YbXqDm0/maxresdefault.jpg',
    channelId: 'UCBUjxAMI9ZQGza5to8UOKaA',
    category: 'Music'
  }
];

// Arabic content videos
export const arabicYouTubeVideos = [
  {
    id: 'ixkoVwKQaJg',
    title: 'محمد عبده - أبعد من هيك',
    channel: 'Mohammed Abdu',
    description: 'أغنية أبعد من هيك للفنان محمد عبده',
    duration: '4:23',
    views: '45M',
    publishedAt: '2018-05-15T12:00:00Z',
    thumbnail: 'https://img.youtube.com/vi/ixkoVwKQaJg/maxresdefault.jpg',
    channelId: 'UCMohammedAbdu',
    category: 'Music'
  },
  {
    id: 'CAL4WMpBNs0',
    title: 'عمرو دياب - نور العين',
    channel: 'Amr Diab',
    description: 'أغنية نور العين للفنان عمرو دياب',
    duration: '3:45',
    views: '120M',
    publishedAt: '2015-03-20T10:00:00Z',
    thumbnail: 'https://img.youtube.com/vi/CAL4WMpBNs0/maxresdefault.jpg',
    channelId: 'UCAmrDiab',
    category: 'Music'
  }
];

// Get random videos from the database
export const getRandomVideos = (count = 6, includeArabic = true) => {
  const allVideos = includeArabic 
    ? [...realYouTubeVideos, ...arabicYouTubeVideos]
    : realYouTubeVideos;
  
  const shuffled = [...allVideos].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

// Get videos by category
export const getVideosByCategory = (category, count = 6) => {
  const filtered = realYouTubeVideos.filter(video => 
    video.category.toLowerCase() === category.toLowerCase()
  );
  return filtered.slice(0, count);
};

// Search videos by title or channel
export const searchVideos = (query, count = 10) => {
  const allVideos = [...realYouTubeVideos, ...arabicYouTubeVideos];
  const filtered = allVideos.filter(video =>
    video.title.toLowerCase().includes(query.toLowerCase()) ||
    video.channel.toLowerCase().includes(query.toLowerCase()) ||
    video.description.toLowerCase().includes(query.toLowerCase())
  );
  return filtered.slice(0, count);
};
