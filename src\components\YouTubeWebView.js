import React, { useRef, useEffect, useState } from 'react';

const YouTubeWebView = ({ searchQuery, onVideoChange, settings }) => {
  const webviewRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentUrl, setCurrentUrl] = useState('https://www.youtube.com');
  const [canGoBack, setCanGoBack] = useState(false);
  const [canGoForward, setCanGoForward] = useState(false);

  useEffect(() => {
    const webview = webviewRef.current;
    if (!webview) return;

    // معالجة تحميل الصفحة
    const handleLoadStop = () => {
      setIsLoading(false);
      console.log('✅ تم تحميل يوتيوب بنجاح');

      // تحديث حالة التنقل
      updateNavigationState();

      // تطبيق مانع الإعلانات
      setTimeout(() => {
        injectAdBlockerScript();
        injectCustomStyles();
      }, 2000);
    };

    const handleLoadStart = () => {
      setIsLoading(true);
      console.log('🔄 جاري تحميل يوتيوب...');
    };

    const handleNavigate = (e) => {
      setCurrentUrl(e.url);
      updateNavigationState();
      console.log('🔗 تم التنقل إلى:', e.url);
    };

    // إضافة المستمعين
    webview.addEventListener('did-stop-loading', handleLoadStop);
    webview.addEventListener('did-start-loading', handleLoadStart);
    webview.addEventListener('did-navigate', handleNavigate);
    webview.addEventListener('did-navigate-in-page', handleNavigate);

    return () => {
      webview.removeEventListener('did-stop-loading', handleLoadStop);
      webview.removeEventListener('did-start-loading', handleLoadStart);
      webview.removeEventListener('did-navigate', handleNavigate);
      webview.removeEventListener('did-navigate-in-page', handleNavigate);
    };
  }, []);

  // تحديث حالة التنقل
  const updateNavigationState = () => {
    const webview = webviewRef.current;
    if (webview) {
      setCanGoBack(webview.canGoBack());
      setCanGoForward(webview.canGoForward());
    }
  };

  // حقن سكريبت مانع الإعلانات
  const injectAdBlockerScript = () => {
    const webview = webviewRef.current;
    if (!webview) return;

    const adBlockScript = `
      // مانع إعلانات YouTube متقدم
      (function() {
        console.log('🛡️ تم تفعيل مانع الإعلانات');
        
        // إخفاء الإعلانات الموجودة
        function hideAds() {
          const adSelectors = [
            '.ytd-display-ad-renderer',
            '.ytd-promoted-sparkles-web-renderer',
            '.ytd-ad-slot-renderer',
            '.ytp-ad-module',
            '.ytp-ad-overlay-container',
            '.ytp-ad-text-overlay',
            '.video-ads',
            '.masthead-ad-control',
            '#player-ads',
            '.ad-container',
            '.ytd-banner-promo-renderer',
            '.ytd-video-masthead-ad-v3-renderer',
            '.ytd-primetime-promo-renderer'
          ];
          
          adSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => {
              el.style.display = 'none';
              el.style.visibility = 'hidden';
              el.style.opacity = '0';
              el.style.height = '0';
              el.style.width = '0';
            });
          });
        }
        
        // تشغيل مانع الإعلانات كل ثانية
        setInterval(hideAds, 1000);
        
        // تشغيل فوري
        hideAds();
        
        // مراقبة تغييرات DOM
        const observer = new MutationObserver(hideAds);
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
        
        // تخطي الإعلانات تلقائياً
        function skipAds() {
          const skipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
          if (skipButton) {
            skipButton.click();
            console.log('⏭️ تم تخطي الإعلان تلقائياً');
          }
        }
        
        setInterval(skipAds, 500);
      })();
    `;

    webview.executeJavaScript(adBlockScript);
    console.log('🛡️ تم حقن مانع الإعلانات في webview');
  };

  // حقن الأنماط المخصصة
  const injectCustomStyles = () => {
    const webview = webviewRef.current;
    if (!webview) return;

    const customCSS = `
      /* تحسين مظهر YouTube */
      #masthead {
        background: #1a1a1a !important;
      }
      
      /* إخفاء العناصر غير المرغوبة */
      .ytd-guide-renderer,
      #guide-button,
      #voice-search-button {
        display: none !important;
      }
      
      /* تحسين التخطيط */
      #primary {
        margin: 0 auto !important;
      }
      
      /* ثيم داكن محسن */
      body {
        background: #0f0f0f !important;
      }
    `;

    webview.insertCSS(customCSS);
    console.log('🎨 تم حقن الأنماط المخصصة في webview');
  };

  // البحث في YouTube
  useEffect(() => {
    if (searchQuery && webviewRef.current) {
      const searchUrl = `https://www.youtube.com/results?search_query=${encodeURIComponent(searchQuery)}`;
      webviewRef.current.loadURL(searchUrl);
      console.log('🔍 البحث عن:', searchQuery);
    }
  }, [searchQuery]);

  // التنقل
  const goBack = () => {
    const webview = webviewRef.current;
    if (webview && canGoBack) {
      webview.goBack();
      console.log('⬅️ العودة للخلف');
    }
  };

  const goForward = () => {
    const webview = webviewRef.current;
    if (webview && canGoForward) {
      webview.goForward();
      console.log('➡️ التقدم للأمام');
    }
  };

  const reload = () => {
    const webview = webviewRef.current;
    if (webview) {
      webview.reload();
      console.log('🔄 إعادة تحميل الصفحة');
    }
  };

  const goHome = () => {
    const webview = webviewRef.current;
    if (webview) {
      webview.loadURL('https://www.youtube.com');
      console.log('🏠 العودة للصفحة الرئيسية');
    }
  };

  return (
    <div className="youtube-webview-container">
      {/* شريط التنقل */}
      <div className="webview-navigation">
        <button 
          onClick={goBack} 
          disabled={!canGoBack}
          className="nav-btn"
          title="السابق"
        >
          ←
        </button>
        
        <button 
          onClick={goForward} 
          disabled={!canGoForward}
          className="nav-btn"
          title="التالي"
        >
          →
        </button>
        
        <button 
          onClick={reload}
          className="nav-btn"
          title="إعادة تحميل"
        >
          ↻
        </button>
        
        <button 
          onClick={goHome}
          className="nav-btn"
          title="الصفحة الرئيسية"
        >
          🏠
        </button>
        
        <div className="url-display">
          {currentUrl}
        </div>
        
        {isLoading && (
          <div className="loading-indicator">
            جاري التحميل...
          </div>
        )}
      </div>

      {/* YouTube WebView */}
      <webview
        ref={webviewRef}
        src="https://www.youtube.com"
        className="youtube-webview"
        partition="persist:youtube"
        allowpopups="true"
        webpreferences="allowRunningInsecureContent=yes,nodeIntegration=no,contextIsolation=yes"
      />
    </div>
  );
};

export default YouTubeWebView;
