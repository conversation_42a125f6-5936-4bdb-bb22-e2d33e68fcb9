{"version": 3, "file": "static/js/781.e385b7d4.chunk.js", "mappings": "wIAGO,MAAMA,EAAoB,CAC/B,CACEC,GAAI,cACJC,MAAO,yDACPC,QAAS,cACTC,YAAa,kEACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,0CACPC,QAAS,aACTC,YAAa,4EACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,gEACPC,QAAS,cACTC,YAAa,2EACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,6CACPC,QAAS,aACTC,YAAa,kDACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,0DACPC,QAAS,cACTC,YAAa,yFACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,+BACPC,QAAS,aACTC,YAAa,0CACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,kDACPC,QAAS,QACTC,YAAa,uDACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,0DACPC,QAAS,cACTC,YAAa,0DACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,uCACPC,QAAS,QACTC,YAAa,4CACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,kDACPC,QAAS,aACTC,YAAa,uDACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,2DACPC,QAAS,aACTC,YAAa,6DACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,4DACPC,QAAS,cACTC,YAAa,uEACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,2BACXC,SAAU,UAKDC,EAAsB,CACjC,CACEV,GAAI,cACJC,MAAO,+GACPC,QAAS,gBACTC,YAAa,iLACbC,SAAU,OACVC,MAAO,MACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,iBACXC,SAAU,SAEZ,CACET,GAAI,cACJC,MAAO,wGACPC,QAAS,WACTC,YAAa,0KACbC,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbC,UAAW,2DACXC,UAAW,YACXC,SAAU,UCkGd,MACA,EADmB,IA5QnB,MACEE,WAAAA,GAEEC,KAAKC,OAASC,CAAAA,SAAAA,aAAAA,WAAAA,IAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,0BAC1BH,KAAKI,QAAU,wCAGfJ,KAAKK,iBAAkB,EACvBL,KAAKM,WAAanB,EAClBa,KAAKO,aAAeT,CACtB,CAGA,kBAAMU,CAAaC,GAAyC,IAAlCC,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAIG,EAASH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACrD,IAEE,aAAaX,KAAKe,4BAA4BN,EAAOC,EAAYI,EAEnE,CAAE,MAAOE,GAGP,OAFAC,QAAQD,MAAM,wBAAyBA,GAEhChB,KAAKkB,qBAAqBT,EACnC,CACF,CAGA,qBAAMU,CAAgBC,GACpB,IAEE,aAAapB,KAAKqB,2BAA2BD,EAE/C,CAAE,MAAOJ,GAEP,OADAC,QAAQD,MAAM,+BAAgCA,GACvChB,KAAKsB,oBAAoBF,EAClC,CACF,CAGA,iCAAML,CAA4BN,EAAOC,EAAYI,GACnD,IACE,MAAMS,EAAY,IAAIvB,KAAKM,cAAeN,KAAKO,cAGzCiB,EAAgBD,EAAUE,OAAOC,GACrCA,EAAMrC,MAAMsC,cAAcC,SAASnB,EAAMkB,gBACzCD,EAAMpC,QAAQqC,cAAcC,SAASnB,EAAMkB,gBAC3CD,EAAMnC,YAAYoC,cAAcC,SAASnB,EAAMkB,gBAI3CE,EAAeL,EAAcZ,OAAS,EACxCY,EAAcM,MAAM,EAAGpB,GACvBa,EAAUO,MAAM,EAAGpB,GAEvB,MAAO,CACLqB,OAAQF,EACRG,cAAelB,EAAY,KAAO,kBAClCmB,aAAcJ,EAAajB,OAE/B,CAAE,MAAOI,GAEP,OADAC,QAAQD,MAAM,4BAA6BA,GACpChB,KAAKkB,qBAAqBT,EACnC,CACF,CAKA,gCAAMY,CAA2BD,GAC/B,IAEE,MACMc,EADY,IAAIlC,KAAKM,cAAeN,KAAKO,cAClB4B,KAAKT,GAASA,EAAMtC,KAAOgC,GAExD,OAAIc,GAKGlC,KAAKsB,oBAAoBF,EAClC,CAAE,MAAOJ,GAEP,OADAC,QAAQD,MAAM,mCAAoCA,GAC3ChB,KAAKsB,oBAAoBF,EAClC,CACF,CAGAgB,sBAAAA,CAAuBC,GAAO,IAADC,EAC3B,IAAKD,EAAKE,MACR,MAAO,CAAER,OAAQ,GAAIC,cAAe,KAAMC,aAAc,GAe1D,MAAO,CACLF,OAbaM,EAAKE,MAAMC,IAAIC,IAAI,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,MAAK,CACrCzD,GAAIqD,EAAKrD,GAAGgC,SAAWqB,EAAKrD,GAC5BC,MAAOoD,EAAKK,QAAQzD,MACpBC,QAASmD,EAAKK,QAAQC,aACtBxD,YAAakD,EAAKK,QAAQvD,YAC1BI,WAAyC,QAA9B+C,EAAAD,EAAKK,QAAQE,WAAWC,cAAM,IAAAP,OAAA,EAA9BA,EAAgCQ,OAAsC,QAAnCP,EAAIF,EAAKK,QAAQE,WAAWG,eAAO,IAAAR,OAAA,EAA/BA,EAAiCO,KACnFxD,YAAa+C,EAAKK,QAAQpD,YAC1BF,SAAUQ,KAAKoD,cAAiC,QAApBR,EAACH,EAAKY,sBAAc,IAAAT,OAAA,EAAnBA,EAAqBpD,UAClDC,MAAOO,KAAKsD,YAA2B,QAAhBT,EAACJ,EAAKc,kBAAU,IAAAV,OAAA,EAAfA,EAAiBW,WACzC5D,UAAW6C,EAAKK,QAAQlD,aAKxBoC,cAAeK,EAAKL,eAAiB,KACrCC,cAA2B,QAAbK,EAAAD,EAAKoB,gBAAQ,IAAAnB,OAAA,EAAbA,EAAeL,eAAgB,EAEjD,CAGAyB,qBAAAA,CAAsBjB,GAAO,IAADkB,EAAAC,EAC1B,MAAO,CACLxE,GAAIqD,EAAKrD,GACTC,MAAOoD,EAAKK,QAAQzD,MACpBC,QAASmD,EAAKK,QAAQC,aACtBxD,YAAakD,EAAKK,QAAQvD,YAC1BI,WAAyC,QAA9BgE,EAAAlB,EAAKK,QAAQE,WAAWa,cAAM,IAAAF,OAAA,EAA9BA,EAAgCT,OAAmC,QAAhCU,EAAInB,EAAKK,QAAQE,WAAWc,YAAI,IAAAF,OAAA,EAA5BA,EAA8BV,KAChFxD,YAAa+C,EAAKK,QAAQpD,YAC1BF,SAAUQ,KAAKoD,cAAcX,EAAKY,eAAe7D,UACjDC,MAAOO,KAAKsD,YAAYb,EAAKc,WAAWC,WACxCO,MAAO/D,KAAKsD,YAAYb,EAAKc,WAAWS,WACxCpE,UAAW6C,EAAKK,QAAQlD,UACxBqE,KAAMxB,EAAKK,QAAQmB,MAAQ,GAE/B,CAGAb,aAAAA,CAAc5D,GACZ,IAAKA,EAAU,OAAO,EAEtB,MAAM0E,EAAQ1E,EAAS0E,MAAM,2BAC7B,IAAKA,EAAO,OAAO,EAMnB,OAAe,MAJDC,SAASD,EAAM,KAAO,GAIJ,IAHhBC,SAASD,EAAM,KAAO,IACtBC,SAASD,EAAM,KAAO,EAGxC,CAGAZ,WAAAA,CAAYE,GACV,IAAKA,EAAW,MAAO,IAEvB,MAAMY,EAAQD,SAASX,GACvB,OAAIY,GAAS,IACJ,IAAIA,EAAQ,KAASC,QAAQ,MAC3BD,GAAS,IACX,IAAIA,EAAQ,KAAMC,QAAQ,MAE5BD,EAAME,UACf,CAGAC,cAAAA,CAAeC,GACb,IAAKA,EAAS,MAAO,OAErB,MAAMC,EAAQC,KAAKC,MAAMH,EAAU,MAC7BI,EAAUF,KAAKC,MAAOH,EAAU,KAAQ,IACxCK,EAAOL,EAAU,GAEvB,OAAIC,EAAQ,EACH,GAAGA,KAASG,EAAQN,WAAWQ,SAAS,EAAG,QAAQD,EAAKP,WAAWQ,SAAS,EAAG,OAEjF,GAAGF,KAAWC,EAAKP,WAAWQ,SAAS,EAAG,MACnD,CAKA5D,oBAAAA,CAAqBT,GACnB,MAAMsE,EAAa,CACjB,CACE3F,GAAI,cACJC,MAAO,GAAGoB,yGACVnB,QAAS,sEACTC,YAAa,sMACbI,UAAW,uDACXH,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbE,UAAW,eAEb,CACER,GAAI,cACJC,MAAO,sBAAOoB,qDACdnB,QAAS,sEACTC,YAAa,yLACbI,UAAW,uDACXH,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbE,UAAW,eAEb,CACER,GAAI,cACJC,MAAO,4BAAQoB,4DACfnB,QAAS,kFACTC,YAAa,oMACbI,UAAW,uDACXH,SAAU,OACVC,MAAO,OACPC,YAAa,uBACbE,UAAW,gBAIf,MAAO,CACLmC,OAAQgD,EACR/C,cAAe,KACfC,aAAc8C,EAAWnE,OAE7B,CAGAU,mBAAAA,CAAoBF,GAClB,MAAO,CACLhC,GAAIgC,EACJ/B,MAAO,2GACPC,QAAS,sEACTC,YAAa,wGACbI,UAAW,8BAA8ByB,sBACzC5B,SAAU,OACVC,MAAO,OACPsE,MAAO,MACPrE,YAAa,uBACbE,UAAW,cACXqE,KAAM,CAAC,uCAAU,iCAAS,wCAE9B,CAGA,uBAAMe,GAAuD,IAAlBtE,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACtD,IAEE,aAAaX,KAAKiF,uBAAuBvE,EAE3C,CAAE,MAAOM,GAEP,OADAC,QAAQD,MAAM,0BAA2BA,GAClChB,KAAKkB,qBAAqB,4EACnC,CACF,CAGA,4BAAM+D,GAAwC,IAAjBvE,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,EACxC,IAEE,MAAMY,EAAY,IAAIvB,KAAKM,cAAeN,KAAKO,cAEzC2E,EADW,IAAI3D,GAAW4D,KAAK,IAAM,GAAMT,KAAKU,UACtBtD,MAAM,EAAGpB,GAEzC,MAAO,CACLqB,OAAQmD,EACRlD,cAAe,KACfC,aAAciD,EAAetE,OAEjC,CAAE,MAAOI,GAEP,OADAC,QAAQD,MAAM,8BAA+BA,GACtChB,KAAKkB,qBAAqB,4EACnC,CACF,E,uHCjQF,MAgNA,EAhNemE,IAA2B,IAA1B,YAAEC,EAAc,CAAC,GAAGD,EAClC,MAAO7D,EAAe+D,IAAoBC,EAAAA,EAAAA,UAAS,KAC5CC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCxE,EAAO2E,IAAYH,EAAAA,EAAAA,UAAS,OAGjCI,QAAQ,MAAEnF,EAAK,QAAEoF,GAAS,eAC1BC,EAAc,mBACdC,EACAR,iBAAkBS,IAChBC,EAAAA,EAAAA,MAEE,SAAEC,IAAaC,EAAAA,EAAAA,MAEfC,GAA0B,OAAXd,QAAW,IAAXA,OAAW,EAAXA,EAAa7E,QAASA,GAAS,IAEpD4F,EAAAA,EAAAA,WAAU,KACJD,IACFN,EAAeM,GACfE,EAAcF,KAEf,CAACA,IAEJ,MAAME,EAAgBC,UACpB,GAAKC,EAAYC,OAAjB,CAEAf,GAAa,GACbC,EAAS,MAET,IAEE,MAAMe,QAAgBC,EAAAA,EAAWnG,aAAagG,EAAa,IAC3DjB,EAAiBmB,EAAQ3E,QACzBiE,EAAsBU,EAAQ3E,QAC9BgE,EAAmBS,EACrB,CAAE,MAAOI,GACPjB,EAAS,0LACT1E,QAAQD,MAAM,gBAAiB4F,EACjC,CAAC,QACClB,GAAa,EACf,CAhB+B,GAgC3BmB,EAAcC,IAClB,MAAMC,EAAO,IAAIC,KAAKF,GAChBG,EAAM,IAAID,KACVE,EAAWxC,KAAKyC,IAAIF,EAAMF,GAC1BK,EAAW1C,KAAK2C,KAAKH,EAAQ,OAEnC,OAAiB,IAAbE,EAAuB,qBACvBA,EAAW,EAAU,sBAAOA,6BAC5BA,EAAW,GAAW,sBAAO1C,KAAKC,MAAMyC,EAAW,0CACnDA,EAAW,IAAY,sBAAO1C,KAAKC,MAAMyC,EAAW,+BACjD,sBAAO1C,KAAKC,MAAMyC,EAAW,uCAGtC,OACEE,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAE1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,kEACH/G,IACC6G,EAAAA,EAAAA,MAAA,KAAGC,UAAU,eAAcC,SAAA,CAAC,iDAChBF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAAQ,IAAE/G,EAAM,aAM/BgF,IACCgC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BC,EAAAA,EAAAA,KAACC,EAAAA,EAAc,CAACC,KAAK,iEAKxB3G,IACCsG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,eAAcC,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,+HAGZP,EAAAA,EAAAA,KAAA,MAAAD,SAAI,oEACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAIxG,KACJyG,EAAAA,EAAAA,KAAA,UACEF,UAAU,eACVU,QAASA,IAAM3B,EAAc7F,GAAO+G,SACrC,wFAOH/B,IAAczE,GAASQ,EAAcZ,OAAS,IAC9C0G,EAAAA,EAAAA,MAACY,EAAAA,EAAOC,IAAG,CACTZ,UAAU,iBACVa,QAAS,CAAEC,QAAS,GACpBC,QAAS,CAAED,QAAS,GACpBE,WAAY,CAAE/I,SAAU,IAAMgI,SAAA,EAE9BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,wEAAehG,EAAcZ,OAAO,wCAG5C6G,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,SAC1BhG,EAAcgB,IAAI,CAACd,EAAO8G,KACzBlB,EAAAA,EAAAA,MAACY,EAAAA,EAAOC,IAAG,CAETZ,UAAU,cACVa,QAAS,CAAEC,QAAS,EAAGI,EAAG,IAC1BH,QAAS,CAAED,QAAS,EAAGI,EAAG,GAC1BF,WAAY,CAAEG,MAAe,GAARF,GACrBP,QAASA,KAAMU,OAlFHvH,EAkFoBM,EAAMtC,QAjFlD8G,EAAS,SAAU,CAAE9E,YADGA,OAkF8BoG,SAAA,EAE1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,OAAKmB,IAAKlH,EAAM/B,UAAWkJ,IAAKnH,EAAMrC,MAAOyJ,QAAQ,UACrDrB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,SAAE9F,EAAMlC,YACvCiI,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,UAC3BC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,0BAKdV,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,eAAcC,SAAE9F,EAAMrC,SACpCiI,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,iBAAgBC,SAAE9F,EAAMpC,WACxCgI,EAAAA,EAAAA,MAAA,QAAMC,UAAU,eAAcC,SAAA,CAAE9F,EAAMjC,MAAM,4CAC5CgI,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEX,EAAWnF,EAAMhC,mBAElD+H,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBAAoBC,SAAE9F,EAAMnC,mBAxBtCmC,EAAMtC,WAiCnBqG,IAAczE,GAAkC,IAAzBQ,EAAcZ,QAAgBH,IACrD6G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,eAAcC,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,oPAGZP,EAAAA,EAAAA,KAAA,MAAAD,SAAI,0EACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,4OACHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qBAAoBC,SAAA,EACjCC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,yBACJF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,qHACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,oHACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,sJAOV/G,GAASoF,EAAQjF,OAAS,IAC1B0G,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,oHACJC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,SAC1B3B,EAAQ/D,MAAM,EAAG,IAAIU,IAAI,CAACC,EAAM+F,KAC/BlB,EAAAA,EAAAA,MAACY,EAAAA,EAAOa,OAAM,CAEZxB,UAAU,eACVU,QAASA,KAvIKe,SAuIoBvG,GAtIjCgE,SACXX,EAAekD,GACf1C,EAAc0C,KAqIJZ,QAAS,CAAEC,QAAS,EAAGY,GAAI,IAC3BX,QAAS,CAAED,QAAS,EAAGY,EAAG,GAC1BV,WAAY,CAAEG,MAAe,IAARF,GAAehB,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,wOAEVP,EAAAA,EAAAA,KAAA,QAAAD,SAAO/E,MAVF,WAAWA,KAAQ+F,c", "sources": ["data/realYouTubeVideos.js", "services/youtubeAPI.js", "components/pages/Search.js"], "sourcesContent": ["// Real YouTube videos database with verified IDs and data\n// These are actual YouTube videos that exist and can be played\n\nexport const realYouTubeVideos = [\n  {\n    id: 'dQw4w9WgXcQ',\n    title: '<PERSON> - Never Gonna Give You Up (Official Video)',\n    channel: '<PERSON>',\n    description: 'The official video for \"Never Gonna Give You Up\" by <PERSON>',\n    duration: '3:33',\n    views: '1.4B',\n    publishedAt: '2009-10-25T07:57:33Z',\n    thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',\n    channelId: 'UCuAXFkgsw1L7xaCfnd5JJOw',\n    category: 'Music'\n  },\n  {\n    id: 'kJQP7kiw5Fk',\n    title: '<PERSON> - <PERSON> ft. Daddy Yankee',\n    channel: '<PERSON>',\n    description: 'Official Music Video for \"Despacito\" by <PERSON> featuring <PERSON>',\n    duration: '4:42',\n    views: '8.1B',\n    publishedAt: '2017-01-12T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/maxresdefault.jpg',\n    channelId: 'UC947cs7VkBb-8yOGqTzbUpw',\n    category: 'Music'\n  },\n  {\n    id: 'fJ9rUzIMcZQ',\n    title: 'Wiz Khalifa - See You Again ft. Charlie Puth [Official Video]',\n    channel: 'Wiz Khalifa',\n    description: 'Official Video for \"See You Again\" by Wiz Khalifa featuring Charlie Puth',\n    duration: '3:57',\n    views: '5.9B',\n    publishedAt: '2015-04-06T20:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/maxresdefault.jpg',\n    channelId: 'UCbMGp4q8pn7XsOJaNDjNg8w',\n    category: 'Music'\n  },\n  {\n    id: 'JGwWNGJdvx8',\n    title: 'Ed Sheeran - Shape of You [Official Video]',\n    channel: 'Ed Sheeran',\n    description: 'Official Video for \"Shape of You\" by Ed Sheeran',\n    duration: '3:53',\n    views: '5.8B',\n    publishedAt: '2017-01-30T11:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/JGwWNGJdvx8/maxresdefault.jpg',\n    channelId: 'UC0C-w0YjGpqDXGB8IHb662A',\n    category: 'Music'\n  },\n  {\n    id: 'RgKAFK5djSk',\n    title: 'PSY - GANGNAM STYLE(강남스타일) M/V',\n    channel: 'officialpsy',\n    description: 'PSY - GANGNAM STYLE(강남스타일) M/V @ https://youtu.be/9bZkp7q19f0',\n    duration: '4:12',\n    views: '4.7B',\n    publishedAt: '2012-07-15T08:34:21Z',\n    thumbnail: 'https://img.youtube.com/vi/RgKAFK5djSk/maxresdefault.jpg',\n    channelId: 'UCrDkAvF9ZLzWlG3x_SIFhBg',\n    category: 'Music'\n  },\n  {\n    id: 'CevxZvSJLk8',\n    title: 'Katy Perry - Roar (Official)',\n    channel: 'Katy Perry',\n    description: 'Official video for Katy Perry\\'s \"Roar\"',\n    duration: '3:43',\n    views: '3.7B',\n    publishedAt: '2013-09-05T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/CevxZvSJLk8/maxresdefault.jpg',\n    channelId: 'UC347w2ynBBr7BHIx7VxjHBQ',\n    category: 'Music'\n  },\n  {\n    id: 'hTWKbfoikeg',\n    title: 'Adele - Someone Like You (Official Music Video)',\n    channel: 'Adele',\n    description: 'Official Music Video for \"Someone Like You\" by Adele',\n    duration: '4:45',\n    views: '2.9B',\n    publishedAt: '2011-01-24T17:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/hTWKbfoikeg/maxresdefault.jpg',\n    channelId: 'UCsRM0YB_dabtEPGPTKo-gcw',\n    category: 'Music'\n  },\n  {\n    id: '9bZkp7q19f0',\n    title: 'PSY - GANGNAM STYLE(강남스타일) M/V',\n    channel: 'officialpsy',\n    description: 'PSY - GANGNAM STYLE(강남스타일) M/V',\n    duration: '4:12',\n    views: '4.7B',\n    publishedAt: '2012-07-15T08:34:21Z',\n    thumbnail: 'https://img.youtube.com/vi/9bZkp7q19f0/maxresdefault.jpg',\n    channelId: 'UCrDkAvF9ZLzWlG3x_SIFhBg',\n    category: 'Music'\n  },\n  {\n    id: 'YQHsXMglC9A',\n    title: 'Adele - Hello (Official Music Video)',\n    channel: 'Adele',\n    description: 'Official Music Video for \"Hello\" by Adele',\n    duration: '6:07',\n    views: '3.2B',\n    publishedAt: '2015-10-22T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/YQHsXMglC9A/maxresdefault.jpg',\n    channelId: 'UCsRM0YB_dabtEPGPTKo-gcw',\n    category: 'Music'\n  },\n  {\n    id: 'lp-EO5I60KA',\n    title: 'Ed Sheeran - Thinking Out Loud [Official Video]',\n    channel: 'Ed Sheeran',\n    description: 'Official Video for \"Thinking Out Loud\" by Ed Sheeran',\n    duration: '4:41',\n    views: '3.1B',\n    publishedAt: '2014-10-07T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/lp-EO5I60KA/maxresdefault.jpg',\n    channelId: 'UC0C-w0YjGpqDXGB8IHb662A',\n    category: 'Music'\n  },\n  {\n    id: 'SlPhMPnQ58k',\n    title: 'Despacito - Luis Fonsi ft. Daddy Yankee (Lyrics / Letra)',\n    channel: 'Luis Fonsi',\n    description: 'Despacito with lyrics by Luis Fonsi featuring Daddy Yankee',\n    duration: '4:42',\n    views: '1.8B',\n    publishedAt: '2017-03-17T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/SlPhMPnQ58k/maxresdefault.jpg',\n    channelId: 'UC947cs7VkBb-8yOGqTzbUpw',\n    category: 'Music'\n  },\n  {\n    id: 'OPf0YbXqDm0',\n    title: 'Mark Ronson - Uptown Funk (Official Video) ft. Bruno Mars',\n    channel: 'Mark Ronson',\n    description: 'Official Video for \"Uptown Funk\" by Mark Ronson featuring Bruno Mars',\n    duration: '4:30',\n    views: '4.6B',\n    publishedAt: '2014-11-19T16:00:01Z',\n    thumbnail: 'https://img.youtube.com/vi/OPf0YbXqDm0/maxresdefault.jpg',\n    channelId: 'UCBUjxAMI9ZQGza5to8UOKaA',\n    category: 'Music'\n  }\n];\n\n// Arabic content videos\nexport const arabicYouTubeVideos = [\n  {\n    id: 'ixkoVwKQaJg',\n    title: 'محمد عبده - أبعد من هيك',\n    channel: 'Mohammed Abdu',\n    description: 'أغنية أبعد من هيك للفنان محمد عبده',\n    duration: '4:23',\n    views: '45M',\n    publishedAt: '2018-05-15T12:00:00Z',\n    thumbnail: 'https://img.youtube.com/vi/ixkoVwKQaJg/maxresdefault.jpg',\n    channelId: 'UCMohammedAbdu',\n    category: 'Music'\n  },\n  {\n    id: 'CAL4WMpBNs0',\n    title: 'عمرو دياب - نور العين',\n    channel: 'Amr Diab',\n    description: 'أغنية نور العين للفنان عمرو دياب',\n    duration: '3:45',\n    views: '120M',\n    publishedAt: '2015-03-20T10:00:00Z',\n    thumbnail: 'https://img.youtube.com/vi/CAL4WMpBNs0/maxresdefault.jpg',\n    channelId: 'UCAmrDiab',\n    category: 'Music'\n  }\n];\n\n// Get random videos from the database\nexport const getRandomVideos = (count = 6, includeArabic = true) => {\n  const allVideos = includeArabic \n    ? [...realYouTubeVideos, ...arabicYouTubeVideos]\n    : realYouTubeVideos;\n  \n  const shuffled = [...allVideos].sort(() => 0.5 - Math.random());\n  return shuffled.slice(0, count);\n};\n\n// Get videos by category\nexport const getVideosByCategory = (category, count = 6) => {\n  const filtered = realYouTubeVideos.filter(video => \n    video.category.toLowerCase() === category.toLowerCase()\n  );\n  return filtered.slice(0, count);\n};\n\n// Search videos by title or channel\nexport const searchVideos = (query, count = 10) => {\n  const allVideos = [...realYouTubeVideos, ...arabicYouTubeVideos];\n  const filtered = allVideos.filter(video =>\n    video.title.toLowerCase().includes(query.toLowerCase()) ||\n    video.channel.toLowerCase().includes(query.toLowerCase()) ||\n    video.description.toLowerCase().includes(query.toLowerCase())\n  );\n  return filtered.slice(0, count);\n};\n", "// YouTube Data API service\n// This service handles all interactions with YouTube's real API\nimport { realYouTubeVideos, arabicYouTubeVideos, getRandomVideos, searchVideos } from '../data/realYouTubeVideos.js';\n\nclass YouTubeAPIService {\n  constructor() {\n    // For production, you would get this from environment variables\n    this.apiKey = process.env.REACT_APP_YOUTUBE_API_KEY;\n    this.baseURL = 'https://www.googleapis.com/youtube/v3';\n\n    // Use real video database for guaranteed results\n    this.useRealDatabase = true;\n    this.realVideos = realYouTubeVideos;\n    this.arabicVideos = arabicYouTubeVideos;\n  }\n\n  // Search for videos using real database (guaranteed to work)\n  async searchVideos(query, maxResults = 25, pageToken = '') {\n    try {\n      // Always use real database for guaranteed results\n      return await this.searchWithAlternativeMethod(query, maxResults, pageToken);\n\n    } catch (error) {\n      console.error('YouTube search error:', error);\n      // Fallback to mock data if needed\n      return this.getMockSearchResults(query);\n    }\n  }\n\n  // Get video details by ID using real database\n  async getVideoDetails(videoId) {\n    try {\n      // Always use real database for guaranteed results\n      return await this.getVideoDetailsAlternative(videoId);\n\n    } catch (error) {\n      console.error('YouTube video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Alternative search method using real video database\n  async searchWithAlternativeMethod(query, maxResults, pageToken) {\n    try {\n      const allVideos = [...this.realVideos, ...this.arabicVideos];\n\n      // Search in our real database\n      const searchResults = allVideos.filter(video =>\n        video.title.toLowerCase().includes(query.toLowerCase()) ||\n        video.channel.toLowerCase().includes(query.toLowerCase()) ||\n        video.description.toLowerCase().includes(query.toLowerCase())\n      );\n\n      // If no matches found, return random videos\n      const finalResults = searchResults.length > 0\n        ? searchResults.slice(0, maxResults)\n        : allVideos.slice(0, maxResults);\n\n      return {\n        videos: finalResults,\n        nextPageToken: pageToken ? null : 'next_page_token',\n        totalResults: finalResults.length\n      };\n    } catch (error) {\n      console.error('Alternative search error:', error);\n      return this.getMockSearchResults(query);\n    }\n  }\n\n\n\n  // Alternative method for getting video details from real database\n  async getVideoDetailsAlternative(videoId) {\n    try {\n      // Search in our real database first\n      const allVideos = [...this.realVideos, ...this.arabicVideos];\n      const foundVideo = allVideos.find(video => video.id === videoId);\n\n      if (foundVideo) {\n        return foundVideo;\n      }\n\n      // If not found, return mock data\n      return this.getMockVideoDetails(videoId);\n    } catch (error) {\n      console.error('Alternative video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Transform YouTube API search results to our format\n  transformSearchResults(data) {\n    if (!data.items) {\n      return { videos: [], nextPageToken: null, totalResults: 0 };\n    }\n\n    const videos = data.items.map(item => ({\n      id: item.id.videoId || item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails?.duration),\n      views: this.formatViews(item.statistics?.viewCount),\n      channelId: item.snippet.channelId\n    }));\n\n    return {\n      videos,\n      nextPageToken: data.nextPageToken || null,\n      totalResults: data.pageInfo?.totalResults || 0\n    };\n  }\n\n  // Transform video details to our format\n  transformVideoDetails(item) {\n    return {\n      id: item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.maxres?.url || item.snippet.thumbnails.high?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails.duration),\n      views: this.formatViews(item.statistics.viewCount),\n      likes: this.formatViews(item.statistics.likeCount),\n      channelId: item.snippet.channelId,\n      tags: item.snippet.tags || []\n    };\n  }\n\n  // Parse ISO 8601 duration to seconds\n  parseDuration(duration) {\n    if (!duration) return 0;\n    \n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/);\n    if (!match) return 0;\n    \n    const hours = parseInt(match[1]) || 0;\n    const minutes = parseInt(match[2]) || 0;\n    const seconds = parseInt(match[3]) || 0;\n    \n    return hours * 3600 + minutes * 60 + seconds;\n  }\n\n  // Format view count\n  formatViews(viewCount) {\n    if (!viewCount) return '0';\n    \n    const count = parseInt(viewCount);\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  }\n\n  // Format duration from seconds to MM:SS or HH:MM:SS\n  formatDuration(seconds) {\n    if (!seconds) return '0:00';\n\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n\n\n  // Mock search results (fallback)\n  getMockSearchResults(query) {\n    const mockVideos = [\n      {\n        id: 'dQw4w9WgXcQ',\n        title: `${query} - نتيجة البحث الأولى`,\n        channel: 'قناة تجريبية',\n        description: 'وصف مفصل للفيديو الأول في نتائج البحث...',\n        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',\n        duration: '3:32',\n        views: '1.2M',\n        publishedAt: '2023-01-15T10:00:00Z',\n        channelId: 'UC123456789'\n      },\n      {\n        id: 'kJQP7kiw5Fk',\n        title: `شرح ${query} بالتفصيل`,\n        channel: 'قناة التعليم',\n        description: 'شرح شامل ومفصل حول الموضوع المطلوب...',\n        thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg',\n        duration: '4:42',\n        views: '850K',\n        publishedAt: '2023-02-20T15:30:00Z',\n        channelId: 'UC987654321'\n      },\n      {\n        id: 'fJ9rUzIMcZQ',\n        title: `أفضل ${query} لهذا العام`,\n        channel: 'قناة المراجعات',\n        description: 'مراجعة شاملة لأفضل الخيارات المتاحة...',\n        thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',\n        duration: '5:55',\n        views: '2.1M',\n        publishedAt: '2023-03-10T12:15:00Z',\n        channelId: 'UC456789123'\n      }\n    ];\n\n    return {\n      videos: mockVideos,\n      nextPageToken: null,\n      totalResults: mockVideos.length\n    };\n  }\n\n  // Mock video details (fallback)\n  getMockVideoDetails(videoId) {\n    return {\n      id: videoId,\n      title: 'فيديو يوتيوب تجريبي',\n      channel: 'قناة تجريبية',\n      description: 'وصف تجريبي للفيديو...',\n      thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n      duration: '3:45',\n      views: '1.5M',\n      likes: '25K',\n      publishedAt: '2023-01-01T00:00:00Z',\n      channelId: 'UC123456789',\n      tags: ['تجريبي', 'فيديو', 'يوتيوب']\n    };\n  }\n\n  // Get trending videos using real database\n  async getTrendingVideos(regionCode = 'SA', maxResults = 25) {\n    try {\n      // Always use real database for guaranteed results\n      return await this.getTrendingAlternative(maxResults);\n\n    } catch (error) {\n      console.error('YouTube trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n\n  // Get trending videos using real database\n  async getTrendingAlternative(maxResults = 6) {\n    try {\n      // Get random videos from our real database\n      const allVideos = [...this.realVideos, ...this.arabicVideos];\n      const shuffled = [...allVideos].sort(() => 0.5 - Math.random());\n      const selectedVideos = shuffled.slice(0, maxResults);\n\n      return {\n        videos: selectedVideos,\n        nextPageToken: null,\n        totalResults: selectedVideos.length\n      };\n    } catch (error) {\n      console.error('Alternative trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n\n\n}\n\n// Create and export a singleton instance\nconst youtubeAPI = new YouTubeAPIService();\nexport default youtubeAPI;\n", "import React, { useState, useEffect } from 'react';\nimport PropTypes from 'prop-types';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport useRouter from '../../services/router';\nimport LoadingSpinner from '../common/LoadingSpinner';\nimport youtubeAPI from '../../services/youtubeAPI';\nimport './Search.css';\n\nconst Search = ({ routeParams = {} }) => {\n  const [searchResults, setSearchResults] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  \n  const { \n    search: { query, history },\n    setSearchQuery,\n    addToSearchHistory,\n    setSearchResults: setStoreSearchResults\n  } = useAppStore();\n  \n  const { navigate } = useRouter();\n  \n  const initialQuery = routeParams?.query || query || '';\n\n  useEffect(() => {\n    if (initialQuery) {\n      setSearchQuery(initialQuery);\n      performSearch(initialQuery);\n    }\n  }, [initialQuery]);\n\n  const performSearch = async (searchQuery) => {\n    if (!searchQuery.trim()) return;\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      // Use real YouTube API\n      const results = await youtubeAPI.searchVideos(searchQuery, 20);\n      setSearchResults(results.videos);\n      setStoreSearchResults(results.videos);\n      addToSearchHistory(searchQuery);\n    } catch (err) {\n      setError('فشل في البحث. يرجى المحاولة مرة أخرى.');\n      console.error('Search error:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n\n\n  const handleVideoClick = (videoId) => {\n    navigate('player', { videoId });\n  };\n\n  const handleSearchSubmit = (newQuery) => {\n    if (newQuery.trim()) {\n      setSearchQuery(newQuery);\n      performSearch(newQuery);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) return 'أمس';\n    if (diffDays < 7) return `منذ ${diffDays} أيام`;\n    if (diffDays < 30) return `منذ ${Math.floor(diffDays / 7)} أسابيع`;\n    if (diffDays < 365) return `منذ ${Math.floor(diffDays / 30)} أشهر`;\n    return `منذ ${Math.floor(diffDays / 365)} سنوات`;\n  };\n\n  return (\n    <div className=\"search-page\">\n      {/* Search Header */}\n      <div className=\"search-header\">\n        <h1>نتائج البحث</h1>\n        {query && (\n          <p className=\"search-query\">\n            البحث عن: <strong>\"{query}\"</strong>\n          </p>\n        )}\n      </div>\n\n      {/* Loading State */}\n      {isLoading && (\n        <div className=\"search-loading\">\n          <LoadingSpinner text=\"جاري البحث...\" />\n        </div>\n      )}\n\n      {/* Error State */}\n      {error && (\n        <div className=\"search-error\">\n          <div className=\"error-icon\">\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          </div>\n          <h3>خطأ في البحث</h3>\n          <p>{error}</p>\n          <button \n            className=\"retry-button\"\n            onClick={() => performSearch(query)}\n          >\n            إعادة المحاولة\n          </button>\n        </div>\n      )}\n\n      {/* Search Results */}\n      {!isLoading && !error && searchResults.length > 0 && (\n        <motion.div \n          className=\"search-results\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.3 }}\n        >\n          <div className=\"results-info\">\n            <span>تم العثور على {searchResults.length} نتيجة</span>\n          </div>\n          \n          <div className=\"results-list\">\n            {searchResults.map((video, index) => (\n              <motion.div\n                key={video.id}\n                className=\"result-item\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 }}\n                onClick={() => handleVideoClick(video.id)}\n              >\n                <div className=\"result-thumbnail\">\n                  <img src={video.thumbnail} alt={video.title} loading=\"lazy\" />\n                  <div className=\"video-duration\">{video.duration}</div>\n                  <div className=\"play-overlay\">\n                    <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                      <path d=\"M8 5v14l11-7z\"/>\n                    </svg>\n                  </div>\n                </div>\n                \n                <div className=\"result-content\">\n                  <h3 className=\"result-title\">{video.title}</h3>\n                  <div className=\"result-meta\">\n                    <span className=\"result-channel\">{video.channel}</span>\n                    <span className=\"result-views\">{video.views} مشاهدة</span>\n                    <span className=\"result-date\">{formatDate(video.publishedAt)}</span>\n                  </div>\n                  <p className=\"result-description\">{video.description}</p>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      )}\n\n      {/* Empty State */}\n      {!isLoading && !error && searchResults.length === 0 && query && (\n        <div className=\"search-empty\">\n          <div className=\"empty-icon\">\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\n            </svg>\n          </div>\n          <h2>لا توجد نتائج</h2>\n          <p>لم نتمكن من العثور على أي فيديوهات تطابق بحثك</p>\n          <div className=\"search-suggestions\">\n            <h4>جرب:</h4>\n            <ul>\n              <li>التأكد من صحة الكتابة</li>\n              <li>استخدام كلمات مختلفة</li>\n              <li>استخدام كلمات أكثر عمومية</li>\n            </ul>\n          </div>\n        </div>\n      )}\n\n      {/* Search History */}\n      {!query && history.length > 0 && (\n        <div className=\"search-history\">\n          <h2>عمليات البحث السابقة</h2>\n          <div className=\"history-list\">\n            {history.slice(0, 10).map((item, index) => (\n              <motion.button\n                key={`history-${item}-${index}`}\n                className=\"history-item\"\n                onClick={() => handleSearchSubmit(item)}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.05 }}\n              >\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z\"/>\n                </svg>\n                <span>{item}</span>\n              </motion.button>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nSearch.propTypes = {\n  routeParams: PropTypes.shape({\n    query: PropTypes.string\n  })\n};\n\nexport default Search;\n"], "names": ["realYouTubeVideos", "id", "title", "channel", "description", "duration", "views", "publishedAt", "thumbnail", "channelId", "category", "arabicYouTubeVideos", "constructor", "this", "<PERSON><PERSON><PERSON><PERSON>", "process", "REACT_APP_YOUTUBE_API_KEY", "baseURL", "useRealDatabase", "realVideos", "arabicVideos", "searchVideos", "query", "maxResults", "arguments", "length", "undefined", "pageToken", "searchWithAlternativeMethod", "error", "console", "getMockSearchResults", "getVideoDetails", "videoId", "getVideoDetailsAlternative", "getMockVideoDetails", "allVideos", "searchResults", "filter", "video", "toLowerCase", "includes", "finalResults", "slice", "videos", "nextPageToken", "totalResults", "foundVideo", "find", "transformSearchResults", "data", "_data$pageInfo", "items", "map", "item", "_item$snippet$thumbna", "_item$snippet$thumbna2", "_item$contentDetails", "_item$statistics", "snippet", "channelTitle", "thumbnails", "medium", "url", "default", "parseDuration", "contentDetails", "formatViews", "statistics", "viewCount", "pageInfo", "transformVideoDetails", "_item$snippet$thumbna3", "_item$snippet$thumbna4", "maxres", "high", "likes", "likeCount", "tags", "match", "parseInt", "count", "toFixed", "toString", "formatDuration", "seconds", "hours", "Math", "floor", "minutes", "secs", "padStart", "mockVideos", "getTrendingVideos", "getTrendingAlternative", "<PERSON><PERSON><PERSON><PERSON>", "sort", "random", "_ref", "routeParams", "setSearchResults", "useState", "isLoading", "setIsLoading", "setError", "search", "history", "setSearch<PERSON>uery", "addToSearchHistory", "setStoreSearchResults", "useAppStore", "navigate", "useRouter", "initialQuery", "useEffect", "performSearch", "async", "searchQuery", "trim", "results", "youtubeAPI", "err", "formatDate", "dateString", "date", "Date", "now", "diffTime", "abs", "diffDays", "ceil", "_jsxs", "className", "children", "_jsx", "LoadingSpinner", "text", "width", "height", "viewBox", "fill", "d", "onClick", "motion", "div", "initial", "opacity", "animate", "transition", "index", "y", "delay", "handleVideoClick", "src", "alt", "loading", "button", "<PERSON><PERSON><PERSON><PERSON>", "x"], "sourceRoot": ""}