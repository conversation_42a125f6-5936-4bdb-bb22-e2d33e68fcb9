/* Home page styles */
.home-page {
  padding: var(--spacing-lg);
  max-width: 1200px;
  margin: 0 auto;
}

/* Hero Section */
.hero-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
  margin-bottom: var(--spacing-3xl);
  min-height: 400px;
}

.hero-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.hero-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin: 0;
}

.hero-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

.hero-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.hero-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  font-family: var(--font-family-primary);
  cursor: pointer;
  transition: var(--transition-fast);
  min-height: 48px;
}

.hero-button.primary {
  background-color: var(--primary-color);
  color: white;
}

.hero-button.primary:hover {
  background-color: var(--primary-hover);
  box-shadow: var(--shadow-md);
}

.hero-button.secondary {
  background-color: transparent;
  color: var(--text-primary);
  border: 2px solid var(--border-primary);
}

.hero-button.secondary:hover {
  background-color: var(--surface-hover);
  border-color: var(--border-hover);
}

.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-video-preview {
  width: 300px;
  height: 200px;
  background: linear-gradient(135deg, var(--surface-secondary), var(--surface-tertiary));
  border-radius: var(--radius-xl);
  border: 2px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.video-preview-screen {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
  color: var(--primary-color);
}

.video-preview-controls {
  height: 40px;
  background-color: var(--surface-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: 0 var(--spacing-md);
}

.control-button {
  width: 8px;
  height: 8px;
  background-color: var(--text-tertiary);
  border-radius: var(--radius-full);
}

/* Stats Section */
.stats-section {
  margin-bottom: var(--spacing-3xl);
}

.section-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--spacing-xl);
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
}

.stat-card {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background-color: var(--surface-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  transition: var(--transition-fast);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-hover);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.videos {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.stat-icon.ads {
  background: linear-gradient(135deg, var(--success-color), #059669);
}

.stat-icon.time {
  background: linear-gradient(135deg, var(--info-color), #0284c7);
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.stat-number {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1;
}

/* Featured Videos */
.featured-section {
  margin-bottom: var(--spacing-3xl);
}

.videos-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  gap: var(--spacing-md);
}

.videos-loading .loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.videos-loading .spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-primary);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.videos-loading p {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  margin: 0;
}

.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.video-card {
  background-color: var(--surface-secondary);
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--border-primary);
  cursor: pointer;
  transition: var(--transition-fast);
}

.video-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--border-hover);
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-fast);
}

.video-card:hover .video-thumbnail img {
  transform: scale(1.05);
}

.video-duration {
  position: absolute;
  bottom: var(--spacing-sm);
  left: var(--spacing-sm);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.video-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 64px;
  height: 64px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: var(--transition-fast);
}

.video-card:hover .video-overlay {
  opacity: 1;
}

.video-info {
  padding: var(--spacing-md);
}

.video-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-xs) 0;
  line-height: var(--line-height-tight);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-channel {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin: 0 0 var(--spacing-xs) 0;
}

.video-views {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin: 0;
}

/* Features Section */
.features-section {
  margin-bottom: var(--spacing-3xl);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.feature-card {
  padding: var(--spacing-xl);
  background-color: var(--surface-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  text-align: center;
  transition: var(--transition-fast);
}

.feature-card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--border-hover);
}

.feature-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.feature-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.feature-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    text-align: center;
  }
  
  .hero-video-preview {
    width: 250px;
    height: 160px;
  }
}

@media (max-width: 768px) {
  .home-page {
    padding: var(--spacing-md);
  }
  
  .hero-title {
    font-size: var(--font-size-3xl);
  }
  
  .hero-description {
    font-size: var(--font-size-md);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .videos-grid {
    grid-template-columns: 1fr;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 640px) {
  .hero-actions {
    flex-direction: column;
  }
  
  .hero-button {
    width: 100%;
    justify-content: center;
  }
  
  .hero-video-preview {
    width: 200px;
    height: 130px;
  }
  
  .section-title {
    font-size: var(--font-size-xl);
  }
}
