{"version": 3, "file": "static/js/411.987e5a9d.chunk.js", "mappings": "oLAIA,MAmFA,EAnFcA,KAEVC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTJ,UAAU,kBACVK,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAAMT,SAAA,EAE9BC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBF,EAAAA,EAAAA,KAAA,OAAKY,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcb,UACjEF,EAAAA,EAAAA,KAAA,QAAMgB,EAAE,uBAGZhB,EAAAA,EAAAA,KAAA,MAAAE,SAAI,wBACJF,EAAAA,EAAAA,KAAA,KAAGC,UAAU,UAASC,SAAC,yDAGzBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,MAAA,WAASF,UAAU,gBAAeC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,mEACJF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,ioBAMLC,EAAAA,EAAAA,MAAA,WAASF,UAAU,gBAAeC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,uGACJC,EAAAA,EAAAA,MAAA,MAAIF,UAAU,gBAAeC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,sIACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,2HACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,qHACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,wIACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,4IACJF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,4IAIRC,EAAAA,EAAAA,MAAA,WAASF,UAAU,gBAAeC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,6GACJC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,cACRF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,wFAERC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,iBACRF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,+FAERC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,aACRF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,4EAERC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,mBACRF,EAAAA,EAAAA,KAAA,QAAAE,SAAM,oGAKZC,EAAAA,EAAAA,MAAA,WAASF,UAAU,gBAAeC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,qFACJC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,oJACHF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,2IAIPC,EAAAA,EAAAA,MAAA,WAASF,UAAU,gBAAeC,SAAA,EAChCF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,gDACJF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,0Z", "sources": ["components/pages/About.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport './About.css';\n\nconst About = () => {\n  return (\n    <div className=\"about-page\">\n      <motion.div \n        className=\"about-container\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        <div className=\"about-header\">\n          <div className=\"app-logo\">\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M8 5v14l11-7z\"/>\n            </svg>\n          </div>\n          <h1>YouTube Player Pro</h1>\n          <p className=\"version\">الإصدار 2.0.0</p>\n        </div>\n\n        <div className=\"about-content\">\n          <section className=\"about-section\">\n            <h2>حول التطبيق</h2>\n            <p>\n              مشغل يوتيوب احترافي مع واجهة عربية متقدمة ومانع إعلانات قوي. \n              تم تطويره خصيصاً لتوفير تجربة مشاهدة مثالية للمستخدمين العرب.\n            </p>\n          </section>\n\n          <section className=\"about-section\">\n            <h2>المميزات الرئيسية</h2>\n            <ul className=\"features-list\">\n              <li>واجهة عربية كاملة مع دعم RTL</li>\n              <li>مانع إعلانات قوي وفعال</li>\n              <li>ثيم داكن مريح للعينين</li>\n              <li>تحكم كامل في جودة الفيديو</li>\n              <li>حفظ الإعدادات والتفضيلات</li>\n              <li>إحصائيات مفصلة للمشاهدة</li>\n            </ul>\n          </section>\n\n          <section className=\"about-section\">\n            <h2>التقنيات المستخدمة</h2>\n            <div className=\"tech-grid\">\n              <div className=\"tech-item\">\n                <strong>React 18</strong>\n                <span>واجهة المستخدم</span>\n              </div>\n              <div className=\"tech-item\">\n                <strong>Electron 23</strong>\n                <span>تطبيق سطح المكتب</span>\n              </div>\n              <div className=\"tech-item\">\n                <strong>Zustand</strong>\n                <span>إدارة الحالة</span>\n              </div>\n              <div className=\"tech-item\">\n                <strong>Framer Motion</strong>\n                <span>الرسوم المتحركة</span>\n              </div>\n            </div>\n          </section>\n\n          <section className=\"about-section\">\n            <h2>الدعم والتواصل</h2>\n            <div className=\"contact-info\">\n              <p>للدعم الفني أو الاستفسارات:</p>\n              <p>البريد الإلكتروني: <EMAIL></p>\n            </div>\n          </section>\n\n          <section className=\"about-section\">\n            <h2>الترخيص</h2>\n            <p>\n              هذا التطبيق مرخص تحت رخصة MIT. \n              يمكنك استخدامه وتعديله بحرية وفقاً لشروط الترخيص.\n            </p>\n          </section>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default About;\n"], "names": ["About", "_jsx", "className", "children", "_jsxs", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "width", "height", "viewBox", "fill", "d"], "sourceRoot": ""}