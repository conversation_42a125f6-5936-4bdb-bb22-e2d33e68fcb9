import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import useAppStore from '../../stores/appStore';
import './SearchBar.css';

const SearchBar = ({ 
  onSubmit, 
  onFocus, 
  onBlur, 
  placeholder = "ابحث في يوتيوب...", 
  autoFocus = false 
}) => {
  const [query, setQuery] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef(null);
  
  const { 
    search: { history, suggestions },
    addToSearchHistory,
    clearSearchHistory
  } = useAppStore();

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (query.trim()) {
      addToSearchHistory(query.trim());
      onSubmit?.(query.trim());
      setShowSuggestions(false);
      inputRef.current?.blur();
    }
  };

  const handleFocus = () => {
    setIsFocused(true);
    setShowSuggestions(true);
    onFocus?.();
  };

  const handleBlur = () => {
    // Delay to allow clicking on suggestions
    setTimeout(() => {
      setIsFocused(false);
      setShowSuggestions(false);
      onBlur?.();
    }, 150);
  };

  const handleSuggestionClick = (suggestion) => {
    setQuery(suggestion);
    addToSearchHistory(suggestion);
    onSubmit?.(suggestion);
    setShowSuggestions(false);
    inputRef.current?.blur();
  };

  const handleClearHistory = () => {
    clearSearchHistory();
  };

  const filteredHistory = history.filter(item => 
    item.toLowerCase().includes(query.toLowerCase())
  );

  const filteredSuggestions = suggestions.filter(item => 
    item.toLowerCase().includes(query.toLowerCase()) && 
    !history.includes(item)
  );

  return (
    <div className="search-bar-container">
      <form onSubmit={handleSubmit} className="search-form">
        <div className={`search-input-container ${isFocused ? 'focused' : ''}`}>
          {/* Search icon */}
          <button 
            type="submit" 
            className="search-button"
            aria-label="بحث"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
            </svg>
          </button>

          {/* Input field */}
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={handleFocus}
            onBlur={handleBlur}
            placeholder={placeholder}
            className="search-input"
            autoComplete="off"
            spellCheck="false"
          />

          {/* Clear button */}
          {query && (
            <motion.button
              type="button"
              className="clear-button"
              onClick={() => setQuery('')}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              aria-label="مسح"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </motion.button>
          )}

          {/* Voice search button (future feature) */}
          <button 
            type="button" 
            className="voice-button"
            aria-label="البحث الصوتي"
            disabled
          >
            <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
            </svg>
          </button>
        </div>
      </form>

      {/* Suggestions dropdown */}
      <AnimatePresence>
        {showSuggestions && (filteredHistory.length > 0 || filteredSuggestions.length > 0) && (
          <motion.div
            className="suggestions-dropdown"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {/* Search history */}
            {filteredHistory.length > 0 && (
              <div className="suggestions-section">
                <div className="suggestions-header">
                  <span>البحث السابق</span>
                  <button 
                    className="clear-history-button"
                    onClick={handleClearHistory}
                    aria-label="مسح السجل"
                  >
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                  </button>
                </div>
                {filteredHistory.map((item, index) => (
                  <motion.button
                    key={`history-${index}`}
                    className="suggestion-item history-item"
                    onClick={() => handleSuggestionClick(item)}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z"/>
                    </svg>
                    <span>{item}</span>
                  </motion.button>
                ))}
              </div>
            )}

            {/* Search suggestions */}
            {filteredSuggestions.length > 0 && (
              <div className="suggestions-section">
                <div className="suggestions-header">
                  <span>اقتراحات البحث</span>
                </div>
                {filteredSuggestions.map((item, index) => (
                  <motion.button
                    key={`suggestion-${index}`}
                    className="suggestion-item"
                    onClick={() => handleSuggestionClick(item)}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: (filteredHistory.length + index) * 0.05 }}
                  >
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                    <span>{item}</span>
                  </motion.button>
                ))}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SearchBar;
