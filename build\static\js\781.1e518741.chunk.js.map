{"version": 3, "file": "static/js/781.1e518741.chunk.js", "mappings": "6MAOA,MAmQA,EAnQeA,IAAsB,IAArB,YAAEC,GAAaD,EAC7B,MAAOE,EAAeC,IAAoBC,EAAAA,EAAAA,UAAS,KAC5CC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCG,EAAOC,IAAYJ,EAAAA,EAAAA,UAAS,OAGjCK,QAAQ,MAAEC,EAAK,QAAEC,GAAS,eAC1BC,EAAc,mBACdC,EACAV,iBAAkBW,IAChBC,EAAAA,EAAAA,MAEE,SAAEC,IAAaC,EAAAA,EAAAA,MAEfC,GAA0B,OAAXjB,QAAW,IAAXA,OAAW,EAAXA,EAAaS,QAASA,GAAS,IAEpDS,EAAAA,EAAAA,WAAU,KACJD,IACFN,EAAeM,GACfE,EAAcF,KAEf,CAACA,IAEJ,MAAME,EAAgBC,UACpB,GAAKC,EAAYC,OAAjB,CAEAjB,GAAa,GACbE,EAAS,MAET,IAEE,MAAMgB,QAAoBC,EAAsBH,GAChDnB,EAAiBqB,GACjBV,EAAsBU,GACtBX,EAAmBS,EACrB,CAAE,MAAOI,GACPlB,EAAS,0LACTmB,QAAQpB,MAAM,gBAAiBmB,EACjC,CAAC,QACCpB,GAAa,EACf,CAhB+B,GAmB3BmB,EAAwBJ,gBAEtB,IAAIO,QAAQC,GAAWC,WAAWD,EAAS,MAG1C,CACL,CACEE,GAAI,cACJC,MAAO,GAAGtB,yGACVuB,QAAS,sEACTC,YAAa,sMACbC,SAAU,OACVC,MAAO,OACPC,YAAa,aACbC,UAAW,wDAEb,CACEP,GAAI,cACJC,MAAO,sBAAOtB,qDACduB,QAAS,sEACTC,YAAa,yLACbC,SAAU,OACVC,MAAO,OACPC,YAAa,aACbC,UAAW,wDAEb,CACEP,GAAI,cACJC,MAAO,4BAAQtB,4DACfuB,QAAS,kFACTC,YAAa,oMACbC,SAAU,OACVC,MAAO,OACPC,YAAa,aACbC,UAAW,wDAEb,CACEP,GAAI,cACJC,MAAO,6EAAiBtB,IACxBuB,QAAS,4EACTC,YAAa,kLACbC,SAAU,OACVC,MAAO,OACPC,YAAa,aACbC,UAAW,wDAEb,CACEP,GAAI,cACJC,MAAO,GAAGtB,2DACVuB,QAAS,gEACTC,YAAa,6IACbC,SAAU,OACVC,MAAO,OACPC,YAAa,aACbC,UAAW,0DAgBXC,EAAcC,IAClB,MAAMC,EAAO,IAAIC,KAAKF,GAChBG,EAAM,IAAID,KACVE,EAAWC,KAAKC,IAAIH,EAAMF,GAC1BM,EAAWF,KAAKG,KAAKJ,EAAQ,OAEnC,OAAiB,IAAbG,EAAuB,qBACvBA,EAAW,EAAU,sBAAOA,6BAC5BA,EAAW,GAAW,sBAAOF,KAAKI,MAAMF,EAAW,0CACnDA,EAAW,IAAY,sBAAOF,KAAKI,MAAMF,EAAW,+BACjD,sBAAOF,KAAKI,MAAMF,EAAW,uCAGtC,OACEG,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAE1BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,kEACH1C,IACCwC,EAAAA,EAAAA,MAAA,KAAGC,UAAU,eAAcC,SAAA,CAAC,iDAChBF,EAAAA,EAAAA,MAAA,UAAAE,SAAA,CAAQ,IAAE1C,EAAM,aAM/BL,IACCgD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,UAC7BC,EAAAA,EAAAA,KAACC,EAAAA,EAAc,CAACC,KAAK,iEAKxBhD,IACC2C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,eAAcC,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,+HAGZP,EAAAA,EAAAA,KAAA,MAAAD,SAAI,oEACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAI7C,KACJ8C,EAAAA,EAAAA,KAAA,UACEF,UAAU,eACVU,QAASA,IAAMzC,EAAcV,GAAO0C,SACrC,wFAOH/C,IAAcE,GAASL,EAAc4D,OAAS,IAC9CZ,EAAAA,EAAAA,MAACa,EAAAA,EAAOC,IAAG,CACTb,UAAU,iBACVc,QAAS,CAAEC,QAAS,GACpBC,QAAS,CAAED,QAAS,GACpBE,WAAY,CAAEjC,SAAU,IAAMiB,SAAA,EAE9BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,MAAA,QAAAE,SAAA,CAAM,wEAAelD,EAAc4D,OAAO,wCAG5CT,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,SAC1BlD,EAAcmE,IAAI,CAACC,EAAOC,KACzBrB,EAAAA,EAAAA,MAACa,EAAAA,EAAOC,IAAG,CAETb,UAAU,cACVc,QAAS,CAAEC,QAAS,EAAGM,EAAG,IAC1BL,QAAS,CAAED,QAAS,EAAGM,EAAG,GAC1BJ,WAAY,CAAEK,MAAe,GAARF,GACrBV,QAASA,KAAMa,OAlFHC,EAkFoBL,EAAMvC,QAjFlDf,EAAS,SAAU,CAAE2D,YADGA,OAkF8BvB,SAAA,EAE1CF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,mBAAkBC,SAAA,EAC/BC,EAAAA,EAAAA,KAAA,OAAKuB,IAAKN,EAAMhC,UAAWuC,IAAKP,EAAMtC,MAAO8C,QAAQ,UACrDzB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,SAAEkB,EAAMnC,YACvCkB,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,UAC3BC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,0BAKdV,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,eAAcC,SAAEkB,EAAMtC,SACpCkB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,QAAMF,UAAU,iBAAgBC,SAAEkB,EAAMrC,WACxCiB,EAAAA,EAAAA,MAAA,QAAMC,UAAU,eAAcC,SAAA,CAAEkB,EAAMlC,MAAM,4CAC5CiB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,cAAaC,SAAEb,EAAW+B,EAAMjC,mBAElDgB,EAAAA,EAAAA,KAAA,KAAGF,UAAU,qBAAoBC,SAAEkB,EAAMpC,mBAxBtCoC,EAAMvC,WAiCnB1B,IAAcE,GAAkC,IAAzBL,EAAc4D,QAAgBpD,IACrDwC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,eAAcC,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aAAYC,UACzBC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,oPAGZP,EAAAA,EAAAA,KAAA,MAAAD,SAAI,0EACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,4OACHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,qBAAoBC,SAAA,EACjCC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,yBACJF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,qHACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,oHACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,sJAOV1C,GAASC,EAAQmD,OAAS,IAC1BZ,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,oHACJC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,SAC1BzC,EAAQoE,MAAM,EAAG,IAAIV,IAAI,CAACW,EAAMT,KAC/BrB,EAAAA,EAAAA,MAACa,EAAAA,EAAOkB,OAAM,CAEZ9B,UAAU,eACVU,QAASA,KAvIKqB,SAuIoBF,GAtIjCzD,SACXX,EAAesE,GACf9D,EAAc8D,KAqIJjB,QAAS,CAAEC,QAAS,EAAGiB,GAAI,IAC3BhB,QAAS,CAAED,QAAS,EAAGiB,EAAG,GAC1Bf,WAAY,CAAEK,MAAe,IAARF,GAAenB,SAAA,EAEpCC,EAAAA,EAAAA,KAAA,OAAKG,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcP,UACjEC,EAAAA,EAAAA,KAAA,QAAMO,EAAE,wOAEVP,EAAAA,EAAAA,KAAA,QAAAD,SAAO4B,MAVFT,Y", "sources": ["components/pages/Search.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport useRouter from '../../services/router';\nimport LoadingSpinner from '../common/LoadingSpinner';\nimport './Search.css';\n\nconst Search = ({ routeParams }) => {\n  const [searchResults, setSearchResults] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState(null);\n  \n  const { \n    search: { query, history },\n    setSearchQuery,\n    addToSearchHistory,\n    setSearchResults: setStoreSearchResults\n  } = useAppStore();\n  \n  const { navigate } = useRouter();\n  \n  const initialQuery = routeParams?.query || query || '';\n\n  useEffect(() => {\n    if (initialQuery) {\n      setSearchQuery(initialQuery);\n      performSearch(initialQuery);\n    }\n  }, [initialQuery]);\n\n  const performSearch = async (searchQuery) => {\n    if (!searchQuery.trim()) return;\n    \n    setIsLoading(true);\n    setError(null);\n    \n    try {\n      // Simulate YouTube search API call\n      const mockResults = await simulateYouTubeSearch(searchQuery);\n      setSearchResults(mockResults);\n      setStoreSearchResults(mockResults);\n      addToSearchHistory(searchQuery);\n    } catch (err) {\n      setError('فشل في البحث. يرجى المحاولة مرة أخرى.');\n      console.error('Search error:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const simulateYouTubeSearch = async (query) => {\n    // Simulate API delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    // Mock search results\n    return [\n      {\n        id: 'dQw4w9WgXcQ',\n        title: `${query} - نتيجة البحث الأولى`,\n        channel: 'قناة تجريبية',\n        description: 'وصف مفصل للفيديو الأول في نتائج البحث...',\n        duration: '3:32',\n        views: '1.2M',\n        publishedAt: '2023-01-15',\n        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg'\n      },\n      {\n        id: 'kJQP7kiw5Fk',\n        title: `شرح ${query} بالتفصيل`,\n        channel: 'قناة التعليم',\n        description: 'شرح شامل ومفصل حول الموضوع المطلوب...',\n        duration: '4:42',\n        views: '850K',\n        publishedAt: '2023-02-20',\n        thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg'\n      },\n      {\n        id: 'fJ9rUzIMcZQ',\n        title: `أفضل ${query} لهذا العام`,\n        channel: 'قناة المراجعات',\n        description: 'مراجعة شاملة لأفضل الخيارات المتاحة...',\n        duration: '5:55',\n        views: '2.1M',\n        publishedAt: '2023-03-10',\n        thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg'\n      },\n      {\n        id: 'JGwWNGJdvx8',\n        title: `كيفية استخدام ${query}`,\n        channel: 'قناة الشروحات',\n        description: 'دليل خطوة بخطوة للاستخدام الصحيح...',\n        duration: '2:18',\n        views: '950K',\n        publishedAt: '2023-04-05',\n        thumbnail: 'https://img.youtube.com/vi/JGwWNGJdvx8/mqdefault.jpg'\n      },\n      {\n        id: 'RgKAFK5djSk',\n        title: `${query} للمبتدئين`,\n        channel: 'قناة التعلم',\n        description: 'شرح مبسط ومناسب للمبتدئين...',\n        duration: '6:12',\n        views: '1.7M',\n        publishedAt: '2023-05-12',\n        thumbnail: 'https://img.youtube.com/vi/RgKAFK5djSk/mqdefault.jpg'\n      }\n    ];\n  };\n\n  const handleVideoClick = (videoId) => {\n    navigate('player', { videoId });\n  };\n\n  const handleSearchSubmit = (newQuery) => {\n    if (newQuery.trim()) {\n      setSearchQuery(newQuery);\n      performSearch(newQuery);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) return 'أمس';\n    if (diffDays < 7) return `منذ ${diffDays} أيام`;\n    if (diffDays < 30) return `منذ ${Math.floor(diffDays / 7)} أسابيع`;\n    if (diffDays < 365) return `منذ ${Math.floor(diffDays / 30)} أشهر`;\n    return `منذ ${Math.floor(diffDays / 365)} سنوات`;\n  };\n\n  return (\n    <div className=\"search-page\">\n      {/* Search Header */}\n      <div className=\"search-header\">\n        <h1>نتائج البحث</h1>\n        {query && (\n          <p className=\"search-query\">\n            البحث عن: <strong>\"{query}\"</strong>\n          </p>\n        )}\n      </div>\n\n      {/* Loading State */}\n      {isLoading && (\n        <div className=\"search-loading\">\n          <LoadingSpinner text=\"جاري البحث...\" />\n        </div>\n      )}\n\n      {/* Error State */}\n      {error && (\n        <div className=\"search-error\">\n          <div className=\"error-icon\">\n            <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n            </svg>\n          </div>\n          <h3>خطأ في البحث</h3>\n          <p>{error}</p>\n          <button \n            className=\"retry-button\"\n            onClick={() => performSearch(query)}\n          >\n            إعادة المحاولة\n          </button>\n        </div>\n      )}\n\n      {/* Search Results */}\n      {!isLoading && !error && searchResults.length > 0 && (\n        <motion.div \n          className=\"search-results\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.3 }}\n        >\n          <div className=\"results-info\">\n            <span>تم العثور على {searchResults.length} نتيجة</span>\n          </div>\n          \n          <div className=\"results-list\">\n            {searchResults.map((video, index) => (\n              <motion.div\n                key={video.id}\n                className=\"result-item\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 }}\n                onClick={() => handleVideoClick(video.id)}\n              >\n                <div className=\"result-thumbnail\">\n                  <img src={video.thumbnail} alt={video.title} loading=\"lazy\" />\n                  <div className=\"video-duration\">{video.duration}</div>\n                  <div className=\"play-overlay\">\n                    <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                      <path d=\"M8 5v14l11-7z\"/>\n                    </svg>\n                  </div>\n                </div>\n                \n                <div className=\"result-content\">\n                  <h3 className=\"result-title\">{video.title}</h3>\n                  <div className=\"result-meta\">\n                    <span className=\"result-channel\">{video.channel}</span>\n                    <span className=\"result-views\">{video.views} مشاهدة</span>\n                    <span className=\"result-date\">{formatDate(video.publishedAt)}</span>\n                  </div>\n                  <p className=\"result-description\">{video.description}</p>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      )}\n\n      {/* Empty State */}\n      {!isLoading && !error && searchResults.length === 0 && query && (\n        <div className=\"search-empty\">\n          <div className=\"empty-icon\">\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\n            </svg>\n          </div>\n          <h2>لا توجد نتائج</h2>\n          <p>لم نتمكن من العثور على أي فيديوهات تطابق بحثك</p>\n          <div className=\"search-suggestions\">\n            <h4>جرب:</h4>\n            <ul>\n              <li>التأكد من صحة الكتابة</li>\n              <li>استخدام كلمات مختلفة</li>\n              <li>استخدام كلمات أكثر عمومية</li>\n            </ul>\n          </div>\n        </div>\n      )}\n\n      {/* Search History */}\n      {!query && history.length > 0 && (\n        <div className=\"search-history\">\n          <h2>عمليات البحث السابقة</h2>\n          <div className=\"history-list\">\n            {history.slice(0, 10).map((item, index) => (\n              <motion.button\n                key={index}\n                className=\"history-item\"\n                onClick={() => handleSearchSubmit(item)}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ delay: index * 0.05 }}\n              >\n                <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z\"/>\n                </svg>\n                <span>{item}</span>\n              </motion.button>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Search;\n"], "names": ["_ref", "routeParams", "searchResults", "setSearchResults", "useState", "isLoading", "setIsLoading", "error", "setError", "search", "query", "history", "setSearch<PERSON>uery", "addToSearchHistory", "setStoreSearchResults", "useAppStore", "navigate", "useRouter", "initialQuery", "useEffect", "performSearch", "async", "searchQuery", "trim", "mockResults", "simulateYouTubeSearch", "err", "console", "Promise", "resolve", "setTimeout", "id", "title", "channel", "description", "duration", "views", "publishedAt", "thumbnail", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "floor", "_jsxs", "className", "children", "_jsx", "LoadingSpinner", "text", "width", "height", "viewBox", "fill", "d", "onClick", "length", "motion", "div", "initial", "opacity", "animate", "transition", "map", "video", "index", "y", "delay", "handleVideoClick", "videoId", "src", "alt", "loading", "slice", "item", "button", "<PERSON><PERSON><PERSON><PERSON>", "x"], "sourceRoot": ""}