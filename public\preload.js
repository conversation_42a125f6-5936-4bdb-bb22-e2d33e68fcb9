const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppInfo: () => ipcRenderer.invoke('get-app-info'),
  
  // Settings
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),
  loadSettings: () => ipcRenderer.invoke('load-settings'),
  
  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  
  // External links
  openExternal: (url) => ipcRenderer.invoke('open-external', url),
  
  // File dialogs
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  
  // Platform info
  platform: process.platform,
  
  // Event listeners
  onWindowStateChange: (callback) => {
    ipcRenderer.on('window-state-changed', callback);
    return () => ipcRenderer.removeListener('window-state-changed', callback);
  },
  
  // YouTube API helpers
  youtube: {
    extractVideoId: (url) => {
      const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
      const match = url.match(regex);
      return match ? match[1] : null;
    },
    
    generateThumbnail: (videoId, quality = 'mqdefault') => {
      return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`;
    },
    
    generateEmbedUrl: (videoId, params = {}) => {
      const defaultParams = {
        autoplay: 0,
        controls: 1,
        disablekb: 0,
        fs: 1,
        iv_load_policy: 3,
        modestbranding: 1,
        playsinline: 1,
        rel: 0,
        showinfo: 0,
        cc_load_policy: 0,
        hl: 'ar',
        cc_lang_pref: 'ar'
      };
      
      const finalParams = { ...defaultParams, ...params };
      const paramString = Object.entries(finalParams)
        .map(([key, value]) => `${key}=${value}`)
        .join('&');
      
      return `https://www.youtube.com/embed/${videoId}?${paramString}`;
    }
  },
  
  // Ad blocker utilities
  adBlocker: {
    isAdUrl: (url) => {
      const adPatterns = [
        /doubleclick\.net/,
        /googleadservices\.com/,
        /googlesyndication\.com/,
        /googletagmanager\.com/,
        /google-analytics\.com/,
        /facebook\.com\/tr/,
        /amazon-adsystem\.com/,
        /adsystem\.amazon/,
        /ads\.yahoo\.com/,
        /bing\.com\/ads/,
        /outbrain\.com/,
        /taboola\.com/,
        /adsense\.google\.com/,
        /youtube\.com\/api\/stats/,
        /youtube\.com\/ptracking/,
        /youtube\.com\/youtubei\/v1\/log_event/
      ];
      
      return adPatterns.some(pattern => pattern.test(url));
    }
  },
  
  // Storage utilities
  storage: {
    set: (key, value) => {
      try {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      } catch (error) {
        console.error('Storage set error:', error);
        return false;
      }
    },
    
    get: (key, defaultValue = null) => {
      try {
        const item = localStorage.getItem(key);
        return item ? JSON.parse(item) : defaultValue;
      } catch (error) {
        console.error('Storage get error:', error);
        return defaultValue;
      }
    },
    
    remove: (key) => {
      try {
        localStorage.removeItem(key);
        return true;
      } catch (error) {
        console.error('Storage remove error:', error);
        return false;
      }
    },
    
    clear: () => {
      try {
        localStorage.clear();
        return true;
      } catch (error) {
        console.error('Storage clear error:', error);
        return false;
      }
    }
  },
  
  // Utility functions
  utils: {
    formatDuration: (seconds) => {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);
      
      if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
      }
      return `${minutes}:${secs.toString().padStart(2, '0')}`;
    },
    
    formatViews: (views) => {
      if (views >= 1000000) {
        return `${(views / 1000000).toFixed(1)}M`;
      } else if (views >= 1000) {
        return `${(views / 1000).toFixed(1)}K`;
      }
      return views.toString();
    },
    
    formatDate: (dateString) => {
      const date = new Date(dateString);
      const now = new Date();
      const diffTime = Math.abs(now - date);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays === 1) {
        return 'أمس';
      } else if (diffDays < 7) {
        return `منذ ${diffDays} أيام`;
      } else if (diffDays < 30) {
        const weeks = Math.floor(diffDays / 7);
        return `منذ ${weeks} ${weeks === 1 ? 'أسبوع' : 'أسابيع'}`;
      } else if (diffDays < 365) {
        const months = Math.floor(diffDays / 30);
        return `منذ ${months} ${months === 1 ? 'شهر' : 'أشهر'}`;
      } else {
        const years = Math.floor(diffDays / 365);
        return `منذ ${years} ${years === 1 ? 'سنة' : 'سنوات'}`;
      }
    },
    
    debounce: (func, wait) => {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },
    
    throttle: (func, limit) => {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    }
  }
});

// Security: Remove any global Node.js objects
delete window.require;
delete window.exports;
delete window.module;
