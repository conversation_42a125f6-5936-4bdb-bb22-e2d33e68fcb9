{"version": 3, "file": "static/js/474.23431437.chunk.js", "mappings": "6LAKA,MA8BA,EA9BgBA,KACd,MAAM,MAAEC,IAAUC,EAAAA,EAAAA,KAElB,OACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,eAAcC,UAC3BC,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTJ,UAAU,oBACVK,QAAS,CAAEC,QAAS,EAAGC,EAAG,IAC1BC,QAAS,CAAEF,QAAS,EAAGC,EAAG,GAC1BE,WAAY,CAAEC,SAAU,IAAMT,SAAA,EAE9BF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,yEAEJC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,UACzBF,EAAAA,EAAAA,KAAA,OAAKY,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcb,UACjEF,EAAAA,EAAAA,KAAA,QAAMgB,EAAE,0OAGZhB,EAAAA,EAAAA,KAAA,MAAAE,SAAI,mGACJF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,4PACHF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BC,EAAAA,EAAAA,MAAA,KAAAD,SAAA,CAAG,wJAA4BF,EAAAA,EAAAA,KAAA,UAAAE,SAASJ,EAAMmB,6B", "sources": ["components/pages/History.js"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport './History.css';\n\nconst History = () => {\n  const { stats } = useAppStore();\n\n  return (\n    <div className=\"history-page\">\n      <motion.div \n        className=\"history-container\"\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.3 }}\n      >\n        <h1>سجل المشاهدة</h1>\n        \n        <div className=\"history-empty\">\n          <div className=\"empty-icon\">\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M13 3c-4.97 0-9 4.03-9 9H1l3.89 3.89.07.14L9 12H6c0-3.87 3.13-7 7-7s7 3.13 7 7-3.13 7-7 7c-1.93 0-3.68-.79-4.94-2.06l-1.42 1.42C8.27 19.99 10.51 21 13 21c4.97 0 9-4.03 9-9s-4.03-9-9-9zm-1 5v5l4.28 2.54.72-1.21-3.5-2.08V8H12z\"/>\n            </svg>\n          </div>\n          <h2>لا يوجد سجل مشاهدة</h2>\n          <p>ابدأ بمشاهدة الفيديوهات لرؤية سجل المشاهدة هنا</p>\n          <div className=\"stats-summary\">\n            <p>إجمالي الفيديوهات المشاهدة: <strong>{stats.videosWatched}</strong></p>\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default History;\n"], "names": ["History", "stats", "useAppStore", "_jsx", "className", "children", "_jsxs", "motion", "div", "initial", "opacity", "y", "animate", "transition", "duration", "width", "height", "viewBox", "fill", "d", "videosWatched"], "sourceRoot": ""}