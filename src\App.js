import React, { useEffect, Suspense, lazy } from 'react';
import { Helmet } from 'react-helmet-async';
import { motion, AnimatePresence } from 'framer-motion';
import useAppStore from './stores/appStore';
import useRouter from './services/router';
import Header from './components/layout/Header';
import Sidebar from './components/layout/Sidebar';
import LoadingSpinner from './components/common/LoadingSpinner';
import './App.css';

// Lazy load page components for better performance
const Home = lazy(() => import('./components/pages/HomeSimple'));
const Player = lazy(() => import('./components/pages/Player'));
const Search = lazy(() => import('./components/pages/Search'));
const Settings = lazy(() => import('./components/pages/Settings'));
const History = lazy(() => import('./components/pages/History'));
const Favorites = lazy(() => import('./components/pages/Favorites'));
const About = lazy(() => import('./components/pages/About'));

// Component mapping for routes
const routeComponents = {
  home: Home,
  player: Player,
  search: Search,
  settings: Settings,
  history: History,
  favorites: Favorites,
  about: About,
};

function App() {
  const {
    theme,
    language,
    sidebar,
    incrementSessions,
    incrementAdsBlocked,
    isLoading,
    error,
    clearError
  } = useAppStore();
  
  const { currentRoute, routeParams } = useRouter();

  // Initialize app on mount
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Increment session count
        incrementSessions();
        
        // Load saved settings if in Electron
        if (window.electronAPI) {
          try {
            const savedSettings = await window.electronAPI.loadSettings();
            if (savedSettings) {
              // Apply saved settings to store
              console.log('Loaded settings:', savedSettings);
            }
          } catch (error) {
            console.warn('Failed to load settings:', error);
          }
        }
        
        // Set up theme
        document.documentElement.setAttribute('data-theme', theme);
        
        // Set up language and direction
        document.documentElement.setAttribute('lang', language);
        document.documentElement.setAttribute('dir', language === 'ar' ? 'rtl' : 'ltr');
        
        console.log('App initialized successfully');
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();

    // Set up ad blocker statistics listener
    if (window.electronAPI?.onAdBlocked) {
      const unsubscribe = window.electronAPI.onAdBlocked((_, data) => {
        incrementAdsBlocked();
        console.log('Ad blocked:', data);
      });

      return unsubscribe;
    }
  }, [theme, language, incrementSessions, incrementAdsBlocked]);

  // Handle theme changes
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme);
    
    // Save theme preference
    if (window.electronAPI?.storage) {
      window.electronAPI.storage.set('theme', theme);
    } else {
      localStorage.setItem('youtube-player-pro-theme', theme);
    }
  }, [theme]);

  // Handle language changes
  useEffect(() => {
    document.documentElement.setAttribute('lang', language);
    document.documentElement.setAttribute('dir', language === 'ar' ? 'rtl' : 'ltr');
  }, [language]);

  // Get current page component
  const getCurrentPageComponent = () => {
    const Component = routeComponents[currentRoute];
    return Component || Home;
  };

  const CurrentPageComponent = getCurrentPageComponent();

  // Page transition variants
  const pageVariants = {
    initial: { 
      opacity: 0, 
      x: language === 'ar' ? 50 : -50,
      scale: 0.98
    },
    in: { 
      opacity: 1, 
      x: 0,
      scale: 1
    },
    out: { 
      opacity: 0, 
      x: language === 'ar' ? -50 : 50,
      scale: 0.98
    }
  };

  const pageTransition = {
    type: 'tween',
    ease: 'anticipate',
    duration: 0.4
  };

  return (
    <div className="app">
      <Helmet>
        <title>YouTube Player Pro - مشغل يوتيوب احترافي</title>
        <meta name="description" content="مشغل يوتيوب احترافي مع واجهة عربية متقدمة ومانع إعلانات قوي" />
        <meta name="theme-color" content={theme === 'dark' ? '#0f0f0f' : '#ffffff'} />
      </Helmet>

      {/* Header */}
      <Header />

      {/* Main content area */}
      <div className="app-body">
        {/* Sidebar */}
        <AnimatePresence>
          {sidebar.isOpen && (
            <motion.div
              className="sidebar-container"
              initial={{ x: language === 'ar' ? 300 : -300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: language === 'ar' ? 300 : -300, opacity: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
            >
              <Sidebar />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main content */}
        <main className={`main-content ${sidebar.isOpen ? 'with-sidebar' : 'full-width'}`}>
          {/* Error display */}
          {error && (
            <motion.div 
              className="error-banner"
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -50 }}
            >
              <div className="error-content">
                <span className="error-message">{error}</span>
                <button 
                  className="error-close"
                  onClick={clearError}
                  aria-label="إغلاق رسالة الخطأ"
                >
                  ×
                </button>
              </div>
            </motion.div>
          )}

          {/* Loading overlay */}
          {isLoading && (
            <motion.div 
              className="loading-overlay"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <LoadingSpinner />
            </motion.div>
          )}

          {/* Page content with transitions */}
          <AnimatePresence mode="wait">
            <motion.div
              key={currentRoute}
              className="page-container"
              initial="initial"
              animate="in"
              exit="out"
              variants={pageVariants}
              transition={pageTransition}
            >
              <Suspense fallback={<LoadingSpinner />}>
                <CurrentPageComponent routeParams={routeParams} />
              </Suspense>
            </motion.div>
          </AnimatePresence>
        </main>
      </div>

      {/* Global overlays */}
      <div id="modal-root" />
      <div id="tooltip-root" />
    </div>
  );
}

export default App;
