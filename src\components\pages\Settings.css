/* Settings page styles */
.settings-page {
  padding: var(--spacing-lg);
  max-width: 800px;
  margin: 0 auto;
}

.settings-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.settings-container h1 {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  text-align: center;
}

.settings-section {
  background-color: var(--surface-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-primary);
}

.settings-section h2 {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--spacing-lg) 0;
  border-bottom: 1px solid var(--border-primary);
  padding-bottom: var(--spacing-sm);
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.setting-item select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--surface-primary);
  color: var(--text-primary);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
}

.setting-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  accent-color: var(--primary-color);
}

.setting-item input[type="range"] {
  flex: 1;
  margin: 0 var(--spacing-sm);
}

.theme-options {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-sm);
}

.theme-option {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--surface-primary);
  color: var(--text-primary);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: var(--transition-fast);
}

.theme-option:hover {
  background-color: var(--surface-hover);
  border-color: var(--border-hover);
}

.theme-option.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.stats-display {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--surface-primary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.stat-item span:first-child {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.stat-item span:last-child {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.reset-button {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--error-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-family-primary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-fast);
}

.reset-button:hover {
  background-color: #dc2626;
}

.settings-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

/* Responsive design */
@media (max-width: 768px) {
  .settings-page {
    padding: var(--spacing-md);
  }
  
  .settings-section {
    padding: var(--spacing-md);
  }
  
  .theme-options {
    flex-direction: column;
  }
  
  .settings-actions {
    flex-direction: column;
  }
}

@media (max-width: 640px) {
  .settings-page {
    padding: var(--spacing-sm);
  }
  
  .settings-container h1 {
    font-size: var(--font-size-xl);
  }
  
  .settings-section h2 {
    font-size: var(--font-size-md);
  }
}
