// اختبار مانع الإعلانات
import { adBlocker } from './adBlocker';

export class AdBlockerTest {
  constructor() {
    this.testResults = [];
    this.testUrls = [
      // إعلانات YouTube
      'https://googleads.g.doubleclick.net/pagead/ads',
      'https://www.youtube.com/api/stats/ads',
      'https://www.youtube.com/ptracking',
      'https://www.youtube.com/pagead/conversion',
      
      // إعلانات عامة
      'https://www.googleadservices.com/pagead/conversion',
      'https://googlesyndication.com/safeframe',
      'https://doubleclick.net/instream/ad_status',
      
      // متتبعات
      'https://www.google-analytics.com/collect',
      'https://www.googletagmanager.com/gtm.js',
      
      // روابط عادية (يجب ألا تُحجب)
      'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
      'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
      'https://yt3.ggpht.com/channel/avatar.jpg'
    ];
  }

  // تشغيل جميع الاختبارات
  async runAllTests() {
    console.log('🧪 بدء اختبار مانع الإعلانات...');
    
    this.testResults = [];
    
    // اختبار فلترة الروابط
    await this.testUrlFiltering();
    
    // اختبار الإحصائيات
    await this.testStatistics();
    
    // اختبار التفعيل/الإلغاء
    await this.testToggle();
    
    // عرض النتائج
    this.displayResults();
    
    return this.testResults;
  }

  // اختبار فلترة الروابط
  async testUrlFiltering() {
    console.log('📋 اختبار فلترة الروابط...');
    
    for (const url of this.testUrls) {
      const shouldBlock = adBlocker.shouldBlock(url);
      const isAdUrl = this.isKnownAdUrl(url);
      
      const testResult = {
        url,
        shouldBlock,
        expected: isAdUrl,
        passed: shouldBlock === isAdUrl,
        type: 'url_filtering'
      };
      
      this.testResults.push(testResult);
      
      if (testResult.passed) {
        console.log(`✅ ${url} - ${shouldBlock ? 'محجوب' : 'مسموح'}`);
      } else {
        console.log(`❌ ${url} - متوقع: ${isAdUrl ? 'محجوب' : 'مسموح'}, الفعلي: ${shouldBlock ? 'محجوب' : 'مسموح'}`);
      }
    }
  }

  // اختبار الإحصائيات
  async testStatistics() {
    console.log('📊 اختبار الإحصائيات...');
    
    const initialCount = adBlocker.getBlockedCount();
    
    // محاكاة حجب بعض الإعلانات
    const adUrls = this.testUrls.filter(url => this.isKnownAdUrl(url));
    
    for (const url of adUrls) {
      adBlocker.blockAd(url);
    }
    
    const finalCount = adBlocker.getBlockedCount();
    const expectedIncrease = adUrls.length;
    const actualIncrease = finalCount - initialCount;
    
    const testResult = {
      type: 'statistics',
      initialCount,
      finalCount,
      expectedIncrease,
      actualIncrease,
      passed: actualIncrease >= expectedIncrease
    };
    
    this.testResults.push(testResult);
    
    if (testResult.passed) {
      console.log(`✅ الإحصائيات: زيادة ${actualIncrease} إعلان محجوب`);
    } else {
      console.log(`❌ الإحصائيات: متوقع ${expectedIncrease}، الفعلي ${actualIncrease}`);
    }
  }

  // اختبار التفعيل والإلغاء
  async testToggle() {
    console.log('🔄 اختبار التفعيل والإلغاء...');
    
    // تفعيل مانع الإعلانات
    adBlocker.enable();
    const enabledTest = {
      type: 'toggle_enable',
      enabled: adBlocker.isEnabled,
      passed: adBlocker.isEnabled === true
    };
    this.testResults.push(enabledTest);
    
    // إلغاء تفعيل مانع الإعلانات
    adBlocker.disable();
    const disabledTest = {
      type: 'toggle_disable',
      enabled: adBlocker.isEnabled,
      passed: adBlocker.isEnabled === false
    };
    this.testResults.push(disabledTest);
    
    // إعادة تفعيل
    adBlocker.enable();
    
    console.log(`${enabledTest.passed ? '✅' : '❌'} تفعيل مانع الإعلانات`);
    console.log(`${disabledTest.passed ? '✅' : '❌'} إلغاء تفعيل مانع الإعلانات`);
  }

  // فحص ما إذا كان الرابط إعلاني معروف
  isKnownAdUrl(url) {
    const adPatterns = [
      'doubleclick.net',
      'googleadservices.com',
      'googlesyndication.com',
      'google-analytics.com',
      'googletagmanager.com',
      '/api/stats/ads',
      '/ptracking',
      '/pagead/'
    ];
    
    return adPatterns.some(pattern => url.includes(pattern));
  }

  // عرض النتائج
  displayResults() {
    console.log('\n📋 نتائج الاختبار:');
    console.log('==================');
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(test => test.passed).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`إجمالي الاختبارات: ${totalTests}`);
    console.log(`نجح: ${passedTests} ✅`);
    console.log(`فشل: ${failedTests} ❌`);
    console.log(`معدل النجاح: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ الاختبارات الفاشلة:');
      this.testResults
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`- ${test.type}: ${test.url || 'اختبار عام'}`);
        });
    }
    
    // إحصائيات مانع الإعلانات
    const stats = adBlocker.getStats();
    console.log('\n📊 إحصائيات مانع الإعلانات:');
    console.log(`إجمالي الإعلانات المحجوبة: ${stats.totalBlocked}`);
    console.log(`الإعلانات المحجوبة اليوم: ${stats.todayBlocked}`);
    console.log(`النطاقات المحجوبة: ${adBlocker.blockedDomains.size}`);
  }

  // اختبار الأداء
  async testPerformance() {
    console.log('⚡ اختبار الأداء...');
    
    const testUrl = 'https://googleads.g.doubleclick.net/pagead/ads';
    const iterations = 1000;
    
    const startTime = performance.now();
    
    for (let i = 0; i < iterations; i++) {
      adBlocker.shouldBlock(testUrl);
    }
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    const avgTime = totalTime / iterations;
    
    console.log(`⚡ ${iterations} اختبار في ${totalTime.toFixed(2)}ms`);
    console.log(`⚡ متوسط الوقت: ${avgTime.toFixed(4)}ms لكل اختبار`);
    
    return {
      iterations,
      totalTime,
      avgTime,
      passed: avgTime < 1 // يجب أن يكون أقل من 1ms
    };
  }

  // اختبار تلقائي مستمر
  startContinuousTest() {
    console.log('🔄 بدء الاختبار المستمر...');
    
    setInterval(() => {
      this.runAllTests();
    }, 30000); // كل 30 ثانية
  }
}

// تصدير مثيل للاختبار
export const adBlockerTest = new AdBlockerTest();

// تشغيل الاختبار تلقائياً في وضع التطوير
if (process.env.NODE_ENV === 'development') {
  // تأخير لضمان تحميل مانع الإعلانات
  setTimeout(() => {
    adBlockerTest.runAllTests();
  }, 2000);
}
