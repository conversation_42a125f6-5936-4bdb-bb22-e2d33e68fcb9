<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#1a1a1a" />
    <meta name="description" content="مشغل يوتيوب احترافي مع واجهة عربية متقدمة ومانع إعلانات قوي" />
    <meta name="keywords" content="يوتيوب, مشغل, عربي, مانع إعلانات, فيديو" />
    <meta name="author" content="YouTube Player Pro Team" />
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="YouTube Player Pro - مشغل يوتيوب احترافي" />
    <meta property="og:description" content="مشغل يوتيوب احترافي مع واجهة عربية متقدمة ومانع إعلانات قوي" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.png" />
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="YouTube Player Pro - مشغل يوتيوب احترافي" />
    <meta name="twitter:description" content="مشغل يوتيوب احترافي مع واجهة عربية متقدمة ومانع إعلانات قوي" />
    <meta name="twitter:image" content="%PUBLIC_URL%/twitter-image.png" />
    
    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    
    <!-- Manifest -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://www.youtube.com" />
    <link rel="preconnect" href="https://img.youtube.com" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts - Arabic Support -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet" />
    
    <!-- CSS Variables for theming -->
    <style>
      :root {
        --primary-color: #ff0000;
        --primary-dark: #cc0000;
        --secondary-color: #1a1a1a;
        --background-color: #0f0f0f;
        --surface-color: #1a1a1a;
        --text-primary: #ffffff;
        --text-secondary: #aaaaaa;
        --border-color: #333333;
        --success-color: #00ff00;
        --warning-color: #ffaa00;
        --error-color: #ff4444;
        --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        --border-radius: 8px;
        --transition: all 0.3s ease;
        --font-family-primary: 'Cairo', 'Tajawal', sans-serif;
        --font-family-secondary: 'Tajawal', 'Cairo', sans-serif;
      }
      
      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }
      
      body {
        margin: 0;
        font-family: var(--font-family-primary);
        background-color: var(--background-color);
        color: var(--text-primary);
        direction: rtl;
        text-align: right;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      
      #root {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      /* Loading spinner */
      .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background-color: var(--background-color);
      }
      
      .spinner {
        width: 50px;
        height: 50px;
        border: 3px solid var(--border-color);
        border-top: 3px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Scrollbar styling */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: var(--surface-color);
      }
      
      ::-webkit-scrollbar-thumb {
        background: var(--border-color);
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: var(--text-secondary);
      }
      
      /* Focus styles for accessibility */
      *:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
      }
      
      /* Selection styles */
      ::selection {
        background-color: var(--primary-color);
        color: white;
      }
    </style>
    
    <title>YouTube Player Pro - مشغل يوتيوب احترافي</title>
  </head>
  <body>
    <noscript>
      <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
        <h1>يحتاج هذا التطبيق إلى JavaScript للعمل</h1>
        <p>يرجى تفعيل JavaScript في متصفحك وإعادة تحميل الصفحة</p>
      </div>
    </noscript>
    
    <!-- Loading indicator -->
    <div id="loading" class="loading-spinner">
      <div class="spinner"></div>
    </div>
    
    <div id="root"></div>
    
    <!-- Hide loading spinner when React loads -->
    <script>
      window.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
          const loading = document.getElementById('loading');
          if (loading) {
            loading.style.display = 'none';
          }
        }, 1000);
      });
    </script>
  </body>
</html>
