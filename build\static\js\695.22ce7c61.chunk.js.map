{"version": 3, "file": "static/js/695.22ce7c61.chunk.js", "mappings": "mNAOA,MA6RA,EA7RaA,KACX,MAAOC,EAAgBC,IAAqBC,EAAAA,EAAAA,UAAS,KAC9CC,EAAiBC,IAAsBF,EAAAA,EAAAA,WAAS,IAEjD,MAAEG,EAAK,aAAEC,IAAiBC,EAAAA,EAAAA,MAC1B,SAAEC,IAAaC,EAAAA,EAAAA,OAGrBC,EAAAA,EAAAA,WAAU,KACmBC,WACzB,IACEP,GAAmB,GACnB,MAAMQ,QAAeC,EAAAA,EAAWC,kBAAkB,KAAM,GACxDb,EAAkBW,EAAOG,OAC3B,CAAE,MAAOC,GACPC,QAAQD,MAAM,kCAAmCA,GAEjDf,EAAkB,CAChB,CACEiB,GAAI,cACJC,MAAO,0KACPC,QAAS,qBACTC,SAAU,OACVC,MAAO,OACPC,UAAW,wDAEb,CACEL,GAAI,cACJC,MAAO,4JACPC,QAAS,4EACTC,SAAU,OACVC,MAAO,OACPC,UAAW,wDAEb,CACEL,GAAI,cACJC,MAAO,oJACPC,QAAS,4EACTC,SAAU,OACVC,MAAO,OACPC,UAAW,yDAGjB,CAAC,QACCnB,GAAmB,EACrB,GAGFoB,IACC,IAEH,MAkBMC,EAAe,CACnBC,OAAQ,CAAEC,QAAS,EAAGC,EAAG,IACzBC,QAAS,CAAEF,QAAS,EAAGC,EAAG,IAG5B,OACEE,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVC,SAlBsB,CACxBR,OAAQ,CAAEC,QAAS,GACnBE,QAAS,CACPF,QAAS,EACTQ,WAAY,CACVC,gBAAiB,MAcnBC,QAAQ,SACRC,QAAQ,UAASC,SAAA,EAGjBT,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,eAAeC,SAAUT,EAAac,SAAA,EAC9DT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAACV,EAAAA,EAAOW,GAAE,CACRT,UAAU,aACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAC5B,6KAGDE,EAAAA,EAAAA,KAACV,EAAAA,EAAOa,EAAC,CACPX,UAAU,mBACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAC5B,qbAGDT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,eACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAAA,EAE3BT,EAAAA,EAAAA,MAACC,EAAAA,EAAOc,OAAM,CACZZ,UAAU,sBACVa,QArDcC,KACxBvC,EAAS,WAqDCwC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAOV,SAAA,EAE1BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,iPACJ,8DAGRd,EAAAA,EAAAA,KAACV,EAAAA,EAAOc,OAAM,CACZZ,UAAU,wBACVa,QAASA,IAAMtC,EAAS,SACxBwC,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAOV,SAC3B,4FAKLE,EAAAA,EAAAA,KAACV,EAAAA,EAAOC,IAAG,CACTC,UAAU,aACVI,QAAS,CAAEV,QAAS,EAAGsB,MAAO,IAC9BX,QAAS,CAAEX,QAAS,EAAGsB,MAAO,GAC9Bd,WAAY,CAAEQ,MAAO,IAAMJ,UAE3BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,qBAAoBM,SAAA,EACjCE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,uBAAsBM,UACnCE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uBAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,yBAAwBM,SAAA,EACrCE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,oBACfQ,EAAAA,EAAAA,KAAA,OAAKR,UAAU,oBACfQ,EAAAA,EAAAA,KAAA,OAAKR,UAAU,+BAOvBH,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,gBAAgBC,SAAUT,EAAac,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,4DAC9BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,aAAYM,SAAA,EACzBT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBM,UAC/BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uBAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAElC,EAAMmD,iBACrCf,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,yEAIjCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeM,UAC5BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,sGAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAElC,EAAMoD,cACrChB,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,yEAIjCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,iBAAgBM,UAC7BT,EAAAA,EAAAA,MAAA,OAAKqB,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,SAAA,EACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,qJACRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,mDAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAEmB,KAAKC,MAAMtD,EAAMuD,UAAY,OAC5DnB,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,qFAOrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,mBAAmBC,SAAUT,EAAac,SAAA,EAClEE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,oFAC7BpC,GACC2B,EAAAA,EAAAA,MAAA,OAAKG,UAAU,iBAAgBM,SAAA,EAC7BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,kBAAiBM,UAC9BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAEjBQ,EAAAA,EAAAA,KAAA,KAAAF,SAAG,2KAGLE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,cAAaM,SACzBvC,EAAe6D,IAAI,CAACC,EAAOC,KAC5BjC,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAETC,UAAU,aACVC,SAAUT,EACVuB,WAAY,CAAEC,MAAO,KAAMrB,GAAI,GAC/BkB,QAASA,KAAMkB,OAvKDC,EAuKkBH,EAAM5C,QAtKhDV,EAAS,SAAU,CAAEyD,YADGA,OAuK4B1B,SAAA,EAE1CT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,kBAAiBM,SAAA,EAC9BE,EAAAA,EAAAA,KAAA,OAAKyB,IAAKJ,EAAMvC,UAAW4C,IAAKL,EAAM3C,MAAOiD,QAAQ,UACrD3B,EAAAA,EAAAA,KAAA,OAAKR,UAAU,iBAAgBM,SAAEuB,EAAMzC,YACvCoB,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeM,UAC5BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,0BAIdzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,aAAYM,SAAA,EACzBE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,cAAaM,SAAEuB,EAAM3C,SACnCsB,EAAAA,EAAAA,KAAA,KAAGR,UAAU,gBAAeM,SAAEuB,EAAM1C,WACpCU,EAAAA,EAAAA,MAAA,KAAGG,UAAU,cAAaM,SAAA,CAAEuB,EAAMxC,MAAM,gDAlBrCwC,EAAM5C,WA2BnBY,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,mBAAmBC,SAAUT,EAAac,SAAA,EAClEE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,uGAC9BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,gBAAeM,SAAA,EAC5BT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,sGAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,4FAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,wRAKrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+KAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,wGAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,mPAKrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uDAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,uDAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,wQ,kCCgD/C,MACA,EADmB,IAvUnB,MACE8B,WAAAA,GAGEC,KAAKC,OAASC,CAAAA,SAAAA,aAAAA,WAAAA,IAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,2BAA6B,WACvDH,KAAKI,QAAU,wCAGfJ,KAAKK,UAAYH,CAAAA,SAAAA,aAAAA,WAAAA,IAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,0BAC7BH,KAAKM,SAAW,yCAClB,CAGA,kBAAMC,CAAaC,GAAyC,IAAlCC,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GAAIG,EAASH,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACrD,IACE,GAAIV,KAAKK,SACP,aAAaL,KAAKc,gBAAgBN,EAAOC,EAAYI,GAGvD,MAAME,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,UACNC,EAAGV,EACHW,KAAM,QACNV,WAAYA,EAAWW,WACvBC,IAAKrB,KAAKC,OACVqB,MAAO,YACPC,WAAY,WACZC,WAAY,KACZC,kBAAmB,OAGjBZ,GACFE,EAAOW,OAAO,YAAab,GAG7B,MAAMc,QAAiBC,MAAM,GAAG5B,KAAKI,kBAAkBW,KAEvD,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMC,QAAaL,EAASM,OAG5B,OAAOjC,KAAKkC,uBAAuBF,EACrC,CAAE,MAAOtF,GAGP,OAFAC,QAAQD,MAAM,wBAAyBA,GAEhCsD,KAAKmC,qBAAqB3B,EACnC,CACF,CAGA,qBAAM4B,CAAgBzC,GACpB,IACE,GAAIK,KAAKK,SACP,aAAaL,KAAKqC,yBAAyB1C,GAG7C,MAAMoB,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,oCACNrE,GAAI+C,EACJ0B,IAAKrB,KAAKC,SAGN0B,QAAiBC,MAAM,GAAG5B,KAAKI,kBAAkBW,KAEvD,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMC,QAAaL,EAASM,OAE5B,GAAID,EAAKM,OAASN,EAAKM,MAAM3B,OAAS,EACpC,OAAOX,KAAKuC,sBAAsBP,EAAKM,MAAM,IAG/C,MAAM,IAAIR,MAAM,kBAClB,CAAE,MAAOpF,GAEP,OADAC,QAAQD,MAAM,+BAAgCA,GACvCsD,KAAKwC,oBAAoB7C,EAClC,CACF,CAGA,qBAAMmB,CAAgBN,EAAOC,EAAYI,GACvC,IACE,MAAME,EAAS,IAAIC,gBAAgB,CACjCE,EAAGV,EACHC,WAAYA,EAAWW,WACvBD,KAAM,UAGJN,GACFE,EAAOW,OAAO,YAAab,GAG7B,MAAMc,QAAiBC,MAAM,GAAG5B,KAAKM,mBAAmBS,KAExD,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,oBAAoBH,EAASI,UAG/C,MAAMC,QAAaL,EAASM,OAC5B,OAAOjC,KAAKkC,uBAAuBF,EACrC,CAAE,MAAOtF,GAEP,OADAC,QAAQD,MAAM,sBAAuBA,GAC9BsD,KAAKmC,qBAAqB3B,EACnC,CACF,CAGA,8BAAM6B,CAAyB1C,GAC7B,IACE,MAAMgC,QAAiBC,MAAM,GAAG5B,KAAKM,kBAAkBX,KAEvD,IAAKgC,EAASE,GACZ,MAAM,IAAIC,MAAM,oBAAoBH,EAASI,UAG/C,MAAMC,QAAaL,EAASM,OAC5B,OAAOjC,KAAKuC,sBAAsBP,EACpC,CAAE,MAAOtF,GAEP,OADAC,QAAQD,MAAM,6BAA8BA,GACrCsD,KAAKwC,oBAAoB7C,EAClC,CACF,CAGAuC,sBAAAA,CAAuBF,GAAO,IAADS,EAC3B,IAAKT,EAAKM,MACR,MAAO,CAAE7F,OAAQ,GAAIiG,cAAe,KAAMC,aAAc,GAe1D,MAAO,CACLlG,OAbauF,EAAKM,MAAM/C,IAAIqD,IAAI,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,MAAK,CACrCpG,GAAIgG,EAAKhG,GAAG+C,SAAWiD,EAAKhG,GAC5BC,MAAO+F,EAAKK,QAAQpG,MACpBC,QAAS8F,EAAKK,QAAQC,aACtBC,YAAaP,EAAKK,QAAQE,YAC1BlG,WAAyC,QAA9B4F,EAAAD,EAAKK,QAAQG,WAAWC,cAAM,IAAAR,OAAA,EAA9BA,EAAgCS,OAAsC,QAAnCR,EAAIF,EAAKK,QAAQG,WAAWG,eAAO,IAAAT,OAAA,EAA/BA,EAAiCQ,KACnFE,YAAaZ,EAAKK,QAAQO,YAC1BzG,SAAUiD,KAAKyD,cAAiC,QAApBV,EAACH,EAAKc,sBAAc,IAAAX,OAAA,EAAnBA,EAAqBhG,UAClDC,MAAOgD,KAAK2D,YAA2B,QAAhBX,EAACJ,EAAKgB,kBAAU,IAAAZ,OAAA,EAAfA,EAAiBa,WACzCC,UAAWlB,EAAKK,QAAQa,aAKxBpB,cAAeV,EAAKU,eAAiB,KACrCC,cAA2B,QAAbF,EAAAT,EAAK+B,gBAAQ,IAAAtB,OAAA,EAAbA,EAAeE,eAAgB,EAEjD,CAGAJ,qBAAAA,CAAsBK,GAAO,IAADoB,EAAAC,EAC1B,MAAO,CACLrH,GAAIgG,EAAKhG,GACTC,MAAO+F,EAAKK,QAAQpG,MACpBC,QAAS8F,EAAKK,QAAQC,aACtBC,YAAaP,EAAKK,QAAQE,YAC1BlG,WAAyC,QAA9B+G,EAAApB,EAAKK,QAAQG,WAAWc,cAAM,IAAAF,OAAA,EAA9BA,EAAgCV,OAAmC,QAAhCW,EAAIrB,EAAKK,QAAQG,WAAWe,YAAI,IAAAF,OAAA,EAA5BA,EAA8BX,KAChFE,YAAaZ,EAAKK,QAAQO,YAC1BzG,SAAUiD,KAAKyD,cAAcb,EAAKc,eAAe3G,UACjDC,MAAOgD,KAAK2D,YAAYf,EAAKgB,WAAWC,WACxCO,MAAOpE,KAAK2D,YAAYf,EAAKgB,WAAWS,WACxCP,UAAWlB,EAAKK,QAAQa,UACxBQ,KAAM1B,EAAKK,QAAQqB,MAAQ,GAE/B,CAGAb,aAAAA,CAAc1G,GACZ,IAAKA,EAAU,OAAO,EAEtB,MAAMwH,EAAQxH,EAASwH,MAAM,2BAC7B,IAAKA,EAAO,OAAO,EAMnB,OAAe,MAJDC,SAASD,EAAM,KAAO,GAIJ,IAHhBC,SAASD,EAAM,KAAO,IACtBC,SAASD,EAAM,KAAO,EAGxC,CAGAZ,WAAAA,CAAYE,GACV,IAAKA,EAAW,MAAO,IAEvB,MAAMY,EAAQD,SAASX,GACvB,OAAIY,GAAS,IACJ,IAAIA,EAAQ,KAASC,QAAQ,MAC3BD,GAAS,IACX,IAAIA,EAAQ,KAAMC,QAAQ,MAE5BD,EAAMrD,UACf,CAGAuD,cAAAA,CAAeC,GACb,IAAKA,EAAS,MAAO,OAErB,MAAMC,EAAQzF,KAAKC,MAAMuF,EAAU,MAC7BE,EAAU1F,KAAKC,MAAOuF,EAAU,KAAQ,IACxCG,EAAOH,EAAU,GAEvB,OAAIC,EAAQ,EACH,GAAGA,KAASC,EAAQ1D,WAAW4D,SAAS,EAAG,QAAQD,EAAK3D,WAAW4D,SAAS,EAAG,OAEjF,GAAGF,KAAWC,EAAK3D,WAAW4D,SAAS,EAAG,MACnD,CAGA7C,oBAAAA,CAAqB3B,GACnB,MAAMyE,EAAa,CACjB,CACErI,GAAI,cACJC,MAAO,GAAG2D,yGACV1D,QAAS,sEACTqG,YAAa,sMACblG,UAAW,uDACXF,SAAU,OACVC,MAAO,OACPwG,YAAa,uBACbM,UAAW,eAEb,CACElH,GAAI,cACJC,MAAO,sBAAO2D,qDACd1D,QAAS,sEACTqG,YAAa,yLACblG,UAAW,uDACXF,SAAU,OACVC,MAAO,OACPwG,YAAa,uBACbM,UAAW,eAEb,CACElH,GAAI,cACJC,MAAO,4BAAQ2D,4DACf1D,QAAS,kFACTqG,YAAa,oMACblG,UAAW,uDACXF,SAAU,OACVC,MAAO,OACPwG,YAAa,uBACbM,UAAW,gBAIf,MAAO,CACLrH,OAAQwI,EACRvC,cAAe,KACfC,aAAcsC,EAAWtE,OAE7B,CAGA6B,mBAAAA,CAAoB7C,GAClB,MAAO,CACL/C,GAAI+C,EACJ9C,MAAO,2GACPC,QAAS,sEACTqG,YAAa,wGACblG,UAAW,8BAA8B0C,sBACzC5C,SAAU,OACVC,MAAO,OACPoH,MAAO,MACPZ,YAAa,uBACbM,UAAW,cACXQ,KAAM,CAAC,uCAAU,iCAAS,wCAE9B,CAGA,uBAAM9H,GAAuD,IAArCgF,EAAUd,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,KAAMD,EAAUC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,GACtD,IACE,GAAIV,KAAKK,SACP,aAAaL,KAAKkF,qBAAqB1D,EAAYf,GAGrD,MAAMM,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,oCACNkE,MAAO,cACP3D,aACAf,WAAYA,EAAWW,WACvBC,IAAKrB,KAAKC,OACVmF,gBAAiB,MAGbzD,QAAiBC,MAAM,GAAG5B,KAAKI,kBAAkBW,KAEvD,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMC,QAAaL,EAASM,OAC5B,OAAOjC,KAAKkC,uBAAuBF,EACrC,CAAE,MAAOtF,GAEP,OADAC,QAAQD,MAAM,0BAA2BA,GAClCsD,KAAKmC,qBAAqB,4EACnC,CACF,CAGA,0BAAM+C,CAAqB1D,EAAYf,GACrC,IACE,MAAMM,EAAS,IAAIC,gBAAgB,CACjCQ,aACAf,WAAYA,EAAWW,aAGnBO,QAAiBC,MAAM,GAAG5B,KAAKM,qBAAqBS,KAE1D,IAAKY,EAASE,GACZ,MAAM,IAAIC,MAAM,oBAAoBH,EAASI,UAG/C,MAAMC,QAAaL,EAASM,OAC5B,OAAOjC,KAAKkC,uBAAuBF,EACrC,CAAE,MAAOtF,GAEP,OADAC,QAAQD,MAAM,wBAAyBA,GAChCsD,KAAKmC,qBAAqB,4EACnC,CACF,E", "sources": ["components/pages/Home.js", "services/youtubeAPI.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport useRouter from '../../services/router';\nimport youtubeAPI from '../../services/youtubeAPI';\nimport './Home.css';\n\nconst Home = () => {\n  const [featuredVideos, setFeaturedVideos] = useState([]);\n  const [isLoadingVideos, setIsLoadingVideos] = useState(true);\n\n  const { stats, currentVideo } = useAppStore();\n  const { navigate } = useRouter();\n\n  // Load trending videos on component mount\n  useEffect(() => {\n    const loadTrendingVideos = async () => {\n      try {\n        setIsLoadingVideos(true);\n        const result = await youtubeAPI.getTrendingVideos('SA', 6);\n        setFeaturedVideos(result.videos);\n      } catch (error) {\n        console.error('Failed to load trending videos:', error);\n        // Fallback to mock data\n        setFeaturedVideos([\n          {\n            id: 'dQw4w9WgXcQ',\n            title: 'مرحباً بك في مشغل يوتيوب المتقدم',\n            channel: 'YouTube Player Pro',\n            duration: '3:32',\n            views: '1.2M',\n            thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg'\n          },\n          {\n            id: 'kJQP7kiw5Fk',\n            title: 'كيفية استخدام مانع الإعلانات',\n            channel: 'دليل المستخدم',\n            duration: '4:42',\n            views: '850K',\n            thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg'\n          },\n          {\n            id: 'fJ9rUzIMcZQ',\n            title: 'الميزات الجديدة في الإصدار 2.0',\n            channel: 'أخبار التطبيق',\n            duration: '5:55',\n            views: '2.1M',\n            thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg'\n          }\n        ]);\n      } finally {\n        setIsLoadingVideos(false);\n      }\n    };\n\n    loadTrendingVideos();\n  }, []);\n\n  const handleVideoClick = (videoId) => {\n    navigate('player', { videoId });\n  };\n\n  const handleSearchClick = () => {\n    navigate('search');\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 }\n  };\n\n  return (\n    <motion.div \n      className=\"home-page\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Hero Section */}\n      <motion.section className=\"hero-section\" variants={itemVariants}>\n        <div className=\"hero-content\">\n          <motion.h1 \n            className=\"hero-title\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.2 }}\n          >\n            مرحباً بك في مشغل يوتيوب المتقدم\n          </motion.h1>\n          <motion.p \n            className=\"hero-description\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4 }}\n          >\n            استمتع بمشاهدة فيديوهات يوتيوب بدون إعلانات مع واجهة عربية متقدمة وميزات احترافية\n          </motion.p>\n          <motion.div \n            className=\"hero-actions\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6 }}\n          >\n            <motion.button\n              className=\"hero-button primary\"\n              onClick={handleSearchClick}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\n              </svg>\n              ابدأ البحث\n            </motion.button>\n            <motion.button\n              className=\"hero-button secondary\"\n              onClick={() => navigate('about')}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              تعرف على المزيد\n            </motion.button>\n          </motion.div>\n        </div>\n        <motion.div \n          className=\"hero-image\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ delay: 0.8 }}\n        >\n          <div className=\"hero-video-preview\">\n            <div className=\"video-preview-screen\">\n              <svg width=\"80\" height=\"80\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M8 5v14l11-7z\"/>\n              </svg>\n            </div>\n            <div className=\"video-preview-controls\">\n              <div className=\"control-button\"></div>\n              <div className=\"control-button\"></div>\n              <div className=\"control-button\"></div>\n            </div>\n          </div>\n        </motion.div>\n      </motion.section>\n\n      {/* Stats Section */}\n      <motion.section className=\"stats-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">إحصائياتك</h2>\n        <div className=\"stats-grid\">\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon videos\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M8 5v14l11-7z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{stats.videosWatched}</span>\n              <span className=\"stat-label\">فيديو مشاهد</span>\n            </div>\n          </motion.div>\n\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon ads\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{stats.adsBlocked}</span>\n              <span className=\"stat-label\">إعلان محجوب</span>\n            </div>\n          </motion.div>\n\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon time\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z\"/>\n                <path d=\"M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{Math.floor(stats.timeSpent / 60)}</span>\n              <span className=\"stat-label\">دقيقة مشاهدة</span>\n            </div>\n          </motion.div>\n        </div>\n      </motion.section>\n\n      {/* Featured Videos */}\n      <motion.section className=\"featured-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">فيديوهات مميزة</h2>\n        {isLoadingVideos ? (\n          <div className=\"videos-loading\">\n            <div className=\"loading-spinner\">\n              <div className=\"spinner\"></div>\n            </div>\n            <p>جاري تحميل الفيديوهات المميزة...</p>\n          </div>\n        ) : (\n          <div className=\"videos-grid\">\n            {featuredVideos.map((video, index) => (\n            <motion.div\n              key={video.id}\n              className=\"video-card\"\n              variants={itemVariants}\n              whileHover={{ scale: 1.02, y: -4 }}\n              onClick={() => handleVideoClick(video.id)}\n            >\n              <div className=\"video-thumbnail\">\n                <img src={video.thumbnail} alt={video.title} loading=\"lazy\" />\n                <div className=\"video-duration\">{video.duration}</div>\n                <div className=\"video-overlay\">\n                  <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                    <path d=\"M8 5v14l11-7z\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"video-info\">\n                <h3 className=\"video-title\">{video.title}</h3>\n                <p className=\"video-channel\">{video.channel}</p>\n                <p className=\"video-views\">{video.views} مشاهدة</p>\n              </div>\n            </motion.div>\n          ))}\n          </div>\n        )}\n      </motion.section>\n\n      {/* Features Section */}\n      <motion.section className=\"features-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">المميزات الرئيسية</h2>\n        <div className=\"features-grid\">\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">مانع إعلانات قوي</h3>\n            <p className=\"feature-description\">\n              يحجب جميع أنواع الإعلانات لتجربة مشاهدة سلسة ومريحة\n            </p>\n          </motion.div>\n\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">واجهة عربية متقدمة</h3>\n            <p className=\"feature-description\">\n              تصميم عربي كامل مع دعم RTL وثيم داكن مريح للعينين\n            </p>\n          </motion.div>\n\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">أداء محسن</h3>\n            <p className=\"feature-description\">\n              تشغيل سريع وسلس مع استهلاك أقل للموارد والذاكرة\n            </p>\n          </motion.div>\n        </div>\n      </motion.section>\n    </motion.div>\n  );\n};\n\nexport default Home;\n", "// YouTube Data API service\n// This service handles all interactions with YouTube's real API\n\nclass YouTubeAPIService {\n  constructor() {\n    // For production, you would get this from environment variables\n    // For now, we'll use a demo key or implement a proxy server\n    this.apiKey = process.env.REACT_APP_YOUTUBE_API_KEY || 'DEMO_KEY';\n    this.baseURL = 'https://www.googleapis.com/youtube/v3';\n    \n    // Fallback to a proxy server if no API key is available\n    this.useProxy = !process.env.REACT_APP_YOUTUBE_API_KEY;\n    this.proxyURL = 'https://youtube-proxy-api.herokuapp.com'; // Example proxy\n  }\n\n  // Search for videos using YouTube Data API\n  async searchVideos(query, maxResults = 25, pageToken = '') {\n    try {\n      if (this.useProxy) {\n        return await this.searchWithProxy(query, maxResults, pageToken);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet',\n        q: query,\n        type: 'video',\n        maxResults: maxResults.toString(),\n        key: this.apiKey,\n        order: 'relevance',\n        safeSearch: 'moderate',\n        regionCode: 'SA', // Saudi Arabia for Arabic content\n        relevanceLanguage: 'ar'\n      });\n\n      if (pageToken) {\n        params.append('pageToken', pageToken);\n      }\n\n      const response = await fetch(`${this.baseURL}/search?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      \n      // Transform the data to our format\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('YouTube search error:', error);\n      // Fallback to mock data if API fails\n      return this.getMockSearchResults(query);\n    }\n  }\n\n  // Get video details by ID\n  async getVideoDetails(videoId) {\n    try {\n      if (this.useProxy) {\n        return await this.getVideoDetailsWithProxy(videoId);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet,statistics,contentDetails',\n        id: videoId,\n        key: this.apiKey\n      });\n\n      const response = await fetch(`${this.baseURL}/videos?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      \n      if (data.items && data.items.length > 0) {\n        return this.transformVideoDetails(data.items[0]);\n      }\n      \n      throw new Error('Video not found');\n    } catch (error) {\n      console.error('YouTube video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Search with proxy server (fallback method)\n  async searchWithProxy(query, maxResults, pageToken) {\n    try {\n      const params = new URLSearchParams({\n        q: query,\n        maxResults: maxResults.toString(),\n        type: 'video'\n      });\n\n      if (pageToken) {\n        params.append('pageToken', pageToken);\n      }\n\n      const response = await fetch(`${this.proxyURL}/search?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`Proxy API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('Proxy search error:', error);\n      return this.getMockSearchResults(query);\n    }\n  }\n\n  // Get video details with proxy\n  async getVideoDetailsWithProxy(videoId) {\n    try {\n      const response = await fetch(`${this.proxyURL}/video/${videoId}`);\n      \n      if (!response.ok) {\n        throw new Error(`Proxy API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformVideoDetails(data);\n    } catch (error) {\n      console.error('Proxy video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Transform YouTube API search results to our format\n  transformSearchResults(data) {\n    if (!data.items) {\n      return { videos: [], nextPageToken: null, totalResults: 0 };\n    }\n\n    const videos = data.items.map(item => ({\n      id: item.id.videoId || item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails?.duration),\n      views: this.formatViews(item.statistics?.viewCount),\n      channelId: item.snippet.channelId\n    }));\n\n    return {\n      videos,\n      nextPageToken: data.nextPageToken || null,\n      totalResults: data.pageInfo?.totalResults || 0\n    };\n  }\n\n  // Transform video details to our format\n  transformVideoDetails(item) {\n    return {\n      id: item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.maxres?.url || item.snippet.thumbnails.high?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails.duration),\n      views: this.formatViews(item.statistics.viewCount),\n      likes: this.formatViews(item.statistics.likeCount),\n      channelId: item.snippet.channelId,\n      tags: item.snippet.tags || []\n    };\n  }\n\n  // Parse ISO 8601 duration to seconds\n  parseDuration(duration) {\n    if (!duration) return 0;\n    \n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/);\n    if (!match) return 0;\n    \n    const hours = parseInt(match[1]) || 0;\n    const minutes = parseInt(match[2]) || 0;\n    const seconds = parseInt(match[3]) || 0;\n    \n    return hours * 3600 + minutes * 60 + seconds;\n  }\n\n  // Format view count\n  formatViews(viewCount) {\n    if (!viewCount) return '0';\n    \n    const count = parseInt(viewCount);\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  }\n\n  // Format duration from seconds to MM:SS or HH:MM:SS\n  formatDuration(seconds) {\n    if (!seconds) return '0:00';\n    \n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n    \n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n  // Mock search results (fallback)\n  getMockSearchResults(query) {\n    const mockVideos = [\n      {\n        id: 'dQw4w9WgXcQ',\n        title: `${query} - نتيجة البحث الأولى`,\n        channel: 'قناة تجريبية',\n        description: 'وصف مفصل للفيديو الأول في نتائج البحث...',\n        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',\n        duration: '3:32',\n        views: '1.2M',\n        publishedAt: '2023-01-15T10:00:00Z',\n        channelId: 'UC123456789'\n      },\n      {\n        id: 'kJQP7kiw5Fk',\n        title: `شرح ${query} بالتفصيل`,\n        channel: 'قناة التعليم',\n        description: 'شرح شامل ومفصل حول الموضوع المطلوب...',\n        thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg',\n        duration: '4:42',\n        views: '850K',\n        publishedAt: '2023-02-20T15:30:00Z',\n        channelId: 'UC987654321'\n      },\n      {\n        id: 'fJ9rUzIMcZQ',\n        title: `أفضل ${query} لهذا العام`,\n        channel: 'قناة المراجعات',\n        description: 'مراجعة شاملة لأفضل الخيارات المتاحة...',\n        thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',\n        duration: '5:55',\n        views: '2.1M',\n        publishedAt: '2023-03-10T12:15:00Z',\n        channelId: 'UC456789123'\n      }\n    ];\n\n    return {\n      videos: mockVideos,\n      nextPageToken: null,\n      totalResults: mockVideos.length\n    };\n  }\n\n  // Mock video details (fallback)\n  getMockVideoDetails(videoId) {\n    return {\n      id: videoId,\n      title: 'فيديو يوتيوب تجريبي',\n      channel: 'قناة تجريبية',\n      description: 'وصف تجريبي للفيديو...',\n      thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n      duration: '3:45',\n      views: '1.5M',\n      likes: '25K',\n      publishedAt: '2023-01-01T00:00:00Z',\n      channelId: 'UC123456789',\n      tags: ['تجريبي', 'فيديو', 'يوتيوب']\n    };\n  }\n\n  // Get trending videos\n  async getTrendingVideos(regionCode = 'SA', maxResults = 25) {\n    try {\n      if (this.useProxy) {\n        return await this.getTrendingWithProxy(regionCode, maxResults);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet,statistics,contentDetails',\n        chart: 'mostPopular',\n        regionCode,\n        maxResults: maxResults.toString(),\n        key: this.apiKey,\n        videoCategoryId: '0' // All categories\n      });\n\n      const response = await fetch(`${this.baseURL}/videos?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('YouTube trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n\n  // Get trending with proxy\n  async getTrendingWithProxy(regionCode, maxResults) {\n    try {\n      const params = new URLSearchParams({\n        regionCode,\n        maxResults: maxResults.toString()\n      });\n\n      const response = await fetch(`${this.proxyURL}/trending?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`Proxy API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('Proxy trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n}\n\n// Create and export a singleton instance\nconst youtubeAPI = new YouTubeAPIService();\nexport default youtubeAPI;\n"], "names": ["Home", "featured<PERSON><PERSON><PERSON>", "setFeaturedVideos", "useState", "isLoadingVideos", "setIsLoadingVideos", "stats", "currentVideo", "useAppStore", "navigate", "useRouter", "useEffect", "async", "result", "youtubeAPI", "getTrendingVideos", "videos", "error", "console", "id", "title", "channel", "duration", "views", "thumbnail", "loadTrendingVideos", "itemVariants", "hidden", "opacity", "y", "visible", "_jsxs", "motion", "div", "className", "variants", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "initial", "animate", "children", "section", "_jsx", "h1", "delay", "p", "button", "onClick", "handleSearchClick", "whileHover", "scale", "whileTap", "width", "height", "viewBox", "fill", "d", "videosWatched", "adsBlocked", "Math", "floor", "timeSpent", "map", "video", "index", "handleVideoClick", "videoId", "src", "alt", "loading", "constructor", "this", "<PERSON><PERSON><PERSON><PERSON>", "process", "REACT_APP_YOUTUBE_API_KEY", "baseURL", "useProxy", "proxyURL", "searchVideos", "query", "maxResults", "arguments", "length", "undefined", "pageToken", "searchWithProxy", "params", "URLSearchParams", "part", "q", "type", "toString", "key", "order", "safeSearch", "regionCode", "relevanceLanguage", "append", "response", "fetch", "ok", "Error", "status", "data", "json", "transformSearchResults", "getMockSearchResults", "getVideoDetails", "getVideoDetailsWithProxy", "items", "transformVideoDetails", "getMockVideoDetails", "_data$pageInfo", "nextPageToken", "totalResults", "item", "_item$snippet$thumbna", "_item$snippet$thumbna2", "_item$contentDetails", "_item$statistics", "snippet", "channelTitle", "description", "thumbnails", "medium", "url", "default", "publishedAt", "parseDuration", "contentDetails", "formatViews", "statistics", "viewCount", "channelId", "pageInfo", "_item$snippet$thumbna3", "_item$snippet$thumbna4", "maxres", "high", "likes", "likeCount", "tags", "match", "parseInt", "count", "toFixed", "formatDuration", "seconds", "hours", "minutes", "secs", "padStart", "mockVideos", "getTrendingWithProxy", "chart", "videoCategoryId"], "sourceRoot": ""}