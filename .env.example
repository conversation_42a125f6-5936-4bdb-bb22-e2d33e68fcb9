# YouTube Player Pro Environment Variables

# YouTube Data API Key
# Get your API key from: https://console.developers.google.com/
# Enable YouTube Data API v3 for your project
REACT_APP_YOUTUBE_API_KEY=your_youtube_api_key_here

# Optional: Custom proxy server for YouTube API
# If you don't have an API key, you can use a proxy server
REACT_APP_YOUTUBE_PROXY_URL=https://your-proxy-server.com

# App Configuration
REACT_APP_APP_NAME=YouTube Player Pro
REACT_APP_VERSION=2.0.0

# Development Settings
REACT_APP_DEBUG=false
REACT_APP_MOCK_DATA=false

# Analytics (Optional)
REACT_APP_ANALYTICS_ID=your_analytics_id_here

# Instructions:
# 1. Copy this file to .env
# 2. Replace the placeholder values with your actual API keys
# 3. Restart the development server after making changes
# 4. Never commit the .env file to version control
