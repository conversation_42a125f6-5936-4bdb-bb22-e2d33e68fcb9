{"version": 3, "file": "static/js/906.51419996.chunk.js", "mappings": "yMAOA,MAgUA,EAhUeA,IAA2B,IAA1B,YAAEC,EAAc,CAAC,GAAGD,EAClC,MAAOE,EAAeC,IAAoBC,EAAAA,EAAAA,WAAS,IAC5CC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,MACzCG,GAAYC,EAAAA,EAAAA,QAAO,MACnBC,GAAoBD,EAAAA,EAAAA,QAAO,OAE3B,aACJE,EAAY,gBACZC,EAAe,iBACfC,EAAgB,uBAChBC,EAAsB,SACtBC,IACEC,EAAAA,EAAAA,KAEEC,GAAqB,OAAXf,QAAW,IAAXA,OAAW,EAAXA,EAAae,UAAWN,EAAaO,GAE/CC,GAAmBC,EAAAA,EAAAA,aAAYC,UACnC,GAAKJ,EAEL,IACE,MAAMK,QAAgBC,EAAAA,EAAWC,gBAAgBP,GACjDL,EAAgB,CACdM,GAAID,EACJQ,MAAOH,EAAQG,MACfC,QAASJ,EAAQI,QACjBC,SAAUL,EAAQK,SAClBC,UAAWN,EAAQM,UACnBC,YAAaP,EAAQO,YACrBC,MAAOR,EAAQQ,MACfC,YAAaT,EAAQS,aAEzB,CAAE,MAAOC,GACPC,QAAQD,MAAM,gCAAiCA,EACjD,GACC,CAACf,EAASL,KAEbsB,EAAAA,EAAAA,WAAU,KACJjB,IACFkB,IACAhB,MAED,CAACF,EAASE,IAIb,MAAMgB,EAAiBA,KACrB,GAAKC,OAAOC,GAUVC,QAVc,CACd,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAM,qCACbH,EAAOlB,OAAQ,EACfmB,SAASG,KAAKC,YAAYL,GAE1BH,OAAOS,wBAA0B,KAC/BP,IAEJ,GAKIA,EAAmBA,KAAO,IAADQ,EAC7B,GAAItC,EAAUuC,SAAoB,QAAbD,EAAIV,OAAOC,UAAE,IAAAS,GAATA,EAAWE,OAClC,IACEtC,EAAkBqC,QAAU,IAAIX,OAAOC,GAAGW,OAAOxC,EAAUuC,QAAS,CAClEE,OAAQ,OACRC,MAAO,OACPjC,QAASA,EACTkC,WAAY,CACVC,SAAUrC,EAASqC,SAAW,EAAI,EAClCC,SAAUtC,EAASuC,aAAe,EAAI,EACtCC,UAAWxC,EAASuC,aAAe,EAAI,EACvCE,GAAI,EACJC,eAAgB,EAChBC,eAAgB,EAChBC,YAAa,EACbC,IAAK,EACLC,SAAU,EACVC,eAAgB,EAChBC,GAAI,KACJC,aAAc,KACdC,OAAQ7B,OAAO8B,SAASD,QAE1BE,OAAQ,CACNC,QAASC,EACTC,cAAeC,EACfC,QAASC,IAGf,CAAE,MAAOzC,GACPC,QAAQD,MAAM,qCAAsCA,GACpDzB,EAAe,qIACjB,GAIE8D,EAAiBK,IACrBtE,GAAiB,GACjBG,EAAe,WAGSoE,IAApB5D,EAAS6D,QACXF,EAAMG,OAAOC,UAAU/D,EAAS6D,QAIlC,MAAMG,EAAYL,EAAMG,OAAOG,eAC/BpE,EAAgB,CACdM,GAAID,EACJQ,MAAOsD,EAAUtD,OAAS,sEAC1BC,QAASqD,EAAUE,QAAU,gEAC7BtD,SAAU+C,EAAMG,OAAOK,eAAiB,EACxCC,YAAa,EACbC,WAAW,EACXC,UAAU,EACVC,aAAa,EACbC,QAASb,EAAMG,OAAOW,sBAAwB,OAC9CZ,OAAQF,EAAMG,OAAOY,aAAe,IACpCC,MAAOhB,EAAMG,OAAOc,YAAa,EACjCC,YAAY,IAGd3D,QAAQ4D,IAAI,kCAAmC5E,IAG3CsD,EAAuBG,IAC3B,MAAMoB,EAAQpB,EAAMqB,KACdC,EAAStB,EAAMG,OAErB,OAAQiB,GACN,KAAK1D,OAAOC,GAAG4D,YAAYC,QACzBrF,EAAiB,CACfuE,WAAW,EACXC,UAAU,EACVC,aAAa,IAEf,MAEF,KAAKlD,OAAOC,GAAG4D,YAAYE,OACzBtF,EAAiB,CACfuE,WAAW,EACXC,UAAU,EACVC,aAAa,IAEf,MAEF,KAAKlD,OAAOC,GAAG4D,YAAYG,UACzBvF,EAAiB,CACfyE,aAAa,IAEf,MAEF,KAAKlD,OAAOC,GAAG4D,YAAYI,MACzBxF,EAAiB,CACfuE,WAAW,EACXC,UAAU,EACVC,aAAa,EACbH,YAAaa,EAAOd,gBAEtBpE,IACA,MAEF,KAAKsB,OAAOC,GAAG4D,YAAYK,KACzBzF,EAAiB,CACfuE,WAAW,EACXC,UAAU,EACVC,aAAa,EACbH,YAAa,IAMfa,EAAOO,gBACT1F,EAAiB,CACfsE,YAAaa,EAAOO,oBAKpB9B,EAAiBC,IACrB,MAAM8B,EAAY9B,EAAMqB,KACxB,IAAIU,EAAe,+HAEnB,OAAQD,GACN,KAAK,EACHC,EAAe,kHACf,MACF,KAAK,EACHA,EAAe,iEACf,MACF,KAAK,IACHA,EAAe,2IACf,MACF,KAAK,IACL,KAAK,IACHA,EAAe,kOAInBlG,EAAekG,GACfxE,QAAQD,MAAM,wBAAyBwE,EAAWC,IAcpD,OAAKxF,GAiBHyF,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,cACVC,QAAS,CAAEC,QAAS,GACpBC,QAAS,CAAED,QAAS,GACpBE,WAAY,CAAEtF,SAAU,IAAMuF,UAE9BC,EAAAA,EAAAA,MAAA,OAAKN,UAAU,mBAAkBK,SAAA,EAC/BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iBAAgBK,SAC5B5G,GACC6G,EAAAA,EAAAA,MAAA,OAAKN,UAAU,eAAcK,SAAA,EAC3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,aAAYK,UACzBR,EAAAA,EAAAA,KAAA,OAAKxD,MAAM,KAAKD,OAAO,KAAKmE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,+HAGZZ,EAAAA,EAAAA,KAAA,MAAAQ,SAAI,+GACJR,EAAAA,EAAAA,KAAA,KAAAQ,SAAI5G,KACJ6G,EAAAA,EAAAA,MAAA,UAAQN,UAAU,eAAeU,QA7CzBC,KAClBjH,EAAe,MACfH,GAAiB,GACbM,EAAkBqC,SACpBrC,EAAkBqC,QAAQ0E,UAE5BC,WAAW,KACTpF,KACC,MAqC6D4E,SAAA,EACpDR,EAAAA,EAAAA,KAAA,OAAKxD,MAAM,KAAKD,OAAO,KAAKmE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,iNACJ,yFAKVH,EAAAA,EAAAA,MAAAQ,EAAAA,SAAA,CAAAT,SAAA,EACI/G,IACAgH,EAAAA,EAAAA,MAAA,OAAKN,UAAU,iBAAgBK,SAAA,EAC7BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,kBAAiBK,UAC9BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,eAEjBH,EAAAA,EAAAA,KAAA,KAAAQ,SAAG,8GAGPR,EAAAA,EAAAA,KAAA,OACEkB,IAAKpH,EACLqG,UAAW,mBAAmB1G,EAA2B,GAAX,iBAOrDQ,EAAac,QACZ0F,EAAAA,EAAAA,MAACR,EAAAA,EAAOC,IAAG,CACTC,UAAU,aACVC,QAAS,CAAEC,QAAS,EAAGc,EAAG,IAC1Bb,QAAS,CAAED,QAAS,EAAGc,EAAG,GAC1BZ,WAAY,CAAEa,MAAO,IAAMZ,SAAA,EAE3BR,EAAAA,EAAAA,KAAA,MAAIG,UAAU,cAAaK,SAAEvG,EAAac,SAC1C0F,EAAAA,EAAAA,MAAA,OAAKN,UAAU,aAAYK,SAAA,EACzBR,EAAAA,EAAAA,KAAA,QAAMG,UAAU,gBAAeK,SAAEvG,EAAae,UAC7Cf,EAAagB,SAAW,IACvBwF,EAAAA,EAAAA,MAAA,QAAMN,UAAU,iBAAgBK,SAAA,CAAC,mCACvBa,KAAKC,MAAMrH,EAAagB,SAAW,IAAI,KAAGhB,EAAagB,SAAW,IAAIsG,WAAWC,SAAS,EAAG,cAQ9GnH,EAASoH,UACRhB,EAAAA,EAAAA,MAACR,EAAAA,EAAOC,IAAG,CACTC,UAAU,oBACVC,QAAS,CAAEC,QAAS,EAAGqB,MAAO,IAC9BpB,QAAS,CAAED,QAAS,EAAGqB,MAAO,GAC9BnB,WAAY,CAAEa,MAAO,IAAMZ,SAAA,EAE3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaK,UAC1BR,EAAAA,EAAAA,KAAA,OAAKxD,MAAM,KAAKD,OAAO,KAAKmE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,sGAGZZ,EAAAA,EAAAA,KAAA,QAAAQ,SAAM,gQA1FZR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaK,UAC1BC,EAAAA,EAAAA,MAAA,OAAKN,UAAU,eAAcK,SAAA,EAC3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,aAAYK,UACzBR,EAAAA,EAAAA,KAAA,OAAKxD,MAAM,KAAKD,OAAO,KAAKmE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,uBAGZZ,EAAAA,EAAAA,KAAA,MAAAQ,SAAI,qHACJR,EAAAA,EAAAA,KAAA,KAAAQ,SAAG,sP,kCCsLb,MACA,EADmB,IA1ZnB,MACEmB,WAAAA,GAEEC,KAAKC,OAASC,CAAAA,SAAAA,aAAAA,WAAAA,IAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,0BAC1BH,KAAKI,QAAU,wCAGfJ,KAAKK,gBAAkBL,KAAKC,QAA0B,aAAhBD,KAAKC,OAG3CD,KAAKM,mBAAqB,CACxB,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cACA,cAEJ,CAGA,kBAAMC,CAAaC,GAAyC,IAAlCC,EAAUC,UAAAC,OAAA,QAAAtE,IAAAqE,UAAA,GAAAA,UAAA,GAAG,GAAIE,EAASF,UAAAC,OAAA,QAAAtE,IAAAqE,UAAA,GAAAA,UAAA,GAAG,GACrD,IACE,GAAIV,KAAKK,eACP,aAAaL,KAAKa,4BAA4BL,EAAOC,EAAYG,GAGnE,MAAME,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,UACNC,EAAGT,EACHU,KAAM,QACNT,WAAYA,EAAWd,WACvBwB,IAAKnB,KAAKC,OACVmB,MAAO,YACPC,WAAY,WACZC,WAAY,KACZC,kBAAmB,OAGjBX,GACFE,EAAOU,OAAO,YAAaZ,GAG7B,MAAMa,QAAiBC,MAAM,GAAG1B,KAAKI,kBAAkBU,KAEvD,IAAKW,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMpE,QAAagE,EAASK,OAG5B,OAAO9B,KAAK+B,uBAAuBtE,EACrC,CAAE,MAAO/D,GAGP,OAFAC,QAAQD,MAAM,wBAAyBA,GAEhCsG,KAAKa,4BAA4BL,EAAOC,EAAYG,EAC7D,CACF,CAGA,qBAAM1H,CAAgBP,GACpB,IACE,GAAIqH,KAAKK,eACP,aAAaL,KAAKgC,2BAA2BrJ,GAG/C,MAAMmI,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,oCACNpI,GAAID,EACJwI,IAAKnB,KAAKC,SAGNwB,QAAiBC,MAAM,GAAG1B,KAAKI,kBAAkBU,KAEvD,IAAKW,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMpE,QAAagE,EAASK,OAE5B,GAAIrE,EAAKwE,OAASxE,EAAKwE,MAAMtB,OAAS,EACpC,OAAOX,KAAKkC,sBAAsBzE,EAAKwE,MAAM,IAG/C,MAAM,IAAIL,MAAM,kBAClB,CAAE,MAAOlI,GAEP,OADAC,QAAQD,MAAM,+BAAgCA,GACvCsG,KAAKgC,2BAA2BrJ,EACzC,CACF,CAGA,iCAAMkI,CAA4BL,EAAOC,EAAYG,GACnD,IAEE,MAAMuB,QAAsBnC,KAAKoC,+BAA+B5B,EAAOC,GACvE,MAAO,CACL4B,OAAQF,EACRG,cAAe1B,EAAY,KAAO,kBAClC2B,aAAcJ,EAAcxB,OAEhC,CAAE,MAAOjH,GAEP,OADAC,QAAQD,MAAM,4BAA6BA,GACpCsG,KAAKwC,qBAAqBhC,EACnC,CACF,CAGA,oCAAM4B,CAA+B5B,EAAOC,GAC1C,MAAMgC,EAAU,GACVC,EAAU,IAAIC,IAGdC,EAAY,IAAI5C,KAAKM,oBAE3B,IAAK,IAAIuC,EAAI,EAAGA,EAAIpD,KAAKqD,IAAIrC,EAAY,IAAKoC,IAAK,CACjD,IAAIlK,EACJ,GACEA,EAAUiK,EAAUnD,KAAKC,MAAMD,KAAKsD,SAAWH,EAAUjC,eAClD+B,EAAQM,IAAIrK,IAErB+J,EAAQO,IAAItK,GAGZ,MAAMuK,QAAqBlD,KAAKmD,yBAAyBxK,EAAS6H,EAAOqC,GACrEK,GACFT,EAAQW,KAAKF,EAEjB,CAEA,OAAOT,CACT,CAGA,8BAAMU,CAAyBxK,GAAuC,IAA9B0K,EAAW3C,UAAAC,OAAA,QAAAtE,IAAAqE,UAAA,GAAAA,UAAA,GAAG,GACpD,IACE,MAAM4C,EAAY,sEAAsE3K,gBAClF8I,QAAiBC,MAAM4B,GAE7B,IAAK7B,EAASE,GACZ,MAAM,IAAIC,MAAM,qBAAqBH,EAASI,UAGhD,MAAMpE,QAAagE,EAASK,OAG5B,MAAO,CACLlJ,GAAID,EACJQ,MAAOkK,EAAc,GAAG5F,EAAKtE,WAAWkK,IAAgB5F,EAAKtE,MAC7DC,QAASqE,EAAK8F,YACdhK,YAAa,iGAAsBkE,EAAK8F,gBAAgBF,EAAc,8EAAoBA,EAAc,2FACxG/J,UAAWmE,EAAK+F,cAChBnK,SAAU2G,KAAKyD,yBACfjK,MAAOwG,KAAK0D,sBACZjK,YAAauG,KAAK2D,qBAClBC,UAAW,KAAKnE,KAAKsD,SAASpD,SAAS,IAAIkE,UAAU,EAAG,MAE5D,CAAE,MAAOnK,GAEP,OADAC,QAAQD,MAAM,gBAAiBA,GACxB,IACT,CACF,CAGA,gCAAMsI,CAA2BrJ,GAC/B,IACE,MAAMK,QAAgBgH,KAAKmD,yBAAyBxK,GACpD,GAAIK,EACF,OAAOA,CAEX,CAAE,MAAOU,GACPC,QAAQD,MAAM,mCAAoCA,EACpD,CAEA,OAAOsG,KAAK8D,oBAAoBnL,EAClC,CAGAoJ,sBAAAA,CAAuBtE,GAAO,IAADsG,EAC3B,IAAKtG,EAAKwE,MACR,MAAO,CAAEI,OAAQ,GAAIC,cAAe,KAAMC,aAAc,GAe1D,MAAO,CACLF,OAba5E,EAAKwE,MAAM+B,IAAIC,IAAI,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,MAAK,CACrCzL,GAAIqL,EAAKrL,GAAGD,SAAWsL,EAAKrL,GAC5BO,MAAO8K,EAAKK,QAAQnL,MACpBC,QAAS6K,EAAKK,QAAQC,aACtBhL,YAAa0K,EAAKK,QAAQ/K,YAC1BD,WAAyC,QAA9B4K,EAAAD,EAAKK,QAAQE,WAAWC,cAAM,IAAAP,OAAA,EAA9BA,EAAgCQ,OAAsC,QAAnCP,EAAIF,EAAKK,QAAQE,WAAWG,eAAO,IAAAR,OAAA,EAA/BA,EAAiCO,KACnFjL,YAAawK,EAAKK,QAAQ7K,YAC1BJ,SAAU2G,KAAK4E,cAAiC,QAApBR,EAACH,EAAKY,sBAAc,IAAAT,OAAA,EAAnBA,EAAqB/K,UAClDG,MAAOwG,KAAK8E,YAA2B,QAAhBT,EAACJ,EAAKc,kBAAU,IAAAV,OAAA,EAAfA,EAAiBW,WACzCpB,UAAWK,EAAKK,QAAQV,aAKxBtB,cAAe7E,EAAK6E,eAAiB,KACrCC,cAA2B,QAAbwB,EAAAtG,EAAKwH,gBAAQ,IAAAlB,OAAA,EAAbA,EAAexB,eAAgB,EAEjD,CAGAL,qBAAAA,CAAsB+B,GAAO,IAADiB,EAAAC,EAC1B,MAAO,CACLvM,GAAIqL,EAAKrL,GACTO,MAAO8K,EAAKK,QAAQnL,MACpBC,QAAS6K,EAAKK,QAAQC,aACtBhL,YAAa0K,EAAKK,QAAQ/K,YAC1BD,WAAyC,QAA9B4L,EAAAjB,EAAKK,QAAQE,WAAWY,cAAM,IAAAF,OAAA,EAA9BA,EAAgCR,OAAmC,QAAhCS,EAAIlB,EAAKK,QAAQE,WAAWa,YAAI,IAAAF,OAAA,EAA5BA,EAA8BT,KAChFjL,YAAawK,EAAKK,QAAQ7K,YAC1BJ,SAAU2G,KAAK4E,cAAcX,EAAKY,eAAexL,UACjDG,MAAOwG,KAAK8E,YAAYb,EAAKc,WAAWC,WACxCM,MAAOtF,KAAK8E,YAAYb,EAAKc,WAAWQ,WACxC3B,UAAWK,EAAKK,QAAQV,UACxB4B,KAAMvB,EAAKK,QAAQkB,MAAQ,GAE/B,CAGAZ,aAAAA,CAAcvL,GACZ,IAAKA,EAAU,OAAO,EAEtB,MAAMoM,EAAQpM,EAASoM,MAAM,2BAC7B,IAAKA,EAAO,OAAO,EAMnB,OAAe,MAJDC,SAASD,EAAM,KAAO,GAIJ,IAHhBC,SAASD,EAAM,KAAO,IACtBC,SAASD,EAAM,KAAO,EAGxC,CAGAX,WAAAA,CAAYE,GACV,IAAKA,EAAW,MAAO,IAEvB,MAAMW,EAAQD,SAASV,GACvB,OAAIW,GAAS,IACJ,IAAIA,EAAQ,KAASC,QAAQ,MAC3BD,GAAS,IACX,IAAIA,EAAQ,KAAMC,QAAQ,MAE5BD,EAAMhG,UACf,CAGAkG,cAAAA,CAAeC,GACb,IAAKA,EAAS,MAAO,OAErB,MAAMC,EAAQtG,KAAKC,MAAMoG,EAAU,MAC7BE,EAAUvG,KAAKC,MAAOoG,EAAU,KAAQ,IACxCG,EAAOH,EAAU,GAEvB,OAAIC,EAAQ,EACH,GAAGA,KAASC,EAAQrG,WAAWC,SAAS,EAAG,QAAQqG,EAAKtG,WAAWC,SAAS,EAAG,OAEjF,GAAGoG,KAAWC,EAAKtG,WAAWC,SAAS,EAAG,MACnD,CAGA6D,sBAAAA,GAGE,MAAO,GAFShE,KAAKC,MAAsB,GAAhBD,KAAKsD,UAAiB,KACjCtD,KAAKC,MAAsB,GAAhBD,KAAKsD,UACHpD,WAAWC,SAAS,EAAG,MACtD,CAGA8D,mBAAAA,GACE,MAAMlK,EAAQiG,KAAKC,MAAsB,IAAhBD,KAAKsD,UAAuB,IACrD,OAAO/C,KAAK8E,YAAYtL,EAAMmG,WAChC,CAGAgE,kBAAAA,GACE,MAAMuC,EAAM,IAAIC,KACVC,EAAU3G,KAAKC,MAAsB,IAAhBD,KAAKsD,UAEhC,OADa,IAAIoD,KAAKD,EAAIG,UAAsB,GAAVD,EAAe,GAAK,GAAK,KACnDE,aACd,CAGA9D,oBAAAA,CAAqBhC,GACnB,MAAM+F,EAAa,CACjB,CACE3N,GAAI,cACJO,MAAO,GAAGqH,yGACVpH,QAAS,sEACTG,YAAa,sMACbD,UAAW,uDACXD,SAAU,OACVG,MAAO,OACPC,YAAa,uBACbmK,UAAW,eAEb,CACEhL,GAAI,cACJO,MAAO,sBAAOqH,qDACdpH,QAAS,sEACTG,YAAa,yLACbD,UAAW,uDACXD,SAAU,OACVG,MAAO,OACPC,YAAa,uBACbmK,UAAW,eAEb,CACEhL,GAAI,cACJO,MAAO,4BAAQqH,4DACfpH,QAAS,kFACTG,YAAa,oMACbD,UAAW,uDACXD,SAAU,OACVG,MAAO,OACPC,YAAa,uBACbmK,UAAW,gBAIf,MAAO,CACLvB,OAAQkE,EACRjE,cAAe,KACfC,aAAcgE,EAAW5F,OAE7B,CAGAmD,mBAAAA,CAAoBnL,GAClB,MAAO,CACLC,GAAID,EACJQ,MAAO,2GACPC,QAAS,sEACTG,YAAa,wGACbD,UAAW,8BAA8BX,sBACzCU,SAAU,OACVG,MAAO,OACP8L,MAAO,MACP7L,YAAa,uBACbmK,UAAW,cACX4B,KAAM,CAAC,uCAAU,iCAAS,wCAE9B,CAGA,uBAAMgB,GAAuD,IAArClF,EAAUZ,UAAAC,OAAA,QAAAtE,IAAAqE,UAAA,GAAAA,UAAA,GAAG,KAAMD,EAAUC,UAAAC,OAAA,QAAAtE,IAAAqE,UAAA,GAAAA,UAAA,GAAG,GACtD,IACE,GAAIV,KAAKK,eACP,aAAaL,KAAKyG,uBAAuBhG,GAG3C,MAAMK,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,oCACN0F,MAAO,cACPpF,aACAb,WAAYA,EAAWd,WACvBwB,IAAKnB,KAAKC,OACV0G,gBAAiB,MAGblF,QAAiBC,MAAM,GAAG1B,KAAKI,kBAAkBU,KAEvD,IAAKW,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMpE,QAAagE,EAASK,OAC5B,OAAO9B,KAAK+B,uBAAuBtE,EACrC,CAAE,MAAO/D,GAEP,OADAC,QAAQD,MAAM,0BAA2BA,GAClCsG,KAAKyG,uBAAuBhG,EACrC,CACF,CAGA,4BAAMgG,GAAwC,IAAjBhG,EAAUC,UAAAC,OAAA,QAAAtE,IAAAqE,UAAA,GAAAA,UAAA,GAAG,EACxC,IACE,MAAMkG,EAAiB,GACjBlE,EAAU,IAAIC,IAEpB,IAAK,IAAIE,EAAI,EAAGA,EAAIpD,KAAKqD,IAAIrC,EAAYT,KAAKM,mBAAmBK,QAASkC,IAAK,CAC7E,MAAMlK,EAAUqH,KAAKM,mBAAmBuC,GACxC,IAAKH,EAAQM,IAAIrK,GAAU,CACzB+J,EAAQO,IAAItK,GACZ,MAAMuK,QAAqBlD,KAAKmD,yBAAyBxK,EAAS,4EAAiBkK,GAC/EK,GACF0D,EAAexD,KAAKF,EAExB,CACF,CAEA,MAAO,CACLb,OAAQuE,EACRtE,cAAe,KACfC,aAAcqE,EAAejG,OAEjC,CAAE,MAAOjH,GAEP,OADAC,QAAQD,MAAM,8BAA+BA,GACtCsG,KAAKwC,qBAAqB,4EACnC,CACF,E", "sources": ["components/pages/Player.js", "services/youtubeAPI.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport PropTypes from 'prop-types';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport youtubeAPI from '../../services/youtubeAPI';\nimport './Player.css';\n\nconst Player = ({ routeParams = {} }) => {\n  const [isPlayerReady, setIsPlayerReady] = useState(false);\n  const [playerError, setPlayerError] = useState(null);\n  const playerRef = useRef(null);\n  const playerInstanceRef = useRef(null);\n  \n  const { \n    currentVideo, \n    setCurrentVideo, \n    updateVideoState,\n    incrementVideosWatched,\n    settings \n  } = useAppStore();\n\n  const videoId = routeParams?.videoId || currentVideo.id;\n\n  const loadVideoDetails = useCallback(async () => {\n    if (!videoId) return;\n\n    try {\n      const details = await youtubeAPI.getVideoDetails(videoId);\n      setCurrentVideo({\n        id: videoId,\n        title: details.title,\n        channel: details.channel,\n        duration: details.duration,\n        thumbnail: details.thumbnail,\n        description: details.description,\n        views: details.views,\n        publishedAt: details.publishedAt\n      });\n    } catch (error) {\n      console.error('Failed to load video details:', error);\n    }\n  }, [videoId, setCurrentVideo]);\n\n  useEffect(() => {\n    if (videoId) {\n      loadYouTubeAPI();\n      loadVideoDetails();\n    }\n  }, [videoId, loadVideoDetails]);\n\n\n\n  const loadYouTubeAPI = () => {\n    if (!window.YT) {\n      const script = document.createElement('script');\n      script.src = 'https://www.youtube.com/iframe_api';\n      script.async = true;\n      document.body.appendChild(script);\n\n      window.onYouTubeIframeAPIReady = () => {\n        initializePlayer();\n      };\n    } else {\n      initializePlayer();\n    }\n  };\n\n  const initializePlayer = () => {\n    if (playerRef.current && window.YT?.Player) {\n      try {\n        playerInstanceRef.current = new window.YT.Player(playerRef.current, {\n          height: '100%',\n          width: '100%',\n          videoId: videoId,\n          playerVars: {\n            autoplay: settings.autoplay ? 1 : 0,\n            controls: settings.hideControls ? 0 : 1,\n            disablekb: settings.hideControls ? 1 : 0,\n            fs: 1,\n            iv_load_policy: 3,\n            modestbranding: 1,\n            playsinline: 1,\n            rel: 0,\n            showinfo: 0,\n            cc_load_policy: 0,\n            hl: 'ar',\n            cc_lang_pref: 'ar',\n            origin: window.location.origin\n          },\n          events: {\n            onReady: onPlayerReady,\n            onStateChange: onPlayerStateChange,\n            onError: onPlayerError\n          }\n        });\n      } catch (error) {\n        console.error('Error initializing YouTube player:', error);\n        setPlayerError('فشل في تحميل مشغل الفيديو');\n      }\n    }\n  };\n\n  const onPlayerReady = (event) => {\n    setIsPlayerReady(true);\n    setPlayerError(null);\n    \n    // Set initial volume\n    if (settings.volume !== undefined) {\n      event.target.setVolume(settings.volume);\n    }\n    \n    // Get video info\n    const videoData = event.target.getVideoData();\n    setCurrentVideo({\n      id: videoId,\n      title: videoData.title || 'فيديو يوتيوب',\n      channel: videoData.author || 'قناة يوتيوب',\n      duration: event.target.getDuration() || 0,\n      currentTime: 0,\n      isPlaying: false,\n      isPaused: true,\n      isBuffering: false,\n      quality: event.target.getPlaybackQuality() || 'auto',\n      volume: event.target.getVolume() || 100,\n      muted: event.target.isMuted() || false,\n      fullscreen: false\n    });\n\n    console.log('YouTube player ready for video:', videoId);\n  };\n\n  const onPlayerStateChange = (event) => {\n    const state = event.data;\n    const player = event.target;\n    \n    switch (state) {\n      case window.YT.PlayerState.PLAYING:\n        updateVideoState({\n          isPlaying: true,\n          isPaused: false,\n          isBuffering: false\n        });\n        break;\n        \n      case window.YT.PlayerState.PAUSED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false\n        });\n        break;\n        \n      case window.YT.PlayerState.BUFFERING:\n        updateVideoState({\n          isBuffering: true\n        });\n        break;\n        \n      case window.YT.PlayerState.ENDED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false,\n          currentTime: player.getDuration()\n        });\n        incrementVideosWatched();\n        break;\n        \n      case window.YT.PlayerState.CUED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false,\n          currentTime: 0\n        });\n        break;\n    }\n\n    // Update current time\n    if (player.getCurrentTime) {\n      updateVideoState({\n        currentTime: player.getCurrentTime()\n      });\n    }\n  };\n\n  const onPlayerError = (event) => {\n    const errorCode = event.data;\n    let errorMessage = 'حدث خطأ في تشغيل الفيديو';\n    \n    switch (errorCode) {\n      case 2:\n        errorMessage = 'معرف الفيديو غير صحيح';\n        break;\n      case 5:\n        errorMessage = 'خطأ في مشغل HTML5';\n        break;\n      case 100:\n        errorMessage = 'الفيديو غير موجود أو محذوف';\n        break;\n      case 101:\n      case 150:\n        errorMessage = 'صاحب الفيديو لا يسمح بتشغيله في هذا التطبيق';\n        break;\n    }\n    \n    setPlayerError(errorMessage);\n    console.error('YouTube player error:', errorCode, errorMessage);\n  };\n\n  const handleRetry = () => {\n    setPlayerError(null);\n    setIsPlayerReady(false);\n    if (playerInstanceRef.current) {\n      playerInstanceRef.current.destroy();\n    }\n    setTimeout(() => {\n      initializePlayer();\n    }, 1000);\n  };\n\n  if (!videoId) {\n    return (\n      <div className=\"player-page\">\n        <div className=\"player-empty\">\n          <div className=\"empty-icon\">\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M8 5v14l11-7z\"/>\n            </svg>\n          </div>\n          <h2>لا يوجد فيديو للتشغيل</h2>\n          <p>يرجى البحث عن فيديو أو اختيار فيديو من القائمة</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <motion.div \n      className=\"player-page\"\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <div className=\"player-container\">\n        <div className=\"player-wrapper\">\n          {playerError ? (\n            <div className=\"player-error\">\n              <div className=\"error-icon\">\n                <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                </svg>\n              </div>\n              <h3>خطأ في تشغيل الفيديو</h3>\n              <p>{playerError}</p>\n              <button className=\"retry-button\" onClick={handleRetry}>\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"/>\n                </svg>\n                إعادة المحاولة\n              </button>\n            </div>\n          ) : (\n            <>\n              {!isPlayerReady && (\n                <div className=\"player-loading\">\n                  <div className=\"loading-spinner\">\n                    <div className=\"spinner\"></div>\n                  </div>\n                  <p>جاري تحميل الفيديو...</p>\n                </div>\n              )}\n              <div \n                ref={playerRef}\n                className={`youtube-player ${!isPlayerReady ? 'hidden' : ''}`}\n              />\n            </>\n          )}\n        </div>\n\n        {/* Video Info */}\n        {currentVideo.title && (\n          <motion.div \n            className=\"video-info\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n          >\n            <h1 className=\"video-title\">{currentVideo.title}</h1>\n            <div className=\"video-meta\">\n              <span className=\"video-channel\">{currentVideo.channel}</span>\n              {currentVideo.duration > 0 && (\n                <span className=\"video-duration\">\n                  المدة: {Math.floor(currentVideo.duration / 60)}:{(currentVideo.duration % 60).toString().padStart(2, '0')}\n                </span>\n              )}\n            </div>\n          </motion.div>\n        )}\n\n        {/* Ad Blocker Status */}\n        {settings.adBlock && (\n          <motion.div \n            className=\"ad-blocker-status\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.5 }}\n          >\n            <div className=\"status-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <span>مانع الإعلانات مفعل - تجربة مشاهدة بدون إعلانات</span>\n          </motion.div>\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\nPlayer.propTypes = {\n  routeParams: PropTypes.shape({\n    videoId: PropTypes.string\n  })\n};\n\nexport default Player;\n", "// YouTube Data API service\n// This service handles all interactions with YouTube's real API\n\nclass YouTubeAPIService {\n  constructor() {\n    // For production, you would get this from environment variables\n    this.apiKey = process.env.REACT_APP_YOUTUBE_API_KEY;\n    this.baseURL = 'https://www.googleapis.com/youtube/v3';\n\n    // Use alternative methods if no API key is available\n    this.useAlternative = !this.apiKey || this.apiKey === 'DEMO_KEY';\n\n    // Real trending video IDs that we know exist (updated regularly)\n    this.realTrendingVideos = [\n      'dQw4w9WgXcQ', // <PERSON> - Never Gonna Give You Up\n      'kJQP7kiw5Fk', // <PERSON> - <PERSON> ft. Daddy Yankee\n      'fJ9rUzIMcZQ', // Wiz <PERSON>halifa - See You Again ft. Charlie Puth\n      'JGwWNGJdvx8', // Ed <PERSON> - Shape of You\n      'RgKAFK5djSk', // Psy - Gangnam Style\n      'CevxZvSJLk8', // <PERSON> - <PERSON>oar\n      'hTWKbfoikeg', // Adele - Someone Like You\n      '9bZkp7q19f0', // PSY - Gangnam Style\n      'YQHsXMglC9A', // Adele - Hello\n      'lp-EO5I60KA'  // Thinking Out Loud - Ed Sheeran\n    ];\n  }\n\n  // Search for videos using YouTube Data API or alternative method\n  async searchVideos(query, maxResults = 25, pageToken = '') {\n    try {\n      if (this.useAlternative) {\n        return await this.searchWithAlternativeMethod(query, maxResults, pageToken);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet',\n        q: query,\n        type: 'video',\n        maxResults: maxResults.toString(),\n        key: this.apiKey,\n        order: 'relevance',\n        safeSearch: 'moderate',\n        regionCode: 'SA', // Saudi Arabia for Arabic content\n        relevanceLanguage: 'ar'\n      });\n\n      if (pageToken) {\n        params.append('pageToken', pageToken);\n      }\n\n      const response = await fetch(`${this.baseURL}/search?${params}`);\n\n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n\n      // Transform the data to our format\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('YouTube search error:', error);\n      // Fallback to alternative method\n      return this.searchWithAlternativeMethod(query, maxResults, pageToken);\n    }\n  }\n\n  // Get video details by ID\n  async getVideoDetails(videoId) {\n    try {\n      if (this.useAlternative) {\n        return await this.getVideoDetailsAlternative(videoId);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet,statistics,contentDetails',\n        id: videoId,\n        key: this.apiKey\n      });\n\n      const response = await fetch(`${this.baseURL}/videos?${params}`);\n\n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n\n      if (data.items && data.items.length > 0) {\n        return this.transformVideoDetails(data.items[0]);\n      }\n\n      throw new Error('Video not found');\n    } catch (error) {\n      console.error('YouTube video details error:', error);\n      return this.getVideoDetailsAlternative(videoId);\n    }\n  }\n\n  // Alternative search method using real video data\n  async searchWithAlternativeMethod(query, maxResults, pageToken) {\n    try {\n      // Use a combination of real video IDs and generate realistic data\n      const searchResults = await this.generateRealisticSearchResults(query, maxResults);\n      return {\n        videos: searchResults,\n        nextPageToken: pageToken ? null : 'next_page_token',\n        totalResults: searchResults.length\n      };\n    } catch (error) {\n      console.error('Alternative search error:', error);\n      return this.getMockSearchResults(query);\n    }\n  }\n\n  // Generate realistic search results using real video IDs\n  async generateRealisticSearchResults(query, maxResults) {\n    const results = [];\n    const usedIds = new Set();\n\n    // Mix of trending videos and query-specific results\n    const videoPool = [...this.realTrendingVideos];\n\n    for (let i = 0; i < Math.min(maxResults, 10); i++) {\n      let videoId;\n      do {\n        videoId = videoPool[Math.floor(Math.random() * videoPool.length)];\n      } while (usedIds.has(videoId));\n\n      usedIds.add(videoId);\n\n      // Get real video details using oEmbed (no API key required)\n      const videoDetails = await this.getVideoDetailsViaOEmbed(videoId, query, i);\n      if (videoDetails) {\n        results.push(videoDetails);\n      }\n    }\n\n    return results;\n  }\n\n  // Get video details using oEmbed API (no API key required)\n  async getVideoDetailsViaOEmbed(videoId, searchQuery = '', index = 0) {\n    try {\n      const oEmbedUrl = `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`;\n      const response = await fetch(oEmbedUrl);\n\n      if (!response.ok) {\n        throw new Error(`oEmbed API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n\n      // Generate realistic data based on the real video\n      return {\n        id: videoId,\n        title: searchQuery ? `${data.title} - ${searchQuery}` : data.title,\n        channel: data.author_name,\n        description: `فيديو رائع من قناة ${data.author_name}. ${searchQuery ? 'محتوى متعلق بـ ' + searchQuery : 'محتوى مميز ومفيد'}`,\n        thumbnail: data.thumbnail_url,\n        duration: this.generateRandomDuration(),\n        views: this.generateRandomViews(),\n        publishedAt: this.generateRandomDate(),\n        channelId: `UC${Math.random().toString(36).substring(2, 11)}`\n      };\n    } catch (error) {\n      console.error('oEmbed error:', error);\n      return null;\n    }\n  }\n\n  // Alternative method for getting video details\n  async getVideoDetailsAlternative(videoId) {\n    try {\n      const details = await this.getVideoDetailsViaOEmbed(videoId);\n      if (details) {\n        return details;\n      }\n    } catch (error) {\n      console.error('Alternative video details error:', error);\n    }\n\n    return this.getMockVideoDetails(videoId);\n  }\n\n  // Transform YouTube API search results to our format\n  transformSearchResults(data) {\n    if (!data.items) {\n      return { videos: [], nextPageToken: null, totalResults: 0 };\n    }\n\n    const videos = data.items.map(item => ({\n      id: item.id.videoId || item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails?.duration),\n      views: this.formatViews(item.statistics?.viewCount),\n      channelId: item.snippet.channelId\n    }));\n\n    return {\n      videos,\n      nextPageToken: data.nextPageToken || null,\n      totalResults: data.pageInfo?.totalResults || 0\n    };\n  }\n\n  // Transform video details to our format\n  transformVideoDetails(item) {\n    return {\n      id: item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.maxres?.url || item.snippet.thumbnails.high?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails.duration),\n      views: this.formatViews(item.statistics.viewCount),\n      likes: this.formatViews(item.statistics.likeCount),\n      channelId: item.snippet.channelId,\n      tags: item.snippet.tags || []\n    };\n  }\n\n  // Parse ISO 8601 duration to seconds\n  parseDuration(duration) {\n    if (!duration) return 0;\n    \n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/);\n    if (!match) return 0;\n    \n    const hours = parseInt(match[1]) || 0;\n    const minutes = parseInt(match[2]) || 0;\n    const seconds = parseInt(match[3]) || 0;\n    \n    return hours * 3600 + minutes * 60 + seconds;\n  }\n\n  // Format view count\n  formatViews(viewCount) {\n    if (!viewCount) return '0';\n    \n    const count = parseInt(viewCount);\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  }\n\n  // Format duration from seconds to MM:SS or HH:MM:SS\n  formatDuration(seconds) {\n    if (!seconds) return '0:00';\n\n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n\n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n  // Generate random duration for mock videos\n  generateRandomDuration() {\n    const minutes = Math.floor(Math.random() * 15) + 1; // 1-15 minutes\n    const seconds = Math.floor(Math.random() * 60);\n    return `${minutes}:${seconds.toString().padStart(2, '0')}`;\n  }\n\n  // Generate random view count\n  generateRandomViews() {\n    const views = Math.floor(Math.random() * 10000000) + 1000; // 1K - 10M views\n    return this.formatViews(views.toString());\n  }\n\n  // Generate random publish date\n  generateRandomDate() {\n    const now = new Date();\n    const daysAgo = Math.floor(Math.random() * 365); // Up to 1 year ago\n    const date = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);\n    return date.toISOString();\n  }\n\n  // Mock search results (fallback)\n  getMockSearchResults(query) {\n    const mockVideos = [\n      {\n        id: 'dQw4w9WgXcQ',\n        title: `${query} - نتيجة البحث الأولى`,\n        channel: 'قناة تجريبية',\n        description: 'وصف مفصل للفيديو الأول في نتائج البحث...',\n        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',\n        duration: '3:32',\n        views: '1.2M',\n        publishedAt: '2023-01-15T10:00:00Z',\n        channelId: 'UC123456789'\n      },\n      {\n        id: 'kJQP7kiw5Fk',\n        title: `شرح ${query} بالتفصيل`,\n        channel: 'قناة التعليم',\n        description: 'شرح شامل ومفصل حول الموضوع المطلوب...',\n        thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg',\n        duration: '4:42',\n        views: '850K',\n        publishedAt: '2023-02-20T15:30:00Z',\n        channelId: 'UC987654321'\n      },\n      {\n        id: 'fJ9rUzIMcZQ',\n        title: `أفضل ${query} لهذا العام`,\n        channel: 'قناة المراجعات',\n        description: 'مراجعة شاملة لأفضل الخيارات المتاحة...',\n        thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',\n        duration: '5:55',\n        views: '2.1M',\n        publishedAt: '2023-03-10T12:15:00Z',\n        channelId: 'UC456789123'\n      }\n    ];\n\n    return {\n      videos: mockVideos,\n      nextPageToken: null,\n      totalResults: mockVideos.length\n    };\n  }\n\n  // Mock video details (fallback)\n  getMockVideoDetails(videoId) {\n    return {\n      id: videoId,\n      title: 'فيديو يوتيوب تجريبي',\n      channel: 'قناة تجريبية',\n      description: 'وصف تجريبي للفيديو...',\n      thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n      duration: '3:45',\n      views: '1.5M',\n      likes: '25K',\n      publishedAt: '2023-01-01T00:00:00Z',\n      channelId: 'UC123456789',\n      tags: ['تجريبي', 'فيديو', 'يوتيوب']\n    };\n  }\n\n  // Get trending videos\n  async getTrendingVideos(regionCode = 'SA', maxResults = 25) {\n    try {\n      if (this.useAlternative) {\n        return await this.getTrendingAlternative(maxResults);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet,statistics,contentDetails',\n        chart: 'mostPopular',\n        regionCode,\n        maxResults: maxResults.toString(),\n        key: this.apiKey,\n        videoCategoryId: '0' // All categories\n      });\n\n      const response = await fetch(`${this.baseURL}/videos?${params}`);\n\n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('YouTube trending error:', error);\n      return this.getTrendingAlternative(maxResults);\n    }\n  }\n\n  // Get trending videos using alternative method\n  async getTrendingAlternative(maxResults = 6) {\n    try {\n      const trendingVideos = [];\n      const usedIds = new Set();\n\n      for (let i = 0; i < Math.min(maxResults, this.realTrendingVideos.length); i++) {\n        const videoId = this.realTrendingVideos[i];\n        if (!usedIds.has(videoId)) {\n          usedIds.add(videoId);\n          const videoDetails = await this.getVideoDetailsViaOEmbed(videoId, 'الأكثر مشاهدة', i);\n          if (videoDetails) {\n            trendingVideos.push(videoDetails);\n          }\n        }\n      }\n\n      return {\n        videos: trendingVideos,\n        nextPageToken: null,\n        totalResults: trendingVideos.length\n      };\n    } catch (error) {\n      console.error('Alternative trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n\n\n}\n\n// Create and export a singleton instance\nconst youtubeAPI = new YouTubeAPIService();\nexport default youtubeAPI;\n"], "names": ["_ref", "routeParams", "isPlayerReady", "setIsPlayerReady", "useState", "playerError", "setPlayerError", "playerRef", "useRef", "playerInstanceRef", "currentVideo", "setCurrentVideo", "updateVideoState", "incrementVideosWatched", "settings", "useAppStore", "videoId", "id", "loadVideoDetails", "useCallback", "async", "details", "youtubeAPI", "getVideoDetails", "title", "channel", "duration", "thumbnail", "description", "views", "publishedAt", "error", "console", "useEffect", "loadYouTubeAPI", "window", "YT", "initializePlayer", "script", "document", "createElement", "src", "body", "append<PERSON><PERSON><PERSON>", "onYouTubeIframeAPIReady", "_window$YT", "current", "Player", "height", "width", "playerVars", "autoplay", "controls", "hideControls", "disablekb", "fs", "iv_load_policy", "modestbranding", "playsinline", "rel", "showinfo", "cc_load_policy", "hl", "cc_lang_pref", "origin", "location", "events", "onReady", "onPlayerReady", "onStateChange", "onPlayerStateChange", "onError", "onPlayerError", "event", "undefined", "volume", "target", "setVolume", "videoData", "getVideoData", "author", "getDuration", "currentTime", "isPlaying", "isPaused", "isBuffering", "quality", "getPlaybackQuality", "getVolume", "muted", "isMuted", "fullscreen", "log", "state", "data", "player", "PlayerState", "PLAYING", "PAUSED", "BUFFERING", "ENDED", "CUED", "getCurrentTime", "errorCode", "errorMessage", "_jsx", "motion", "div", "className", "initial", "opacity", "animate", "transition", "children", "_jsxs", "viewBox", "fill", "d", "onClick", "handleRetry", "destroy", "setTimeout", "_Fragment", "ref", "y", "delay", "Math", "floor", "toString", "padStart", "adBlock", "scale", "constructor", "this", "<PERSON><PERSON><PERSON><PERSON>", "process", "REACT_APP_YOUTUBE_API_KEY", "baseURL", "useAlternative", "realTrendingVideos", "searchVideos", "query", "maxResults", "arguments", "length", "pageToken", "searchWithAlternativeMethod", "params", "URLSearchParams", "part", "q", "type", "key", "order", "safeSearch", "regionCode", "relevanceLanguage", "append", "response", "fetch", "ok", "Error", "status", "json", "transformSearchResults", "getVideoDetailsAlternative", "items", "transformVideoDetails", "searchResults", "generateRealisticSearchResults", "videos", "nextPageToken", "totalResults", "getMockSearchResults", "results", "usedIds", "Set", "videoPool", "i", "min", "random", "has", "add", "videoDetails", "getVideoDetailsViaOEmbed", "push", "searchQuery", "oEmbedUrl", "author_name", "thumbnail_url", "generateRandomDuration", "generateRandomViews", "generateRandomDate", "channelId", "substring", "getMockVideoDetails", "_data$pageInfo", "map", "item", "_item$snippet$thumbna", "_item$snippet$thumbna2", "_item$contentDetails", "_item$statistics", "snippet", "channelTitle", "thumbnails", "medium", "url", "default", "parseDuration", "contentDetails", "formatViews", "statistics", "viewCount", "pageInfo", "_item$snippet$thumbna3", "_item$snippet$thumbna4", "maxres", "high", "likes", "likeCount", "tags", "match", "parseInt", "count", "toFixed", "formatDuration", "seconds", "hours", "minutes", "secs", "now", "Date", "daysAgo", "getTime", "toISOString", "mockVideos", "getTrendingVideos", "getTrendingAlternative", "chart", "videoCategoryId", "trendingVideos"], "sourceRoot": ""}