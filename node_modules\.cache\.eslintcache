[{"C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\ErrorFallback.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\LoadingSpinner.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\layout\\Header.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Favorites.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\stores\\appStore.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\services\\router.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\About.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\layout\\Sidebar.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Settings.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\History.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Search.js": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Home.js": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Player.js": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\ThemeToggle.js": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\SearchBar.js": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\WindowControls.js": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\services\\youtubeAPI.js": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\data\\realYouTubeVideos.js": "20"}, {"size": 5197, "mtime": 1754079032198, "results": "21", "hashOfConfig": "22"}, {"size": 6849, "mtime": 1754081279663, "results": "23", "hashOfConfig": "22"}, {"size": 4251, "mtime": 1754079194459, "results": "24", "hashOfConfig": "22"}, {"size": 1638, "mtime": 1754079153661, "results": "25", "hashOfConfig": "22"}, {"size": 6594, "mtime": 1754078762266, "results": "26", "hashOfConfig": "22"}, {"size": 1011, "mtime": 1754080425990, "results": "27", "hashOfConfig": "22"}, {"size": 7626, "mtime": 1754078647862, "results": "28", "hashOfConfig": "22"}, {"size": 4686, "mtime": 1754078673463, "results": "29", "hashOfConfig": "22"}, {"size": 3274, "mtime": 1754080437585, "results": "30", "hashOfConfig": "22"}, {"size": 7414, "mtime": 1754079356285, "results": "31", "hashOfConfig": "22"}, {"size": 5498, "mtime": 1754080375363, "results": "32", "hashOfConfig": "22"}, {"size": 1332, "mtime": 1754080413744, "results": "33", "hashOfConfig": "22"}, {"size": 7660, "mtime": 1754081995527, "results": "34", "hashOfConfig": "22"}, {"size": 11313, "mtime": 1754081382553, "results": "35", "hashOfConfig": "22"}, {"size": 9865, "mtime": 1754082053811, "results": "36", "hashOfConfig": "22"}, {"size": 2588, "mtime": 1754079256479, "results": "37", "hashOfConfig": "22"}, {"size": 7666, "mtime": 1754078833591, "results": "38", "hashOfConfig": "22"}, {"size": 2186, "mtime": 1754079287058, "results": "39", "hashOfConfig": "22"}, {"size": 8867, "mtime": 1754082811017, "results": "40", "hashOfConfig": "22"}, {"size": 7073, "mtime": 1754082549411, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1a6rq5h", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\index.js", ["102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "112"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\App.js", ["113", "114", "115", "116", "117"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\ErrorFallback.js", ["118", "119"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Favorites.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\stores\\appStore.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\services\\router.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\layout\\Sidebar.js", ["120"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Settings.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\History.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Search.js", ["121", "122"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Home.js", ["123", "124"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Player.js", ["125", "126", "127", "128", "129", "130", "131"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\ThemeToggle.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\SearchBar.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\WindowControls.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\services\\youtubeAPI.js", ["132", "133", "134", "135", "136", "137"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\data\\realYouTubeVideos.js", [], [], {"ruleId": "138", "severity": 1, "message": "139", "line": 31, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 31, "endColumn": 16, "suggestions": "142"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 32, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 32, "endColumn": 16, "suggestions": "143"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 37, "column": 5, "nodeType": "140", "messageId": "141", "endLine": 37, "endColumn": 18, "suggestions": "144"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 63, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 63, "endColumn": 14, "suggestions": "145"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 139, "column": 7, "nodeType": "140", "messageId": "141", "endLine": 139, "endColumn": 18, "suggestions": "146"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 151, "column": 9, "nodeType": "140", "messageId": "141", "endLine": 151, "endColumn": 20, "suggestions": "147"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 154, "column": 9, "nodeType": "140", "messageId": "141", "endLine": 154, "endColumn": 20, "suggestions": "148"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 162, "column": 5, "nodeType": "140", "messageId": "141", "endLine": 162, "endColumn": 16, "suggestions": "149"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 168, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 168, "endColumn": 16, "suggestions": "150"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 172, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 172, "endColumn": 16, "suggestions": "151"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 179, "column": 3, "nodeType": "140", "messageId": "141", "endLine": 179, "endColumn": 14, "suggestions": "152"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 58, "column": 15, "nodeType": "140", "messageId": "141", "endLine": 58, "endColumn": 26, "suggestions": "153"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 61, "column": 13, "nodeType": "140", "messageId": "141", "endLine": 61, "endColumn": 25, "suggestions": "154"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 72, "column": 9, "nodeType": "140", "messageId": "141", "endLine": 72, "endColumn": 20, "suggestions": "155"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 74, "column": 9, "nodeType": "140", "messageId": "141", "endLine": 74, "endColumn": 22, "suggestions": "156"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 84, "column": 9, "nodeType": "140", "messageId": "141", "endLine": 84, "endColumn": 20, "suggestions": "157"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 16, "column": 5, "nodeType": "140", "messageId": "141", "endLine": 16, "endColumn": 18, "suggestions": "158"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 20, "column": 7, "nodeType": "140", "messageId": "141", "endLine": 20, "endColumn": 18, "suggestions": "159"}, {"ruleId": "160", "severity": 1, "message": "161", "line": 8, "column": 11, "nodeType": "162", "messageId": "163", "endLine": 8, "endColumn": 18}, {"ruleId": "164", "severity": 1, "message": "165", "line": 31, "column": 6, "nodeType": "166", "endLine": 31, "endColumn": 20, "suggestions": "167"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 47, "column": 7, "nodeType": "140", "messageId": "141", "endLine": 47, "endColumn": 20, "suggestions": "168"}, {"ruleId": "160", "severity": 1, "message": "169", "line": 12, "column": 18, "nodeType": "162", "messageId": "163", "endLine": 12, "endColumn": 30}, {"ruleId": "138", "severity": 1, "message": "139", "line": 23, "column": 9, "nodeType": "140", "messageId": "141", "endLine": 23, "endColumn": 22, "suggestions": "170"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 40, "column": 7, "nodeType": "140", "messageId": "141", "endLine": 40, "endColumn": 20, "suggestions": "171"}, {"ruleId": "164", "severity": 1, "message": "172", "line": 49, "column": 6, "nodeType": "166", "endLine": 49, "endColumn": 33, "suggestions": "173"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 97, "column": 9, "nodeType": "140", "messageId": "141", "endLine": 97, "endColumn": 22, "suggestions": "174"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 129, "column": 5, "nodeType": "140", "messageId": "141", "endLine": 129, "endColumn": 16, "suggestions": "175"}, {"ruleId": "176", "severity": 1, "message": "177", "line": 136, "column": 5, "nodeType": "178", "messageId": "179", "endLine": 177, "endColumn": 6}, {"ruleId": "176", "severity": 1, "message": "177", "line": 191, "column": 5, "nodeType": "178", "messageId": "179", "endLine": 205, "endColumn": 6}, {"ruleId": "138", "severity": 1, "message": "139", "line": 208, "column": 5, "nodeType": "140", "messageId": "141", "endLine": 208, "endColumn": 18, "suggestions": "180"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 24, "column": 7, "nodeType": "140", "messageId": "141", "endLine": 24, "endColumn": 20, "suggestions": "181"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 37, "column": 7, "nodeType": "140", "messageId": "141", "endLine": 37, "endColumn": 20, "suggestions": "182"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 65, "column": 7, "nodeType": "140", "messageId": "141", "endLine": 65, "endColumn": 20, "suggestions": "183"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 86, "column": 7, "nodeType": "140", "messageId": "141", "endLine": 86, "endColumn": 20, "suggestions": "184"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 245, "column": 7, "nodeType": "140", "messageId": "141", "endLine": 245, "endColumn": 20, "suggestions": "185"}, {"ruleId": "138", "severity": 1, "message": "139", "line": 264, "column": 7, "nodeType": "140", "messageId": "141", "endLine": 264, "endColumn": 20, "suggestions": "186"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["187"], ["188"], ["189"], ["190"], ["191"], ["192"], ["193"], ["194"], ["195"], ["196"], ["197"], ["198"], ["199"], ["200"], ["201"], ["202"], ["203"], ["204"], "no-unused-vars", "'sidebar' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'performSearch' and 'setSearchQuery'. Either include them or remove the dependency array.", "ArrayExpression", ["205"], ["206"], "'currentVideo' is assigned a value but never used.", ["207"], ["208"], "React Hook useEffect has a missing dependency: 'loadYouTubeAPI'. Either include it or remove the dependency array.", ["209"], ["210"], ["211"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", ["212"], ["213"], ["214"], ["215"], ["216"], ["217"], ["218"], {"messageId": "219", "data": "220", "fix": "221", "desc": "222"}, {"messageId": "219", "data": "223", "fix": "224", "desc": "222"}, {"messageId": "219", "data": "225", "fix": "226", "desc": "222"}, {"messageId": "219", "data": "227", "fix": "228", "desc": "229"}, {"messageId": "219", "data": "230", "fix": "231", "desc": "229"}, {"messageId": "219", "data": "232", "fix": "233", "desc": "229"}, {"messageId": "219", "data": "234", "fix": "235", "desc": "229"}, {"messageId": "219", "data": "236", "fix": "237", "desc": "229"}, {"messageId": "219", "data": "238", "fix": "239", "desc": "222"}, {"messageId": "219", "data": "240", "fix": "241", "desc": "222"}, {"messageId": "219", "data": "242", "fix": "243", "desc": "229"}, {"messageId": "219", "data": "244", "fix": "245", "desc": "229"}, {"messageId": "219", "data": "246", "fix": "247", "desc": "248"}, {"messageId": "219", "data": "249", "fix": "250", "desc": "229"}, {"messageId": "219", "data": "251", "fix": "252", "desc": "222"}, {"messageId": "219", "data": "253", "fix": "254", "desc": "229"}, {"messageId": "219", "data": "255", "fix": "256", "desc": "222"}, {"messageId": "219", "data": "257", "fix": "258", "desc": "229"}, {"desc": "259", "fix": "260"}, {"messageId": "219", "data": "261", "fix": "262", "desc": "222"}, {"messageId": "219", "data": "263", "fix": "264", "desc": "222"}, {"messageId": "219", "data": "265", "fix": "266", "desc": "222"}, {"desc": "267", "fix": "268"}, {"messageId": "219", "data": "269", "fix": "270", "desc": "222"}, {"messageId": "219", "data": "271", "fix": "272", "desc": "229"}, {"messageId": "219", "data": "273", "fix": "274", "desc": "222"}, {"messageId": "219", "data": "275", "fix": "276", "desc": "222"}, {"messageId": "219", "data": "277", "fix": "278", "desc": "222"}, {"messageId": "219", "data": "279", "fix": "280", "desc": "222"}, {"messageId": "219", "data": "281", "fix": "282", "desc": "222"}, {"messageId": "219", "data": "283", "fix": "284", "desc": "222"}, {"messageId": "219", "data": "285", "fix": "286", "desc": "222"}, "removeConsole", {"propertyName": "287"}, {"range": "288", "text": "289"}, "Remove the console.error().", {"propertyName": "287"}, {"range": "290", "text": "289"}, {"propertyName": "287"}, {"range": "291", "text": "289"}, {"propertyName": "292"}, {"range": "293", "text": "289"}, "Remove the console.log().", {"propertyName": "292"}, {"range": "294", "text": "289"}, {"propertyName": "292"}, {"range": "295", "text": "289"}, {"propertyName": "292"}, {"range": "296", "text": "289"}, {"propertyName": "292"}, {"range": "297", "text": "289"}, {"propertyName": "287"}, {"range": "298", "text": "289"}, {"propertyName": "287"}, {"range": "299", "text": "289"}, {"propertyName": "292"}, {"range": "300", "text": "289"}, {"propertyName": "292"}, {"range": "301", "text": "289"}, {"propertyName": "302"}, {"range": "303", "text": "289"}, "Remove the console.warn().", {"propertyName": "292"}, {"range": "304", "text": "289"}, {"propertyName": "287"}, {"range": "305", "text": "289"}, {"propertyName": "292"}, {"range": "306", "text": "289"}, {"propertyName": "287"}, {"range": "307", "text": "289"}, {"propertyName": "292"}, {"range": "308", "text": "289"}, "Update the dependencies array to be: [initialQuery, performSearch, setSearchQuery]", {"range": "309", "text": "310"}, {"propertyName": "287"}, {"range": "311", "text": "289"}, {"propertyName": "287"}, {"range": "312", "text": "289"}, {"propertyName": "287"}, {"range": "313", "text": "289"}, "Update the dependencies array to be: [videoId, loadVideoDetails, loadYouTubeAPI]", {"range": "314", "text": "315"}, {"propertyName": "287"}, {"range": "316", "text": "289"}, {"propertyName": "292"}, {"range": "317", "text": "289"}, {"propertyName": "287"}, {"range": "318", "text": "289"}, {"propertyName": "287"}, {"range": "319", "text": "289"}, {"propertyName": "287"}, {"range": "320", "text": "289"}, {"propertyName": "287"}, {"range": "321", "text": "289"}, {"propertyName": "287"}, {"range": "322", "text": "289"}, {"propertyName": "287"}, {"range": "323", "text": "289"}, {"propertyName": "287"}, {"range": "324", "text": "289"}, "error", [960, 1003], "", [1006, 1046], [1202, 1281], "log", [2086, 2186], [4018, 4082], [4420, 4465], [4521, 4580], [4727, 4771], [4853, 4897], [4964, 5024], [5159, 5192], [1806, 1853], "warn", [1908, 1956], [2301, 2345], [2378, 2428], [2666, 2699], [398, 438], [549, 605], [935, 949], "[initial<PERSON><PERSON><PERSON>, perform<PERSON>earch, setSearchQuery]", [1402, 1438], [792, 848], [1200, 1254], [1396, 1423], "[videoId, loadVideoDetails, loadYouTubeAPI]", [2724, 2783], [3623, 3679], [5506, 5570], [896, 942], [1283, 1336], [2283, 2333], [2879, 2936], [7750, 7798], [8378, 8430]]