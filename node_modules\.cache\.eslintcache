[{"C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\index.js": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\App.js": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\ErrorFallback.js": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\LoadingSpinner.js": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\layout\\Header.js": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Favorites.js": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\stores\\appStore.js": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\services\\router.js": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\About.js": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\layout\\Sidebar.js": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Settings.js": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\History.js": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Search.js": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Home.js": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Player.js": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\ThemeToggle.js": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\SearchBar.js": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\WindowControls.js": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\services\\youtubeAPI.js": "19"}, {"size": 5197, "mtime": 1754079032198, "results": "20", "hashOfConfig": "21"}, {"size": 6849, "mtime": 1754081279663, "results": "22", "hashOfConfig": "21"}, {"size": 4251, "mtime": 1754079194459, "results": "23", "hashOfConfig": "21"}, {"size": 1638, "mtime": 1754079153661, "results": "24", "hashOfConfig": "21"}, {"size": 6594, "mtime": 1754078762266, "results": "25", "hashOfConfig": "21"}, {"size": 1011, "mtime": 1754080425990, "results": "26", "hashOfConfig": "21"}, {"size": 7626, "mtime": 1754078647862, "results": "27", "hashOfConfig": "21"}, {"size": 4686, "mtime": 1754078673463, "results": "28", "hashOfConfig": "21"}, {"size": 3274, "mtime": 1754080437585, "results": "29", "hashOfConfig": "21"}, {"size": 7414, "mtime": 1754079356285, "results": "30", "hashOfConfig": "21"}, {"size": 5498, "mtime": 1754080375363, "results": "31", "hashOfConfig": "21"}, {"size": 1332, "mtime": 1754080413744, "results": "32", "hashOfConfig": "21"}, {"size": 7528, "mtime": 1754081099501, "results": "33", "hashOfConfig": "21"}, {"size": 11313, "mtime": 1754081382553, "results": "34", "hashOfConfig": "21"}, {"size": 9670, "mtime": 1754081145486, "results": "35", "hashOfConfig": "21"}, {"size": 2588, "mtime": 1754079256479, "results": "36", "hashOfConfig": "21"}, {"size": 7666, "mtime": 1754078833591, "results": "37", "hashOfConfig": "21"}, {"size": 2186, "mtime": 1754079287058, "results": "38", "hashOfConfig": "21"}, {"size": 10218, "mtime": 1754081021732, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1a6rq5h", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\index.js", ["97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\App.js", ["108", "109", "110", "111", "112"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\ErrorFallback.js", ["113", "114"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\layout\\Header.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Favorites.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\stores\\appStore.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\services\\router.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\About.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\layout\\Sidebar.js", ["115"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Settings.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\History.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Search.js", ["116", "117"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Home.js", ["118", "119"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\pages\\Player.js", ["120", "121", "122", "123", "124", "125", "126", "127"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\ThemeToggle.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\SearchBar.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\components\\common\\WindowControls.js", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app\\src\\services\\youtubeAPI.js", ["128", "129", "130", "131", "132", "133"], [], {"ruleId": "134", "severity": 1, "message": "135", "line": 31, "column": 3, "nodeType": "136", "messageId": "137", "endLine": 31, "endColumn": 16, "suggestions": "138"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 32, "column": 3, "nodeType": "136", "messageId": "137", "endLine": 32, "endColumn": 16, "suggestions": "139"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 37, "column": 5, "nodeType": "136", "messageId": "137", "endLine": 37, "endColumn": 18, "suggestions": "140"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 63, "column": 3, "nodeType": "136", "messageId": "137", "endLine": 63, "endColumn": 14, "suggestions": "141"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 139, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 139, "endColumn": 18, "suggestions": "142"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 151, "column": 9, "nodeType": "136", "messageId": "137", "endLine": 151, "endColumn": 20, "suggestions": "143"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 154, "column": 9, "nodeType": "136", "messageId": "137", "endLine": 154, "endColumn": 20, "suggestions": "144"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 162, "column": 5, "nodeType": "136", "messageId": "137", "endLine": 162, "endColumn": 16, "suggestions": "145"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 168, "column": 3, "nodeType": "136", "messageId": "137", "endLine": 168, "endColumn": 16, "suggestions": "146"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 172, "column": 3, "nodeType": "136", "messageId": "137", "endLine": 172, "endColumn": 16, "suggestions": "147"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 179, "column": 3, "nodeType": "136", "messageId": "137", "endLine": 179, "endColumn": 14, "suggestions": "148"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 58, "column": 15, "nodeType": "136", "messageId": "137", "endLine": 58, "endColumn": 26, "suggestions": "149"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 61, "column": 13, "nodeType": "136", "messageId": "137", "endLine": 61, "endColumn": 25, "suggestions": "150"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 72, "column": 9, "nodeType": "136", "messageId": "137", "endLine": 72, "endColumn": 20, "suggestions": "151"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 74, "column": 9, "nodeType": "136", "messageId": "137", "endLine": 74, "endColumn": 22, "suggestions": "152"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 84, "column": 9, "nodeType": "136", "messageId": "137", "endLine": 84, "endColumn": 20, "suggestions": "153"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 16, "column": 5, "nodeType": "136", "messageId": "137", "endLine": 16, "endColumn": 18, "suggestions": "154"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 20, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 20, "endColumn": 18, "suggestions": "155"}, {"ruleId": "156", "severity": 1, "message": "157", "line": 8, "column": 11, "nodeType": "158", "messageId": "159", "endLine": 8, "endColumn": 18}, {"ruleId": "160", "severity": 1, "message": "161", "line": 30, "column": 6, "nodeType": "162", "endLine": 30, "endColumn": 20, "suggestions": "163"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 46, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 46, "endColumn": 20, "suggestions": "164"}, {"ruleId": "156", "severity": 1, "message": "165", "line": 12, "column": 18, "nodeType": "158", "messageId": "159", "endLine": 12, "endColumn": 30}, {"ruleId": "134", "severity": 1, "message": "135", "line": 23, "column": 9, "nodeType": "136", "messageId": "137", "endLine": 23, "endColumn": 22, "suggestions": "166"}, {"ruleId": "156", "severity": 1, "message": "167", "line": 1, "column": 46, "nodeType": "158", "messageId": "159", "endLine": 1, "endColumn": 57}, {"ruleId": "160", "severity": 1, "message": "168", "line": 28, "column": 6, "nodeType": "162", "endLine": 28, "endColumn": 15, "suggestions": "169"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 46, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 46, "endColumn": 20, "suggestions": "170"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 94, "column": 9, "nodeType": "136", "messageId": "137", "endLine": 94, "endColumn": 22, "suggestions": "171"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 126, "column": 5, "nodeType": "136", "messageId": "137", "endLine": 126, "endColumn": 16, "suggestions": "172"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 133, "column": 5, "nodeType": "175", "messageId": "176", "endLine": 174, "endColumn": 6}, {"ruleId": "173", "severity": 1, "message": "174", "line": 188, "column": 5, "nodeType": "175", "messageId": "176", "endLine": 202, "endColumn": 6}, {"ruleId": "134", "severity": 1, "message": "135", "line": 205, "column": 5, "nodeType": "136", "messageId": "137", "endLine": 205, "endColumn": 18, "suggestions": "177"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 50, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 50, "endColumn": 20, "suggestions": "178"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 83, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 83, "endColumn": 20, "suggestions": "179"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 110, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 110, "endColumn": 20, "suggestions": "180"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 127, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 127, "endColumn": 20, "suggestions": "181"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 302, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 302, "endColumn": 20, "suggestions": "182"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 324, "column": 7, "nodeType": "136", "messageId": "137", "endLine": 324, "endColumn": 20, "suggestions": "183"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["184"], ["185"], ["186"], ["187"], ["188"], ["189"], ["190"], ["191"], ["192"], ["193"], ["194"], ["195"], ["196"], ["197"], ["198"], ["199"], ["200"], ["201"], "no-unused-vars", "'sidebar' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'performSearch' and 'setSearchQuery'. Either include them or remove the dependency array.", "ArrayExpression", ["202"], ["203"], "'currentVideo' is assigned a value but never used.", ["204"], "'useCallback' is defined but never used.", "React Hook useEffect has missing dependencies: 'loadVideoDetails' and 'loadYouTubeAPI'. Either include them or remove the dependency array.", ["205"], ["206"], ["207"], ["208"], "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", ["209"], ["210"], ["211"], ["212"], ["213"], ["214"], ["215"], {"messageId": "216", "data": "217", "fix": "218", "desc": "219"}, {"messageId": "216", "data": "220", "fix": "221", "desc": "219"}, {"messageId": "216", "data": "222", "fix": "223", "desc": "219"}, {"messageId": "216", "data": "224", "fix": "225", "desc": "226"}, {"messageId": "216", "data": "227", "fix": "228", "desc": "226"}, {"messageId": "216", "data": "229", "fix": "230", "desc": "226"}, {"messageId": "216", "data": "231", "fix": "232", "desc": "226"}, {"messageId": "216", "data": "233", "fix": "234", "desc": "226"}, {"messageId": "216", "data": "235", "fix": "236", "desc": "219"}, {"messageId": "216", "data": "237", "fix": "238", "desc": "219"}, {"messageId": "216", "data": "239", "fix": "240", "desc": "226"}, {"messageId": "216", "data": "241", "fix": "242", "desc": "226"}, {"messageId": "216", "data": "243", "fix": "244", "desc": "245"}, {"messageId": "216", "data": "246", "fix": "247", "desc": "226"}, {"messageId": "216", "data": "248", "fix": "249", "desc": "219"}, {"messageId": "216", "data": "250", "fix": "251", "desc": "226"}, {"messageId": "216", "data": "252", "fix": "253", "desc": "219"}, {"messageId": "216", "data": "254", "fix": "255", "desc": "226"}, {"desc": "256", "fix": "257"}, {"messageId": "216", "data": "258", "fix": "259", "desc": "219"}, {"messageId": "216", "data": "260", "fix": "261", "desc": "219"}, {"desc": "262", "fix": "263"}, {"messageId": "216", "data": "264", "fix": "265", "desc": "219"}, {"messageId": "216", "data": "266", "fix": "267", "desc": "219"}, {"messageId": "216", "data": "268", "fix": "269", "desc": "226"}, {"messageId": "216", "data": "270", "fix": "271", "desc": "219"}, {"messageId": "216", "data": "272", "fix": "273", "desc": "219"}, {"messageId": "216", "data": "274", "fix": "275", "desc": "219"}, {"messageId": "216", "data": "276", "fix": "277", "desc": "219"}, {"messageId": "216", "data": "278", "fix": "279", "desc": "219"}, {"messageId": "216", "data": "280", "fix": "281", "desc": "219"}, {"messageId": "216", "data": "282", "fix": "283", "desc": "219"}, "removeConsole", {"propertyName": "284"}, {"range": "285", "text": "286"}, "Remove the console.error().", {"propertyName": "284"}, {"range": "287", "text": "286"}, {"propertyName": "284"}, {"range": "288", "text": "286"}, {"propertyName": "289"}, {"range": "290", "text": "286"}, "Remove the console.log().", {"propertyName": "289"}, {"range": "291", "text": "286"}, {"propertyName": "289"}, {"range": "292", "text": "286"}, {"propertyName": "289"}, {"range": "293", "text": "286"}, {"propertyName": "289"}, {"range": "294", "text": "286"}, {"propertyName": "284"}, {"range": "295", "text": "286"}, {"propertyName": "284"}, {"range": "296", "text": "286"}, {"propertyName": "289"}, {"range": "297", "text": "286"}, {"propertyName": "289"}, {"range": "298", "text": "286"}, {"propertyName": "299"}, {"range": "300", "text": "286"}, "Remove the console.warn().", {"propertyName": "289"}, {"range": "301", "text": "286"}, {"propertyName": "284"}, {"range": "302", "text": "286"}, {"propertyName": "289"}, {"range": "303", "text": "286"}, {"propertyName": "284"}, {"range": "304", "text": "286"}, {"propertyName": "289"}, {"range": "305", "text": "286"}, "Update the dependencies array to be: [initialQuery, performSearch, setSearchQuery]", {"range": "306", "text": "307"}, {"propertyName": "284"}, {"range": "308", "text": "286"}, {"propertyName": "284"}, {"range": "309", "text": "286"}, "Update the dependencies array to be: [loadVideoDetails, loadYouTubeAPI, videoId]", {"range": "310", "text": "311"}, {"propertyName": "284"}, {"range": "312", "text": "286"}, {"propertyName": "284"}, {"range": "313", "text": "286"}, {"propertyName": "289"}, {"range": "314", "text": "286"}, {"propertyName": "284"}, {"range": "315", "text": "286"}, {"propertyName": "284"}, {"range": "316", "text": "286"}, {"propertyName": "284"}, {"range": "317", "text": "286"}, {"propertyName": "284"}, {"range": "318", "text": "286"}, {"propertyName": "284"}, {"range": "319", "text": "286"}, {"propertyName": "284"}, {"range": "320", "text": "286"}, {"propertyName": "284"}, {"range": "321", "text": "286"}, "error", [960, 1003], "", [1006, 1046], [1202, 1281], "log", [2086, 2186], [4018, 4082], [4420, 4465], [4521, 4580], [4727, 4771], [4853, 4897], [4964, 5024], [5159, 5192], [1806, 1853], "warn", [1908, 1956], [2301, 2345], [2378, 2428], [2666, 2699], [398, 438], [549, 605], [894, 908], "[initial<PERSON><PERSON><PERSON>, perform<PERSON>earch, setSearchQuery]", [1361, 1397], [792, 848], [770, 779], "[loadVideoDetails, loadYouTubeAPI, videoId]", [1260, 1314], [2622, 2681], [3521, 3577], [5404, 5468], [1610, 1656], [2502, 2555], [3242, 3286], [3729, 3780], [9111, 9159], [9737, 9783]]