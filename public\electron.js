const { app, BrowserWindow, Menu, ipc<PERSON>ain, session } = require('electron');
const path = require('path');
const isDev = process.env.ELECTRON_IS_DEV === '1';

let mainWindow;

// إعداد مانع الإعلانات المحسن
const adBlockFilters = [
  // إعلانات YouTube الأساسية
  '*://*.doubleclick.net/*',
  '*://*.googleadservices.com/*',
  '*://*.googlesyndication.com/*',
  '*://googleads.g.doubleclick.net/*',
  '*://*.youtube.com/api/stats/ads*',
  '*://*.youtube.com/ptracking*',
  '*://*.youtube.com/pagead/*',
  '*://www.youtube.com/api/stats/ads*',
  '*://www.youtube.com/ptracking*',
  '*://www.youtube.com/pagead/*',

  // إعلانات YouTube المتقدمة
  '*://*.youtube.com/get_video_info*ad*',
  '*://*.youtube.com/youtubei/v1/player/ad_break*',
  '*://*.youtube.com/api/stats/watchtime*',
  '*://www.youtube.com/api/stats/watchtime*',
  '*://*.youtube.com/youtubei/v1/log_event*',

  // إعلانات Google العامة
  '*://*.adsystem.com/*',
  '*://*.amazon-adsystem.com/*',
  '*://*.google-analytics.com/*',
  '*://*.googletagmanager.com/*',

  // متتبعات أخرى
  '*://*.scorecardresearch.com/*',
  '*://*.outbrain.com/*',
  '*://*.taboola.com/*',
  '*://*.adsafeprotected.com/*',
  '*://*.moatads.com/*'
];

// CSS لإخفاء الإعلانات
const adBlockCSS = `
  /* إخفاء إعلانات YouTube */
  .ytd-display-ad-renderer,
  .ytd-promoted-sparkles-web-renderer,
  .ytd-ad-slot-renderer,
  .ytp-ad-module,
  .ytp-ad-overlay-container,
  .ytp-ad-text-overlay,
  .video-ads,
  .masthead-ad-control,
  #player-ads,
  .ad-container,
  .ytd-banner-promo-renderer,
  .ytd-video-masthead-ad-v3-renderer,
  .ytd-primetime-promo-renderer {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
  }

  /* تحسين مظهر الصفحة */
  #masthead {
    background: #1a1a1a !important;
  }

  /* إخفاء عناصر غير مرغوبة */
  .ytd-guide-renderer,
  #guide,
  #secondary {
    display: none !important;
  }

  /* توسيط المحتوى */
  #primary {
    margin: 0 auto !important;
    max-width: 100% !important;
  }
`;

function createWindow() {
  // إنشاء نافذة المتصفح
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1000,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true,
      webSecurity: false,
      webviewTag: true,
      allowRunningInsecureContent: true
    },
    icon: path.join(__dirname, '../assets/icon.png'),
    title: 'مشغل يوتيوب المتقدم',
    show: false,
    backgroundColor: '#1a1a1a',
    titleBarStyle: 'hidden',
    frame: false
  });

  // تحميل التطبيق الهجين الجديد
  const startUrl = isDev
    ? 'http://localhost:3000'  // في وضع التطوير نحمل التطبيق المحلي
    : `file://${path.join(__dirname, '../build/index.html')}`; // في الإنتاج نحمل الملفات المبنية

  mainWindow.loadURL(startUrl);

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // حقن الواجهة العربية ومانع الإعلانات بعد تحميل الصفحة
    setTimeout(() => {
      injectAdBlockCSS();
      injectArabicInterface();
    }, 3000);
  });

  // إعداد مانع الإعلانات المحسن
  setupAdBlocker();

  // إعداد معالجات الرسائل
  setupIPCHandlers();

  // فتح أدوات المطور في وضع التطوير
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // إزالة شريط القوائم
  Menu.setApplicationMenu(null);



  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// إعداد مانع الإعلانات
function setupAdBlocker() {
  // حجب الطلبات الإعلانية
  session.defaultSession.webRequest.onBeforeRequest({ urls: adBlockFilters }, (details, callback) => {
    console.log('🛡️ تم حجب إعلان:', details.url);
    callback({ cancel: true });
  });

  // تعديل الهيدرز لمنع التتبع
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    const responseHeaders = { ...details.responseHeaders };

    // إزالة هيدرز التتبع
    delete responseHeaders['x-frame-options'];
    delete responseHeaders['content-security-policy'];

    callback({ responseHeaders });
  });

  // حجب الإعلانات بناءً على نوع المحتوى
  session.defaultSession.webRequest.onBeforeRequest({ urls: ['*://*/*'] }, (details, callback) => {
    const url = details.url.toLowerCase();

    // حجب الإعلانات المخفية
    if (url.includes('ads') ||
        url.includes('advertisement') ||
        url.includes('doubleclick') ||
        url.includes('googleadservices') ||
        url.includes('googlesyndication') ||
        url.includes('adsystem') ||
        (url.includes('youtube.com') && url.includes('ad'))) {
      console.log('🛡️ تم حجب محتوى إعلاني:', details.url);
      callback({ cancel: true });
      return;
    }

    callback({});
  });
}

// حقن CSS لمانع الإعلانات
function injectAdBlockCSS() {
  if (mainWindow && mainWindow.webContents) {
    mainWindow.webContents.insertCSS(adBlockCSS);
    console.log('✅ تم حقن CSS مانع الإعلانات');
  }
}

// حقن الواجهة العربية
function injectArabicInterface() {
  if (mainWindow && mainWindow.webContents) {
    const arabicInterfaceScript = `
      // إنشاء الواجهة العربية
      (function() {
        console.log('🌐 بدء حقن الواجهة العربية...');

        // إنشاء شريط علوي عربي
        const arabicHeader = document.createElement('div');
        arabicHeader.id = 'arabic-header';
        arabicHeader.innerHTML = \`
          <div style="
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: linear-gradient(135deg, #ff0000, #cc0000);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 10000;
            font-family: 'Segoe UI', 'Cairo', 'Tahoma', sans-serif;
            direction: rtl;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
          ">
            <div style="display: flex; align-items: center; gap: 15px;">
              <div style="display: flex; align-items: center; gap: 10px; font-size: 18px; font-weight: bold;">
                <span>🌐</span>
                <span>مشغل يوتيوب المتقدم</span>
              </div>

              <div style="
                display: flex;
                align-items: center;
                background: rgba(255,255,255,0.2);
                border-radius: 20px;
                padding: 8px 15px;
                gap: 10px;
              ">
                <input
                  type="text"
                  id="arabic-search"
                  placeholder="ابحث في يوتيوب..."
                  style="
                    background: none;
                    border: none;
                    color: white;
                    font-size: 14px;
                    width: 300px;
                    outline: none;
                    direction: rtl;
                  "
                />
                <button onclick="performArabicSearch()" style="
                  background: none;
                  border: none;
                  color: white;
                  cursor: pointer;
                  font-size: 16px;
                ">🔍</button>
              </div>
            </div>

            <div style="display: flex; align-items: center; gap: 15px;">
              <div style="
                display: flex;
                align-items: center;
                gap: 15px;
                background: rgba(255,255,255,0.1);
                padding: 6px 12px;
                border-radius: 15px;
                font-size: 12px;
              ">
                <span title="إعلانات محجوبة">🛡️ <span id="ads-blocked-count">0</span></span>
                <span title="فيديوهات مشاهدة">🎥 <span id="videos-watched-count">0</span></span>
              </div>

              <button onclick="toggleAdBlocker()" style="
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
              " title="مانع الإعلانات">🛡️</button>

              <button onclick="showDeveloperInfo()" style="
                background: rgba(255,255,255,0.2);
                border: none;
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 12px;
              " title="معلومات المطور">👨‍💻</button>
            </div>
          </div>
        \`;

        document.body.appendChild(arabicHeader);

        // تعديل تخطيط يوتيوب ليتناسب مع الشريط العلوي
        const ytdApp = document.querySelector('ytd-app');
        if (ytdApp) {
          ytdApp.style.marginTop = '60px';
        }

        // إضافة شريط حالة سفلي
        const statusBar = document.createElement('div');
        statusBar.id = 'arabic-status-bar';
        statusBar.innerHTML = \`
          <div style="
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: #1a1a1a;
            color: #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            z-index: 9999;
            font-size: 12px;
            border-top: 1px solid #333;
          ">
            <span>🌐 يوتيوب مباشر</span>
            <span>🛡️ مانع الإعلانات مفعل</span>
            <span>📊 <span id="status-ads-blocked">0</span> إعلان محجوب</span>
            <span>💰 تطوير: YouTube Player Developer</span>
          </div>
        \`;

        document.body.appendChild(statusBar);

        // دوال JavaScript للواجهة
        window.performArabicSearch = function() {
          const searchInput = document.getElementById('arabic-search');
          if (searchInput && searchInput.value.trim()) {
            const searchQuery = encodeURIComponent(searchInput.value.trim());
            window.location.href = \`https://www.youtube.com/results?search_query=\${searchQuery}\`;
          }
        };

        window.toggleAdBlocker = function() {
          alert('مانع الإعلانات مفعل ويعمل في الخلفية!');
        };

        window.showDeveloperInfo = function() {
          alert('تطوير: YouTube Player Developer\\nالإصدار: 2.0.0\\nالبريد: <EMAIL>');
        };

        // تحديث إحصائيات مانع الإعلانات
        let adsBlockedCount = 0;
        let videosWatchedCount = 0;

        function updateStats() {
          adsBlockedCount++;
          document.getElementById('ads-blocked-count').textContent = adsBlockedCount;
          document.getElementById('status-ads-blocked').textContent = adsBlockedCount;
        }

        // مراقبة الفيديوهات الجديدة
        function monitorVideos() {
          const videos = document.querySelectorAll('video');
          videos.forEach(video => {
            if (!video.hasAttribute('data-monitored')) {
              video.setAttribute('data-monitored', 'true');
              video.addEventListener('play', () => {
                videosWatchedCount++;
                document.getElementById('videos-watched-count').textContent = videosWatchedCount;
              });
            }
          });
        }

        // تشغيل مراقبة الفيديوهات كل ثانيتين
        setInterval(monitorVideos, 2000);

        // تحديث إحصائيات مانع الإعلانات كل 5 ثوان
        setInterval(updateStats, 5000);

        // إضافة مستمع للبحث بالضغط على Enter
        document.getElementById('arabic-search').addEventListener('keypress', function(e) {
          if (e.key === 'Enter') {
            performArabicSearch();
          }
        });

        console.log('✅ تم حقن الواجهة العربية بنجاح');
      })();
    `;

    mainWindow.webContents.executeJavaScript(arabicInterfaceScript);
    console.log('🌐 تم حقن الواجهة العربية');
  }
}

// إعداد معالجات IPC
function setupIPCHandlers() {
  // معالجة الرسائل من العملية المُرسِلة
  ipcMain.handle('get-app-version', () => {
    return app.getVersion();
  });

  ipcMain.handle('minimize-window', () => {
    if (mainWindow) {
      mainWindow.minimize();
    }
  });

  ipcMain.handle('maximize-window', () => {
    if (mainWindow) {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      } else {
        mainWindow.maximize();
      }
    }
  });

  ipcMain.handle('close-window', () => {
    if (mainWindow) {
      mainWindow.close();
    }
  });

  // معالجة حقن CSS إضافي
  ipcMain.handle('inject-css', (event, css) => {
    if (mainWindow && mainWindow.webContents) {
      mainWindow.webContents.insertCSS(css);
      return true;
    }
    return false;
  });

  // معالجة تنفيذ JavaScript
  ipcMain.handle('execute-js', (event, script) => {
    if (mainWindow && mainWindow.webContents) {
      return mainWindow.webContents.executeJavaScript(script);
    }
    return null;
  });
}

// هذه الطريقة ستُستدعى عندما يكون Electron جاهزاً
app.whenReady().then(createWindow);

// الخروج عندما تُغلق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});



