const { app, BrowserWindow, Menu, ipcMain, session, shell, dialog } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';

// Keep a global reference of the window object
let mainWindow;

// Security: Content Security Policy
const CSP = `
  default-src 'self';
  script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.youtube.com https://www.gstatic.com;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https: blob:;
  media-src 'self' https: blob:;
  connect-src 'self' https: wss: ws:;
  frame-src 'self' https://www.youtube.com https://www.youtube-nocookie.com;
  object-src 'none';
  base-uri 'self';
  form-action 'self';
`;

function createWindow() {
  // Create the browser window with security settings
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1000,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,
      preload: path.join(__dirname, 'preload.js'),
      additionalArguments: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
    },
    icon: path.join(__dirname, 'assets', 'icon.png'),
    title: 'YouTube Player Pro - مشغل يوتيوب احترافي',
    show: false,
    backgroundColor: '#0f0f0f',
    titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
    frame: true,
    autoHideMenuBar: true
  });

  // Load the app
  const startUrl = isDev 
    ? 'http://localhost:3000' 
    : `file://${path.join(__dirname, '../build/index.html')}`;

  mainWindow.loadURL(startUrl);

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Prevent navigation to external sites
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.origin !== 'http://localhost:3000' && !navigationUrl.startsWith('file://')) {
      event.preventDefault();
    }
  });

  // Set up security headers
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [CSP],
        'X-Frame-Options': ['DENY'],
        'X-Content-Type-Options': ['nosniff'],
        'Referrer-Policy': ['strict-origin-when-cross-origin'],
        'Permissions-Policy': ['camera=(), microphone=(), geolocation=()']
      }
    });
  });

  // Block ads and trackers
  setupAdBlocker();
  
  // Set up IPC handlers
  setupIPCHandlers();
  
  // Remove default menu
  Menu.setApplicationMenu(null);
}

function setupAdBlocker() {
  const filter = {
    urls: ['*://*/*']
  };

  // Enhanced ad blocking patterns
  const adPatterns = [
    // Google Ads
    /doubleclick\.net/,
    /googleadservices\.com/,
    /googlesyndication\.com/,
    /googletagmanager\.com/,
    /google-analytics\.com/,
    /adsense\.google\.com/,
    /pagead2\.googlesyndication\.com/,
    /tpc\.googlesyndication\.com/,

    // YouTube specific ads
    /youtube\.com\/api\/stats/,
    /youtube\.com\/ptracking/,
    /youtube\.com\/youtubei\/v1\/log_event/,
    /youtube\.com\/youtubei\/v1\/player\/ad_break/,
    /youtube\.com\/get_midroll_info/,
    /youtube\.com\/pagead/,
    /youtube\.com\/pcs\/click/,
    /youtube\.com\/generate_204/,

    // Social media trackers
    /facebook\.com\/tr/,
    /connect\.facebook\.net/,
    /twitter\.com\/i\/adsct/,
    /analytics\.twitter\.com/,

    // Other ad networks
    /amazon-adsystem\.com/,
    /adsystem\.amazon/,
    /ads\.yahoo\.com/,
    /bing\.com\/ads/,
    /outbrain\.com/,
    /taboola\.com/,
    /criteo\.com/,
    /adsystem\.com/,
    /adnxs\.com/,
    /adsafeprotected\.com/,
    /moatads\.com/,
    /scorecardresearch\.com/,
    /quantserve\.com/,

    // Video ad providers
    /imasdk\.googleapis\.com/,
    /pubads\.g\.doubleclick\.net/,
    /securepubads\.g\.doubleclick\.net/,
    /video-ad-stats\.googlesyndication\.com/,

    // Analytics and tracking
    /hotjar\.com/,
    /fullstory\.com/,
    /mixpanel\.com/,
    /segment\.com/,
    /amplitude\.com/
  ];

  // Ad blocking statistics
  let adsBlocked = 0;

  session.defaultSession.webRequest.onBeforeRequest(filter, (details, callback) => {
    const url = details.url;
    const shouldBlock = adPatterns.some(pattern => pattern.test(url));

    if (shouldBlock) {
      adsBlocked++;
      console.log(`Blocked ad #${adsBlocked}:`, url);

      // Send statistics to renderer process
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('ad-blocked', {
          url,
          totalBlocked: adsBlocked,
          timestamp: new Date().toISOString()
        });
      }

      callback({ cancel: true });
    } else {
      callback({ cancel: false });
    }
  });
}

function setupIPCHandlers() {
  // Handle app info requests
  ipcMain.handle('get-app-info', () => {
    return {
      name: app.getName(),
      version: app.getVersion(),
      platform: process.platform,
      arch: process.arch
    };
  });

  // Handle settings
  ipcMain.handle('save-settings', async (event, settings) => {
    // In a real app, you'd save to a file or database
    console.log('Saving settings:', settings);
    return true;
  });

  ipcMain.handle('load-settings', async () => {
    // In a real app, you'd load from a file or database
    return {
      theme: 'dark',
      language: 'ar',
      quality: 'auto',
      adBlock: true
    };
  });

  // Handle window controls
  ipcMain.handle('minimize-window', () => {
    if (mainWindow) {
      mainWindow.minimize();
    }
  });

  ipcMain.handle('maximize-window', () => {
    if (mainWindow) {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      } else {
        mainWindow.maximize();
      }
    }
  });

  ipcMain.handle('close-window', () => {
    if (mainWindow) {
      mainWindow.close();
    }
  });

  // Handle external links
  ipcMain.handle('open-external', async (event, url) => {
    await shell.openExternal(url);
  });

  // Handle file dialogs
  ipcMain.handle('show-save-dialog', async (event, options) => {
    const result = await dialog.showSaveDialog(mainWindow, options);
    return result;
  });

  ipcMain.handle('show-open-dialog', async (event, options) => {
    const result = await dialog.showOpenDialog(mainWindow, options);
    return result;
  });
}

// App event handlers
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    shell.openExternal(navigationUrl);
  });
});

// Handle certificate errors
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (isDev) {
    // In development, ignore certificate errors
    event.preventDefault();
    callback(true);
  } else {
    // In production, use default behavior
    callback(false);
  }
});

// Prevent navigation to external protocols
app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:' && parsedUrl.protocol !== 'file:') {
      event.preventDefault();
    }
  });
});
