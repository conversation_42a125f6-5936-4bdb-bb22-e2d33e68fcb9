{"version": 3, "file": "static/js/906.dfded4d9.chunk.js", "mappings": "yMAMA,MAwTA,EAxTeA,IAAsB,IAArB,YAAEC,GAAaD,EAC7B,MAAOE,EAAeC,IAAoBC,EAAAA,EAAAA,WAAS,IAC5CC,EAAaC,IAAkBF,EAAAA,EAAAA,UAAS,MACzCG,GAAYC,EAAAA,EAAAA,QAAO,MACnBC,GAAoBD,EAAAA,EAAAA,QAAO,OAE3B,aACJE,EAAY,gBACZC,EAAe,iBACfC,EAAgB,uBAChBC,EAAsB,SACtBC,IACEC,EAAAA,EAAAA,KAEEC,GAAqB,OAAXf,QAAW,IAAXA,OAAW,EAAXA,EAAae,UAAWN,EAAaO,IAErDC,EAAAA,EAAAA,WAAU,KACJF,IACFG,IACAC,MAED,CAACJ,IAEJ,MAAMI,EAAmBC,UACvB,GAAKL,EAEL,IACE,MAAMM,QAAgBC,EAAAA,EAAWC,gBAAgBR,GACjDL,EAAgB,CACdM,GAAID,EACJS,MAAOH,EAAQG,MACfC,QAASJ,EAAQI,QACjBC,SAAUL,EAAQK,SAClBC,UAAWN,EAAQM,UACnBC,YAAaP,EAAQO,YACrBC,MAAOR,EAAQQ,MACfC,YAAaT,EAAQS,aAEzB,CAAE,MAAOC,GACPC,QAAQD,MAAM,gCAAiCA,EACjD,GAGIb,EAAiBA,KACrB,GAAKe,OAAOC,GAUVC,QAVc,CACd,MAAMC,EAASC,SAASC,cAAc,UACtCF,EAAOG,IAAM,qCACbH,EAAOhB,OAAQ,EACfiB,SAASG,KAAKC,YAAYL,GAE1BH,OAAOS,wBAA0B,KAC/BP,IAEJ,GAKIA,EAAmBA,KAAO,IAADQ,EAC7B,GAAIrC,EAAUsC,SAAoB,QAAbD,EAAIV,OAAOC,UAAE,IAAAS,GAATA,EAAWE,OAClC,IACErC,EAAkBoC,QAAU,IAAIX,OAAOC,GAAGW,OAAOvC,EAAUsC,QAAS,CAClEE,OAAQ,OACRC,MAAO,OACPhC,QAASA,EACTiC,WAAY,CACVC,SAAUpC,EAASoC,SAAW,EAAI,EAClCC,SAAUrC,EAASsC,aAAe,EAAI,EACtCC,UAAWvC,EAASsC,aAAe,EAAI,EACvCE,GAAI,EACJC,eAAgB,EAChBC,eAAgB,EAChBC,YAAa,EACbC,IAAK,EACLC,SAAU,EACVC,eAAgB,EAChBC,GAAI,KACJC,aAAc,KACdC,OAAQ7B,OAAO8B,SAASD,QAE1BE,OAAQ,CACNC,QAASC,EACTC,cAAeC,EACfC,QAASC,IAGf,CAAE,MAAOvC,GACPC,QAAQD,MAAM,qCAAsCA,GACpD1B,EAAe,qIACjB,GAIE6D,EAAiBK,IACrBrE,GAAiB,GACjBG,EAAe,WAGSmE,IAApB3D,EAAS4D,QACXF,EAAMG,OAAOC,UAAU9D,EAAS4D,QAIlC,MAAMG,EAAYL,EAAMG,OAAOG,eAC/BnE,EAAgB,CACdM,GAAID,EACJS,MAAOoD,EAAUpD,OAAS,sEAC1BC,QAASmD,EAAUE,QAAU,gEAC7BpD,SAAU6C,EAAMG,OAAOK,eAAiB,EACxCC,YAAa,EACbC,WAAW,EACXC,UAAU,EACVC,aAAa,EACbC,QAASb,EAAMG,OAAOW,sBAAwB,OAC9CZ,OAAQF,EAAMG,OAAOY,aAAe,IACpCC,MAAOhB,EAAMG,OAAOc,YAAa,EACjCC,YAAY,IAGdzD,QAAQ0D,IAAI,kCAAmC3E,IAG3CqD,EAAuBG,IAC3B,MAAMoB,EAAQpB,EAAMqB,KACdC,EAAStB,EAAMG,OAErB,OAAQiB,GACN,KAAK1D,OAAOC,GAAG4D,YAAYC,QACzBpF,EAAiB,CACfsE,WAAW,EACXC,UAAU,EACVC,aAAa,IAEf,MAEF,KAAKlD,OAAOC,GAAG4D,YAAYE,OACzBrF,EAAiB,CACfsE,WAAW,EACXC,UAAU,EACVC,aAAa,IAEf,MAEF,KAAKlD,OAAOC,GAAG4D,YAAYG,UACzBtF,EAAiB,CACfwE,aAAa,IAEf,MAEF,KAAKlD,OAAOC,GAAG4D,YAAYI,MACzBvF,EAAiB,CACfsE,WAAW,EACXC,UAAU,EACVC,aAAa,EACbH,YAAaa,EAAOd,gBAEtBnE,IACA,MAEF,KAAKqB,OAAOC,GAAG4D,YAAYK,KACzBxF,EAAiB,CACfsE,WAAW,EACXC,UAAU,EACVC,aAAa,EACbH,YAAa,IAMfa,EAAOO,gBACTzF,EAAiB,CACfqE,YAAaa,EAAOO,oBAKpB9B,EAAiBC,IACrB,MAAM8B,EAAY9B,EAAMqB,KACxB,IAAIU,EAAe,+HAEnB,OAAQD,GACN,KAAK,EACHC,EAAe,kHACf,MACF,KAAK,EACHA,EAAe,iEACf,MACF,KAAK,IACHA,EAAe,2IACf,MACF,KAAK,IACL,KAAK,IACHA,EAAe,kOAInBjG,EAAeiG,GACftE,QAAQD,MAAM,wBAAyBsE,EAAWC,IAcpD,OAAKvF,GAiBHwF,EAAAA,EAAAA,KAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,cACVC,QAAS,CAAEC,QAAS,GACpBC,QAAS,CAAED,QAAS,GACpBE,WAAY,CAAEpF,SAAU,IAAMqF,UAE9BC,EAAAA,EAAAA,MAAA,OAAKN,UAAU,mBAAkBK,SAAA,EAC/BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,iBAAgBK,SAC5B3G,GACC4G,EAAAA,EAAAA,MAAA,OAAKN,UAAU,eAAcK,SAAA,EAC3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,aAAYK,UACzBR,EAAAA,EAAAA,KAAA,OAAKxD,MAAM,KAAKD,OAAO,KAAKmE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,+HAGZZ,EAAAA,EAAAA,KAAA,MAAAQ,SAAI,+GACJR,EAAAA,EAAAA,KAAA,KAAAQ,SAAI3G,KACJ4G,EAAAA,EAAAA,MAAA,UAAQN,UAAU,eAAeU,QA7CzBC,KAClBhH,EAAe,MACfH,GAAiB,GACbM,EAAkBoC,SACpBpC,EAAkBoC,QAAQ0E,UAE5BC,WAAW,KACTpF,KACC,MAqC6D4E,SAAA,EACpDR,EAAAA,EAAAA,KAAA,OAAKxD,MAAM,KAAKD,OAAO,KAAKmE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,iNACJ,yFAKVH,EAAAA,EAAAA,MAAAQ,EAAAA,SAAA,CAAAT,SAAA,EACI9G,IACA+G,EAAAA,EAAAA,MAAA,OAAKN,UAAU,iBAAgBK,SAAA,EAC7BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,kBAAiBK,UAC9BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,eAEjBH,EAAAA,EAAAA,KAAA,KAAAQ,SAAG,8GAGPR,EAAAA,EAAAA,KAAA,OACEkB,IAAKnH,EACLoG,UAAW,mBAAmBzG,EAA2B,GAAX,iBAOrDQ,EAAae,QACZwF,EAAAA,EAAAA,MAACR,EAAAA,EAAOC,IAAG,CACTC,UAAU,aACVC,QAAS,CAAEC,QAAS,EAAGc,EAAG,IAC1Bb,QAAS,CAAED,QAAS,EAAGc,EAAG,GAC1BZ,WAAY,CAAEa,MAAO,IAAMZ,SAAA,EAE3BR,EAAAA,EAAAA,KAAA,MAAIG,UAAU,cAAaK,SAAEtG,EAAae,SAC1CwF,EAAAA,EAAAA,MAAA,OAAKN,UAAU,aAAYK,SAAA,EACzBR,EAAAA,EAAAA,KAAA,QAAMG,UAAU,gBAAeK,SAAEtG,EAAagB,UAC7ChB,EAAaiB,SAAW,IACvBsF,EAAAA,EAAAA,MAAA,QAAMN,UAAU,iBAAgBK,SAAA,CAAC,mCACvBa,KAAKC,MAAMpH,EAAaiB,SAAW,IAAI,KAAGjB,EAAaiB,SAAW,IAAIoG,WAAWC,SAAS,EAAG,cAQ9GlH,EAASmH,UACRhB,EAAAA,EAAAA,MAACR,EAAAA,EAAOC,IAAG,CACTC,UAAU,oBACVC,QAAS,CAAEC,QAAS,EAAGqB,MAAO,IAC9BpB,QAAS,CAAED,QAAS,EAAGqB,MAAO,GAC9BnB,WAAY,CAAEa,MAAO,IAAMZ,SAAA,EAE3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaK,UAC1BR,EAAAA,EAAAA,KAAA,OAAKxD,MAAM,KAAKD,OAAO,KAAKmE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,sGAGZZ,EAAAA,EAAAA,KAAA,QAAAQ,SAAM,gQA1FZR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,cAAaK,UAC1BC,EAAAA,EAAAA,MAAA,OAAKN,UAAU,eAAcK,SAAA,EAC3BR,EAAAA,EAAAA,KAAA,OAAKG,UAAU,aAAYK,UACzBR,EAAAA,EAAAA,KAAA,OAAKxD,MAAM,KAAKD,OAAO,KAAKmE,QAAQ,YAAYC,KAAK,eAAcH,UACjER,EAAAA,EAAAA,KAAA,QAAMY,EAAE,uBAGZZ,EAAAA,EAAAA,KAAA,MAAAQ,SAAI,qHACJR,EAAAA,EAAAA,KAAA,KAAAQ,SAAG,sP,kCCsGb,MACA,EADmB,IAvUnB,MACEmB,WAAAA,GAGEC,KAAKC,OAASC,CAAAA,SAAAA,aAAAA,WAAAA,IAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,2BAA6B,WACvDH,KAAKI,QAAU,wCAGfJ,KAAKK,UAAYH,CAAAA,SAAAA,aAAAA,WAAAA,IAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,qBAAAA,EAAAA,cAAAA,GAAYC,0BAC7BH,KAAKM,SAAW,yCAClB,CAGA,kBAAMC,CAAaC,GAAyC,IAAlCC,EAAUC,UAAAC,OAAA,QAAAtE,IAAAqE,UAAA,GAAAA,UAAA,GAAG,GAAIE,EAASF,UAAAC,OAAA,QAAAtE,IAAAqE,UAAA,GAAAA,UAAA,GAAG,GACrD,IACE,GAAIV,KAAKK,SACP,aAAaL,KAAKa,gBAAgBL,EAAOC,EAAYG,GAGvD,MAAME,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,UACNC,EAAGT,EACHU,KAAM,QACNT,WAAYA,EAAWd,WACvBwB,IAAKnB,KAAKC,OACVmB,MAAO,YACPC,WAAY,WACZC,WAAY,KACZC,kBAAmB,OAGjBX,GACFE,EAAOU,OAAO,YAAaZ,GAG7B,MAAMa,QAAiBC,MAAM,GAAG1B,KAAKI,kBAAkBU,KAEvD,IAAKW,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMpE,QAAagE,EAASK,OAG5B,OAAO9B,KAAK+B,uBAAuBtE,EACrC,CAAE,MAAO7D,GAGP,OAFAC,QAAQD,MAAM,wBAAyBA,GAEhCoG,KAAKgC,qBAAqBxB,EACnC,CACF,CAGA,qBAAMpH,CAAgBR,GACpB,IACE,GAAIoH,KAAKK,SACP,aAAaL,KAAKiC,yBAAyBrJ,GAG7C,MAAMkI,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,oCACNnI,GAAID,EACJuI,IAAKnB,KAAKC,SAGNwB,QAAiBC,MAAM,GAAG1B,KAAKI,kBAAkBU,KAEvD,IAAKW,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMpE,QAAagE,EAASK,OAE5B,GAAIrE,EAAKyE,OAASzE,EAAKyE,MAAMvB,OAAS,EACpC,OAAOX,KAAKmC,sBAAsB1E,EAAKyE,MAAM,IAG/C,MAAM,IAAIN,MAAM,kBAClB,CAAE,MAAOhI,GAEP,OADAC,QAAQD,MAAM,+BAAgCA,GACvCoG,KAAKoC,oBAAoBxJ,EAClC,CACF,CAGA,qBAAMiI,CAAgBL,EAAOC,EAAYG,GACvC,IACE,MAAME,EAAS,IAAIC,gBAAgB,CACjCE,EAAGT,EACHC,WAAYA,EAAWd,WACvBuB,KAAM,UAGJN,GACFE,EAAOU,OAAO,YAAaZ,GAG7B,MAAMa,QAAiBC,MAAM,GAAG1B,KAAKM,mBAAmBQ,KAExD,IAAKW,EAASE,GACZ,MAAM,IAAIC,MAAM,oBAAoBH,EAASI,UAG/C,MAAMpE,QAAagE,EAASK,OAC5B,OAAO9B,KAAK+B,uBAAuBtE,EACrC,CAAE,MAAO7D,GAEP,OADAC,QAAQD,MAAM,sBAAuBA,GAC9BoG,KAAKgC,qBAAqBxB,EACnC,CACF,CAGA,8BAAMyB,CAAyBrJ,GAC7B,IACE,MAAM6I,QAAiBC,MAAM,GAAG1B,KAAKM,kBAAkB1H,KAEvD,IAAK6I,EAASE,GACZ,MAAM,IAAIC,MAAM,oBAAoBH,EAASI,UAG/C,MAAMpE,QAAagE,EAASK,OAC5B,OAAO9B,KAAKmC,sBAAsB1E,EACpC,CAAE,MAAO7D,GAEP,OADAC,QAAQD,MAAM,6BAA8BA,GACrCoG,KAAKoC,oBAAoBxJ,EAClC,CACF,CAGAmJ,sBAAAA,CAAuBtE,GAAO,IAAD4E,EAC3B,IAAK5E,EAAKyE,MACR,MAAO,CAAEI,OAAQ,GAAIC,cAAe,KAAMC,aAAc,GAe1D,MAAO,CACLF,OAba7E,EAAKyE,MAAMO,IAAIC,IAAI,IAAAC,EAAAC,EAAAC,EAAAC,EAAA,MAAK,CACrCjK,GAAI6J,EAAK7J,GAAGD,SAAW8J,EAAK7J,GAC5BQ,MAAOqJ,EAAKK,QAAQ1J,MACpBC,QAASoJ,EAAKK,QAAQC,aACtBvJ,YAAaiJ,EAAKK,QAAQtJ,YAC1BD,WAAyC,QAA9BmJ,EAAAD,EAAKK,QAAQE,WAAWC,cAAM,IAAAP,OAAA,EAA9BA,EAAgCQ,OAAsC,QAAnCP,EAAIF,EAAKK,QAAQE,WAAWG,eAAO,IAAAR,OAAA,EAA/BA,EAAiCO,KACnFxJ,YAAa+I,EAAKK,QAAQpJ,YAC1BJ,SAAUyG,KAAKqD,cAAiC,QAApBR,EAACH,EAAKY,sBAAc,IAAAT,OAAA,EAAnBA,EAAqBtJ,UAClDG,MAAOsG,KAAKuD,YAA2B,QAAhBT,EAACJ,EAAKc,kBAAU,IAAAV,OAAA,EAAfA,EAAiBW,WACzCC,UAAWhB,EAAKK,QAAQW,aAKxBnB,cAAe9E,EAAK8E,eAAiB,KACrCC,cAA2B,QAAbH,EAAA5E,EAAKkG,gBAAQ,IAAAtB,OAAA,EAAbA,EAAeG,eAAgB,EAEjD,CAGAL,qBAAAA,CAAsBO,GAAO,IAADkB,EAAAC,EAC1B,MAAO,CACLhL,GAAI6J,EAAK7J,GACTQ,MAAOqJ,EAAKK,QAAQ1J,MACpBC,QAASoJ,EAAKK,QAAQC,aACtBvJ,YAAaiJ,EAAKK,QAAQtJ,YAC1BD,WAAyC,QAA9BoK,EAAAlB,EAAKK,QAAQE,WAAWa,cAAM,IAAAF,OAAA,EAA9BA,EAAgCT,OAAmC,QAAhCU,EAAInB,EAAKK,QAAQE,WAAWc,YAAI,IAAAF,OAAA,EAA5BA,EAA8BV,KAChFxJ,YAAa+I,EAAKK,QAAQpJ,YAC1BJ,SAAUyG,KAAKqD,cAAcX,EAAKY,eAAe/J,UACjDG,MAAOsG,KAAKuD,YAAYb,EAAKc,WAAWC,WACxCO,MAAOhE,KAAKuD,YAAYb,EAAKc,WAAWS,WACxCP,UAAWhB,EAAKK,QAAQW,UACxBQ,KAAMxB,EAAKK,QAAQmB,MAAQ,GAE/B,CAGAb,aAAAA,CAAc9J,GACZ,IAAKA,EAAU,OAAO,EAEtB,MAAM4K,EAAQ5K,EAAS4K,MAAM,2BAC7B,IAAKA,EAAO,OAAO,EAMnB,OAAe,MAJDC,SAASD,EAAM,KAAO,GAIJ,IAHhBC,SAASD,EAAM,KAAO,IACtBC,SAASD,EAAM,KAAO,EAGxC,CAGAZ,WAAAA,CAAYE,GACV,IAAKA,EAAW,MAAO,IAEvB,MAAMY,EAAQD,SAASX,GACvB,OAAIY,GAAS,IACJ,IAAIA,EAAQ,KAASC,QAAQ,MAC3BD,GAAS,IACX,IAAIA,EAAQ,KAAMC,QAAQ,MAE5BD,EAAM1E,UACf,CAGA4E,cAAAA,CAAeC,GACb,IAAKA,EAAS,MAAO,OAErB,MAAMC,EAAQhF,KAAKC,MAAM8E,EAAU,MAC7BE,EAAUjF,KAAKC,MAAO8E,EAAU,KAAQ,IACxCG,EAAOH,EAAU,GAEvB,OAAIC,EAAQ,EACH,GAAGA,KAASC,EAAQ/E,WAAWC,SAAS,EAAG,QAAQ+E,EAAKhF,WAAWC,SAAS,EAAG,OAEjF,GAAG8E,KAAWC,EAAKhF,WAAWC,SAAS,EAAG,MACnD,CAGAoC,oBAAAA,CAAqBxB,GACnB,MAAMoE,EAAa,CACjB,CACE/L,GAAI,cACJQ,MAAO,GAAGmH,yGACVlH,QAAS,sEACTG,YAAa,sMACbD,UAAW,uDACXD,SAAU,OACVG,MAAO,OACPC,YAAa,uBACb+J,UAAW,eAEb,CACE7K,GAAI,cACJQ,MAAO,sBAAOmH,qDACdlH,QAAS,sEACTG,YAAa,yLACbD,UAAW,uDACXD,SAAU,OACVG,MAAO,OACPC,YAAa,uBACb+J,UAAW,eAEb,CACE7K,GAAI,cACJQ,MAAO,4BAAQmH,4DACflH,QAAS,kFACTG,YAAa,oMACbD,UAAW,uDACXD,SAAU,OACVG,MAAO,OACPC,YAAa,uBACb+J,UAAW,gBAIf,MAAO,CACLpB,OAAQsC,EACRrC,cAAe,KACfC,aAAcoC,EAAWjE,OAE7B,CAGAyB,mBAAAA,CAAoBxJ,GAClB,MAAO,CACLC,GAAID,EACJS,MAAO,2GACPC,QAAS,sEACTG,YAAa,wGACbD,UAAW,8BAA8BZ,sBACzCW,SAAU,OACVG,MAAO,OACPsK,MAAO,MACPrK,YAAa,uBACb+J,UAAW,cACXQ,KAAM,CAAC,uCAAU,iCAAS,wCAE9B,CAGA,uBAAMW,GAAuD,IAArCvD,EAAUZ,UAAAC,OAAA,QAAAtE,IAAAqE,UAAA,GAAAA,UAAA,GAAG,KAAMD,EAAUC,UAAAC,OAAA,QAAAtE,IAAAqE,UAAA,GAAAA,UAAA,GAAG,GACtD,IACE,GAAIV,KAAKK,SACP,aAAaL,KAAK8E,qBAAqBxD,EAAYb,GAGrD,MAAMK,EAAS,IAAIC,gBAAgB,CACjCC,KAAM,oCACN+D,MAAO,cACPzD,aACAb,WAAYA,EAAWd,WACvBwB,IAAKnB,KAAKC,OACV+E,gBAAiB,MAGbvD,QAAiBC,MAAM,GAAG1B,KAAKI,kBAAkBU,KAEvD,IAAKW,EAASE,GACZ,MAAM,IAAIC,MAAM,sBAAsBH,EAASI,UAGjD,MAAMpE,QAAagE,EAASK,OAC5B,OAAO9B,KAAK+B,uBAAuBtE,EACrC,CAAE,MAAO7D,GAEP,OADAC,QAAQD,MAAM,0BAA2BA,GAClCoG,KAAKgC,qBAAqB,4EACnC,CACF,CAGA,0BAAM8C,CAAqBxD,EAAYb,GACrC,IACE,MAAMK,EAAS,IAAIC,gBAAgB,CACjCO,aACAb,WAAYA,EAAWd,aAGnB8B,QAAiBC,MAAM,GAAG1B,KAAKM,qBAAqBQ,KAE1D,IAAKW,EAASE,GACZ,MAAM,IAAIC,MAAM,oBAAoBH,EAASI,UAG/C,MAAMpE,QAAagE,EAASK,OAC5B,OAAO9B,KAAK+B,uBAAuBtE,EACrC,CAAE,MAAO7D,GAEP,OADAC,QAAQD,MAAM,wBAAyBA,GAChCoG,KAAKgC,qBAAqB,4EACnC,CACF,E", "sources": ["components/pages/Player.js", "services/youtubeAPI.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback } from 'react';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport youtubeAPI from '../../services/youtubeAPI';\nimport './Player.css';\n\nconst Player = ({ routeParams }) => {\n  const [isPlayerReady, setIsPlayerReady] = useState(false);\n  const [playerError, setPlayerError] = useState(null);\n  const playerRef = useRef(null);\n  const playerInstanceRef = useRef(null);\n  \n  const { \n    currentVideo, \n    setCurrentVideo, \n    updateVideoState,\n    incrementVideosWatched,\n    settings \n  } = useAppStore();\n\n  const videoId = routeParams?.videoId || currentVideo.id;\n\n  useEffect(() => {\n    if (videoId) {\n      loadYouTubeAPI();\n      loadVideoDetails();\n    }\n  }, [videoId]);\n\n  const loadVideoDetails = async () => {\n    if (!videoId) return;\n\n    try {\n      const details = await youtubeAPI.getVideoDetails(videoId);\n      setCurrentVideo({\n        id: videoId,\n        title: details.title,\n        channel: details.channel,\n        duration: details.duration,\n        thumbnail: details.thumbnail,\n        description: details.description,\n        views: details.views,\n        publishedAt: details.publishedAt\n      });\n    } catch (error) {\n      console.error('Failed to load video details:', error);\n    }\n  };\n\n  const loadYouTubeAPI = () => {\n    if (!window.YT) {\n      const script = document.createElement('script');\n      script.src = 'https://www.youtube.com/iframe_api';\n      script.async = true;\n      document.body.appendChild(script);\n\n      window.onYouTubeIframeAPIReady = () => {\n        initializePlayer();\n      };\n    } else {\n      initializePlayer();\n    }\n  };\n\n  const initializePlayer = () => {\n    if (playerRef.current && window.YT?.Player) {\n      try {\n        playerInstanceRef.current = new window.YT.Player(playerRef.current, {\n          height: '100%',\n          width: '100%',\n          videoId: videoId,\n          playerVars: {\n            autoplay: settings.autoplay ? 1 : 0,\n            controls: settings.hideControls ? 0 : 1,\n            disablekb: settings.hideControls ? 1 : 0,\n            fs: 1,\n            iv_load_policy: 3,\n            modestbranding: 1,\n            playsinline: 1,\n            rel: 0,\n            showinfo: 0,\n            cc_load_policy: 0,\n            hl: 'ar',\n            cc_lang_pref: 'ar',\n            origin: window.location.origin\n          },\n          events: {\n            onReady: onPlayerReady,\n            onStateChange: onPlayerStateChange,\n            onError: onPlayerError\n          }\n        });\n      } catch (error) {\n        console.error('Error initializing YouTube player:', error);\n        setPlayerError('فشل في تحميل مشغل الفيديو');\n      }\n    }\n  };\n\n  const onPlayerReady = (event) => {\n    setIsPlayerReady(true);\n    setPlayerError(null);\n    \n    // Set initial volume\n    if (settings.volume !== undefined) {\n      event.target.setVolume(settings.volume);\n    }\n    \n    // Get video info\n    const videoData = event.target.getVideoData();\n    setCurrentVideo({\n      id: videoId,\n      title: videoData.title || 'فيديو يوتيوب',\n      channel: videoData.author || 'قناة يوتيوب',\n      duration: event.target.getDuration() || 0,\n      currentTime: 0,\n      isPlaying: false,\n      isPaused: true,\n      isBuffering: false,\n      quality: event.target.getPlaybackQuality() || 'auto',\n      volume: event.target.getVolume() || 100,\n      muted: event.target.isMuted() || false,\n      fullscreen: false\n    });\n\n    console.log('YouTube player ready for video:', videoId);\n  };\n\n  const onPlayerStateChange = (event) => {\n    const state = event.data;\n    const player = event.target;\n    \n    switch (state) {\n      case window.YT.PlayerState.PLAYING:\n        updateVideoState({\n          isPlaying: true,\n          isPaused: false,\n          isBuffering: false\n        });\n        break;\n        \n      case window.YT.PlayerState.PAUSED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false\n        });\n        break;\n        \n      case window.YT.PlayerState.BUFFERING:\n        updateVideoState({\n          isBuffering: true\n        });\n        break;\n        \n      case window.YT.PlayerState.ENDED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false,\n          currentTime: player.getDuration()\n        });\n        incrementVideosWatched();\n        break;\n        \n      case window.YT.PlayerState.CUED:\n        updateVideoState({\n          isPlaying: false,\n          isPaused: true,\n          isBuffering: false,\n          currentTime: 0\n        });\n        break;\n    }\n\n    // Update current time\n    if (player.getCurrentTime) {\n      updateVideoState({\n        currentTime: player.getCurrentTime()\n      });\n    }\n  };\n\n  const onPlayerError = (event) => {\n    const errorCode = event.data;\n    let errorMessage = 'حدث خطأ في تشغيل الفيديو';\n    \n    switch (errorCode) {\n      case 2:\n        errorMessage = 'معرف الفيديو غير صحيح';\n        break;\n      case 5:\n        errorMessage = 'خطأ في مشغل HTML5';\n        break;\n      case 100:\n        errorMessage = 'الفيديو غير موجود أو محذوف';\n        break;\n      case 101:\n      case 150:\n        errorMessage = 'صاحب الفيديو لا يسمح بتشغيله في هذا التطبيق';\n        break;\n    }\n    \n    setPlayerError(errorMessage);\n    console.error('YouTube player error:', errorCode, errorMessage);\n  };\n\n  const handleRetry = () => {\n    setPlayerError(null);\n    setIsPlayerReady(false);\n    if (playerInstanceRef.current) {\n      playerInstanceRef.current.destroy();\n    }\n    setTimeout(() => {\n      initializePlayer();\n    }, 1000);\n  };\n\n  if (!videoId) {\n    return (\n      <div className=\"player-page\">\n        <div className=\"player-empty\">\n          <div className=\"empty-icon\">\n            <svg width=\"64\" height=\"64\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n              <path d=\"M8 5v14l11-7z\"/>\n            </svg>\n          </div>\n          <h2>لا يوجد فيديو للتشغيل</h2>\n          <p>يرجى البحث عن فيديو أو اختيار فيديو من القائمة</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <motion.div \n      className=\"player-page\"\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <div className=\"player-container\">\n        <div className=\"player-wrapper\">\n          {playerError ? (\n            <div className=\"player-error\">\n              <div className=\"error-icon\">\n                <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\"/>\n                </svg>\n              </div>\n              <h3>خطأ في تشغيل الفيديو</h3>\n              <p>{playerError}</p>\n              <button className=\"retry-button\" onClick={handleRetry}>\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z\"/>\n                </svg>\n                إعادة المحاولة\n              </button>\n            </div>\n          ) : (\n            <>\n              {!isPlayerReady && (\n                <div className=\"player-loading\">\n                  <div className=\"loading-spinner\">\n                    <div className=\"spinner\"></div>\n                  </div>\n                  <p>جاري تحميل الفيديو...</p>\n                </div>\n              )}\n              <div \n                ref={playerRef}\n                className={`youtube-player ${!isPlayerReady ? 'hidden' : ''}`}\n              />\n            </>\n          )}\n        </div>\n\n        {/* Video Info */}\n        {currentVideo.title && (\n          <motion.div \n            className=\"video-info\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.3 }}\n          >\n            <h1 className=\"video-title\">{currentVideo.title}</h1>\n            <div className=\"video-meta\">\n              <span className=\"video-channel\">{currentVideo.channel}</span>\n              {currentVideo.duration > 0 && (\n                <span className=\"video-duration\">\n                  المدة: {Math.floor(currentVideo.duration / 60)}:{(currentVideo.duration % 60).toString().padStart(2, '0')}\n                </span>\n              )}\n            </div>\n          </motion.div>\n        )}\n\n        {/* Ad Blocker Status */}\n        {settings.adBlock && (\n          <motion.div \n            className=\"ad-blocker-status\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.5 }}\n          >\n            <div className=\"status-icon\">\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <span>مانع الإعلانات مفعل - تجربة مشاهدة بدون إعلانات</span>\n          </motion.div>\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\nexport default Player;\n", "// YouTube Data API service\n// This service handles all interactions with YouTube's real API\n\nclass YouTubeAPIService {\n  constructor() {\n    // For production, you would get this from environment variables\n    // For now, we'll use a demo key or implement a proxy server\n    this.apiKey = process.env.REACT_APP_YOUTUBE_API_KEY || 'DEMO_KEY';\n    this.baseURL = 'https://www.googleapis.com/youtube/v3';\n    \n    // Fallback to a proxy server if no API key is available\n    this.useProxy = !process.env.REACT_APP_YOUTUBE_API_KEY;\n    this.proxyURL = 'https://youtube-proxy-api.herokuapp.com'; // Example proxy\n  }\n\n  // Search for videos using YouTube Data API\n  async searchVideos(query, maxResults = 25, pageToken = '') {\n    try {\n      if (this.useProxy) {\n        return await this.searchWithProxy(query, maxResults, pageToken);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet',\n        q: query,\n        type: 'video',\n        maxResults: maxResults.toString(),\n        key: this.apiKey,\n        order: 'relevance',\n        safeSearch: 'moderate',\n        regionCode: 'SA', // Saudi Arabia for Arabic content\n        relevanceLanguage: 'ar'\n      });\n\n      if (pageToken) {\n        params.append('pageToken', pageToken);\n      }\n\n      const response = await fetch(`${this.baseURL}/search?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      \n      // Transform the data to our format\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('YouTube search error:', error);\n      // Fallback to mock data if API fails\n      return this.getMockSearchResults(query);\n    }\n  }\n\n  // Get video details by ID\n  async getVideoDetails(videoId) {\n    try {\n      if (this.useProxy) {\n        return await this.getVideoDetailsWithProxy(videoId);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet,statistics,contentDetails',\n        id: videoId,\n        key: this.apiKey\n      });\n\n      const response = await fetch(`${this.baseURL}/videos?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      \n      if (data.items && data.items.length > 0) {\n        return this.transformVideoDetails(data.items[0]);\n      }\n      \n      throw new Error('Video not found');\n    } catch (error) {\n      console.error('YouTube video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Search with proxy server (fallback method)\n  async searchWithProxy(query, maxResults, pageToken) {\n    try {\n      const params = new URLSearchParams({\n        q: query,\n        maxResults: maxResults.toString(),\n        type: 'video'\n      });\n\n      if (pageToken) {\n        params.append('pageToken', pageToken);\n      }\n\n      const response = await fetch(`${this.proxyURL}/search?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`Proxy API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('Proxy search error:', error);\n      return this.getMockSearchResults(query);\n    }\n  }\n\n  // Get video details with proxy\n  async getVideoDetailsWithProxy(videoId) {\n    try {\n      const response = await fetch(`${this.proxyURL}/video/${videoId}`);\n      \n      if (!response.ok) {\n        throw new Error(`Proxy API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformVideoDetails(data);\n    } catch (error) {\n      console.error('Proxy video details error:', error);\n      return this.getMockVideoDetails(videoId);\n    }\n  }\n\n  // Transform YouTube API search results to our format\n  transformSearchResults(data) {\n    if (!data.items) {\n      return { videos: [], nextPageToken: null, totalResults: 0 };\n    }\n\n    const videos = data.items.map(item => ({\n      id: item.id.videoId || item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails?.duration),\n      views: this.formatViews(item.statistics?.viewCount),\n      channelId: item.snippet.channelId\n    }));\n\n    return {\n      videos,\n      nextPageToken: data.nextPageToken || null,\n      totalResults: data.pageInfo?.totalResults || 0\n    };\n  }\n\n  // Transform video details to our format\n  transformVideoDetails(item) {\n    return {\n      id: item.id,\n      title: item.snippet.title,\n      channel: item.snippet.channelTitle,\n      description: item.snippet.description,\n      thumbnail: item.snippet.thumbnails.maxres?.url || item.snippet.thumbnails.high?.url,\n      publishedAt: item.snippet.publishedAt,\n      duration: this.parseDuration(item.contentDetails.duration),\n      views: this.formatViews(item.statistics.viewCount),\n      likes: this.formatViews(item.statistics.likeCount),\n      channelId: item.snippet.channelId,\n      tags: item.snippet.tags || []\n    };\n  }\n\n  // Parse ISO 8601 duration to seconds\n  parseDuration(duration) {\n    if (!duration) return 0;\n    \n    const match = duration.match(/PT(\\d+H)?(\\d+M)?(\\d+S)?/);\n    if (!match) return 0;\n    \n    const hours = parseInt(match[1]) || 0;\n    const minutes = parseInt(match[2]) || 0;\n    const seconds = parseInt(match[3]) || 0;\n    \n    return hours * 3600 + minutes * 60 + seconds;\n  }\n\n  // Format view count\n  formatViews(viewCount) {\n    if (!viewCount) return '0';\n    \n    const count = parseInt(viewCount);\n    if (count >= 1000000) {\n      return `${(count / 1000000).toFixed(1)}M`;\n    } else if (count >= 1000) {\n      return `${(count / 1000).toFixed(1)}K`;\n    }\n    return count.toString();\n  }\n\n  // Format duration from seconds to MM:SS or HH:MM:SS\n  formatDuration(seconds) {\n    if (!seconds) return '0:00';\n    \n    const hours = Math.floor(seconds / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    const secs = seconds % 60;\n    \n    if (hours > 0) {\n      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n    }\n    return `${minutes}:${secs.toString().padStart(2, '0')}`;\n  }\n\n  // Mock search results (fallback)\n  getMockSearchResults(query) {\n    const mockVideos = [\n      {\n        id: 'dQw4w9WgXcQ',\n        title: `${query} - نتيجة البحث الأولى`,\n        channel: 'قناة تجريبية',\n        description: 'وصف مفصل للفيديو الأول في نتائج البحث...',\n        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',\n        duration: '3:32',\n        views: '1.2M',\n        publishedAt: '2023-01-15T10:00:00Z',\n        channelId: 'UC123456789'\n      },\n      {\n        id: 'kJQP7kiw5Fk',\n        title: `شرح ${query} بالتفصيل`,\n        channel: 'قناة التعليم',\n        description: 'شرح شامل ومفصل حول الموضوع المطلوب...',\n        thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg',\n        duration: '4:42',\n        views: '850K',\n        publishedAt: '2023-02-20T15:30:00Z',\n        channelId: 'UC987654321'\n      },\n      {\n        id: 'fJ9rUzIMcZQ',\n        title: `أفضل ${query} لهذا العام`,\n        channel: 'قناة المراجعات',\n        description: 'مراجعة شاملة لأفضل الخيارات المتاحة...',\n        thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',\n        duration: '5:55',\n        views: '2.1M',\n        publishedAt: '2023-03-10T12:15:00Z',\n        channelId: 'UC456789123'\n      }\n    ];\n\n    return {\n      videos: mockVideos,\n      nextPageToken: null,\n      totalResults: mockVideos.length\n    };\n  }\n\n  // Mock video details (fallback)\n  getMockVideoDetails(videoId) {\n    return {\n      id: videoId,\n      title: 'فيديو يوتيوب تجريبي',\n      channel: 'قناة تجريبية',\n      description: 'وصف تجريبي للفيديو...',\n      thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,\n      duration: '3:45',\n      views: '1.5M',\n      likes: '25K',\n      publishedAt: '2023-01-01T00:00:00Z',\n      channelId: 'UC123456789',\n      tags: ['تجريبي', 'فيديو', 'يوتيوب']\n    };\n  }\n\n  // Get trending videos\n  async getTrendingVideos(regionCode = 'SA', maxResults = 25) {\n    try {\n      if (this.useProxy) {\n        return await this.getTrendingWithProxy(regionCode, maxResults);\n      }\n\n      const params = new URLSearchParams({\n        part: 'snippet,statistics,contentDetails',\n        chart: 'mostPopular',\n        regionCode,\n        maxResults: maxResults.toString(),\n        key: this.apiKey,\n        videoCategoryId: '0' // All categories\n      });\n\n      const response = await fetch(`${this.baseURL}/videos?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`YouTube API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('YouTube trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n\n  // Get trending with proxy\n  async getTrendingWithProxy(regionCode, maxResults) {\n    try {\n      const params = new URLSearchParams({\n        regionCode,\n        maxResults: maxResults.toString()\n      });\n\n      const response = await fetch(`${this.proxyURL}/trending?${params}`);\n      \n      if (!response.ok) {\n        throw new Error(`Proxy API error: ${response.status}`);\n      }\n\n      const data = await response.json();\n      return this.transformSearchResults(data);\n    } catch (error) {\n      console.error('Proxy trending error:', error);\n      return this.getMockSearchResults('الأكثر مشاهدة');\n    }\n  }\n}\n\n// Create and export a singleton instance\nconst youtubeAPI = new YouTubeAPIService();\nexport default youtubeAPI;\n"], "names": ["_ref", "routeParams", "isPlayerReady", "setIsPlayerReady", "useState", "playerError", "setPlayerError", "playerRef", "useRef", "playerInstanceRef", "currentVideo", "setCurrentVideo", "updateVideoState", "incrementVideosWatched", "settings", "useAppStore", "videoId", "id", "useEffect", "loadYouTubeAPI", "loadVideoDetails", "async", "details", "youtubeAPI", "getVideoDetails", "title", "channel", "duration", "thumbnail", "description", "views", "publishedAt", "error", "console", "window", "YT", "initializePlayer", "script", "document", "createElement", "src", "body", "append<PERSON><PERSON><PERSON>", "onYouTubeIframeAPIReady", "_window$YT", "current", "Player", "height", "width", "playerVars", "autoplay", "controls", "hideControls", "disablekb", "fs", "iv_load_policy", "modestbranding", "playsinline", "rel", "showinfo", "cc_load_policy", "hl", "cc_lang_pref", "origin", "location", "events", "onReady", "onPlayerReady", "onStateChange", "onPlayerStateChange", "onError", "onPlayerError", "event", "undefined", "volume", "target", "setVolume", "videoData", "getVideoData", "author", "getDuration", "currentTime", "isPlaying", "isPaused", "isBuffering", "quality", "getPlaybackQuality", "getVolume", "muted", "isMuted", "fullscreen", "log", "state", "data", "player", "PlayerState", "PLAYING", "PAUSED", "BUFFERING", "ENDED", "CUED", "getCurrentTime", "errorCode", "errorMessage", "_jsx", "motion", "div", "className", "initial", "opacity", "animate", "transition", "children", "_jsxs", "viewBox", "fill", "d", "onClick", "handleRetry", "destroy", "setTimeout", "_Fragment", "ref", "y", "delay", "Math", "floor", "toString", "padStart", "adBlock", "scale", "constructor", "this", "<PERSON><PERSON><PERSON><PERSON>", "process", "REACT_APP_YOUTUBE_API_KEY", "baseURL", "useProxy", "proxyURL", "searchVideos", "query", "maxResults", "arguments", "length", "pageToken", "searchWithProxy", "params", "URLSearchParams", "part", "q", "type", "key", "order", "safeSearch", "regionCode", "relevanceLanguage", "append", "response", "fetch", "ok", "Error", "status", "json", "transformSearchResults", "getMockSearchResults", "getVideoDetailsWithProxy", "items", "transformVideoDetails", "getMockVideoDetails", "_data$pageInfo", "videos", "nextPageToken", "totalResults", "map", "item", "_item$snippet$thumbna", "_item$snippet$thumbna2", "_item$contentDetails", "_item$statistics", "snippet", "channelTitle", "thumbnails", "medium", "url", "default", "parseDuration", "contentDetails", "formatViews", "statistics", "viewCount", "channelId", "pageInfo", "_item$snippet$thumbna3", "_item$snippet$thumbna4", "maxres", "high", "likes", "likeCount", "tags", "match", "parseInt", "count", "toFixed", "formatDuration", "seconds", "hours", "minutes", "secs", "mockVideos", "getTrendingVideos", "getTrendingWithProxy", "chart", "videoCategoryId"], "sourceRoot": ""}