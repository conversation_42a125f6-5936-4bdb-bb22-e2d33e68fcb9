/* Global styles and CSS variables */

/* CSS Custom Properties (Variables) */
:root {
  /* Colors - Dark Theme */
  --primary-color: #ff0000;
  --primary-hover: #e60000;
  --primary-active: #cc0000;
  --primary-light: #ff3333;
  --primary-dark: #990000;
  
  --secondary-color: #1a1a1a;
  --secondary-hover: #2a2a2a;
  --secondary-active: #333333;
  
  --background-primary: #0f0f0f;
  --background-secondary: #1a1a1a;
  --background-tertiary: #2a2a2a;
  --background-overlay: rgba(0, 0, 0, 0.8);
  
  --surface-primary: #1a1a1a;
  --surface-secondary: #2a2a2a;
  --surface-tertiary: #3a3a3a;
  --surface-hover: #333333;
  
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #aaaaaa;
  --text-disabled: #666666;
  --text-inverse: #000000;
  
  --border-primary: #333333;
  --border-secondary: #444444;
  --border-focus: #ff0000;
  --border-hover: #555555;
  
  --success-color: #00ff00;
  --success-bg: rgba(0, 255, 0, 0.1);
  --warning-color: #ffaa00;
  --warning-bg: rgba(255, 170, 0, 0.1);
  --error-color: #ff4444;
  --error-bg: rgba(255, 68, 68, 0.1);
  --info-color: #00aaff;
  --info-bg: rgba(0, 170, 255, 0.1);
  
  /* Spacing */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
  
  /* Typography */
  --font-family-primary: 'Cairo', 'Tajawal', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-secondary: 'Tajawal', 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-family-mono: 'Fira Code', 'Monaco', 'Consolas', monospace;
  
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;
  --font-size-3xl: 32px;
  --font-size-4xl: 40px;
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.7;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 8px 15px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.5);
  --shadow-inner: inset 0 2px 4px rgba(0, 0, 0, 0.3);
  
  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms ease;
  --transition-normal: 300ms ease;
  --transition-slow: 500ms ease;
  --transition-bounce: 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* Layout */
  --header-height: 60px;
  --sidebar-width: 300px;
  --sidebar-collapsed-width: 60px;
  --footer-height: 40px;
  
  /* Breakpoints (for reference in JS) */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Light theme variables */
[data-theme="light"] {
  --background-primary: #ffffff;
  --background-secondary: #f8f9fa;
  --background-tertiary: #e9ecef;
  --background-overlay: rgba(255, 255, 255, 0.9);
  
  --surface-primary: #ffffff;
  --surface-secondary: #f8f9fa;
  --surface-tertiary: #e9ecef;
  --surface-hover: #dee2e6;
  
  --text-primary: #212529;
  --text-secondary: #495057;
  --text-tertiary: #6c757d;
  --text-disabled: #adb5bd;
  --text-inverse: #ffffff;
  
  --border-primary: #dee2e6;
  --border-secondary: #ced4da;
  --border-hover: #adb5bd;
  
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 15px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.2);
}

/* Reset and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--background-primary);
  direction: rtl;
  text-align: right;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--border-focus);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

/* Selection styles */
::selection {
  background-color: var(--primary-color);
  color: var(--text-inverse);
}

/* Scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-primary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-hover);
}

::-webkit-scrollbar-corner {
  background: var(--surface-secondary);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-3xl); }
h2 { font-size: var(--font-size-2xl); }
h3 { font-size: var(--font-size-xl); }
h4 { font-size: var(--font-size-lg); }
h5 { font-size: var(--font-size-md); }
h6 { font-size: var(--font-size-sm); }

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-fast);
}

a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* Lists */
ul, ol {
  margin-bottom: var(--spacing-md);
  padding-right: var(--spacing-lg);
}

li {
  margin-bottom: var(--spacing-xs);
}

/* Code */
code {
  font-family: var(--font-family-mono);
  font-size: 0.875em;
  background-color: var(--surface-secondary);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  color: var(--text-primary);
}

pre {
  font-family: var(--font-family-mono);
  background-color: var(--surface-secondary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  overflow-x: auto;
  margin-bottom: var(--spacing-md);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Buttons */
button {
  font-family: inherit;
  font-size: inherit;
  border: none;
  background: none;
  cursor: pointer;
  transition: var(--transition-fast);
}

button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Form elements */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  background-color: var(--surface-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm) var(--spacing-md);
  transition: var(--transition-fast);
}

input:focus, textarea:focus, select:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(255, 0, 0, 0.1);
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-disabled { color: var(--text-disabled); }

.bg-primary { background-color: var(--background-primary); }
.bg-secondary { background-color: var(--background-secondary); }
.bg-tertiary { background-color: var(--background-tertiary); }

.border { border: 1px solid var(--border-primary); }
.border-secondary { border: 1px solid var(--border-secondary); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.transition { transition: var(--transition-normal); }
.transition-fast { transition: var(--transition-fast); }
.transition-slow { transition: var(--transition-slow); }
