{"version": 3, "file": "static/js/695.72fe4355.chunk.js", "mappings": "oMAMA,MAgQA,EAhQaA,KACX,MAAOC,IAAkBC,EAAAA,EAAAA,UAAS,CAChC,CACEC,GAAI,cACJC,MAAO,0KACPC,QAAS,qBACTC,SAAU,OACVC,MAAO,OACPC,UAAW,wDAEb,CACEL,GAAI,cACJC,MAAO,4JACPC,QAAS,4EACTC,SAAU,OACVC,MAAO,OACPC,UAAW,wDAEb,CACEL,GAAI,cACJC,MAAO,oJACPC,QAAS,4EACTC,SAAU,OACVC,MAAO,OACPC,UAAW,2DAIT,MAAEC,EAAK,aAAEC,IAAiBC,EAAAA,EAAAA,MAC1B,SAAEC,IAAaC,EAAAA,EAAAA,MAoBfC,EAAe,CACnBC,OAAQ,CAAEC,QAAS,EAAGC,EAAG,IACzBC,QAAS,CAAEF,QAAS,EAAGC,EAAG,IAG5B,OACEE,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVC,SAlBsB,CACxBR,OAAQ,CAAEC,QAAS,GACnBE,QAAS,CACPF,QAAS,EACTQ,WAAY,CACVC,gBAAiB,MAcnBC,QAAQ,SACRC,QAAQ,UAASC,SAAA,EAGjBT,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,eAAeC,SAAUT,EAAac,SAAA,EAC9DT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAACV,EAAAA,EAAOW,GAAE,CACRT,UAAU,aACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAC5B,6KAGDE,EAAAA,EAAAA,KAACV,EAAAA,EAAOa,EAAC,CACPX,UAAU,mBACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAC5B,qbAGDT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,eACVI,QAAS,CAAEV,QAAS,EAAGC,EAAG,IAC1BU,QAAS,CAAEX,QAAS,EAAGC,EAAG,GAC1BO,WAAY,CAAEQ,MAAO,IAAMJ,SAAA,EAE3BT,EAAAA,EAAAA,MAACC,EAAAA,EAAOc,OAAM,CACZZ,UAAU,sBACVa,QArDcC,KACxBxB,EAAS,WAqDCyB,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAOV,SAAA,EAE1BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,iPACJ,8DAGRd,EAAAA,EAAAA,KAACV,EAAAA,EAAOc,OAAM,CACZZ,UAAU,wBACVa,QAASA,IAAMvB,EAAS,SACxByB,WAAY,CAAEC,MAAO,MACrBC,SAAU,CAAED,MAAO,KAAOV,SAC3B,4FAKLE,EAAAA,EAAAA,KAACV,EAAAA,EAAOC,IAAG,CACTC,UAAU,aACVI,QAAS,CAAEV,QAAS,EAAGsB,MAAO,IAC9BX,QAAS,CAAEX,QAAS,EAAGsB,MAAO,GAC9Bd,WAAY,CAAEQ,MAAO,IAAMJ,UAE3BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,qBAAoBM,SAAA,EACjCE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,uBAAsBM,UACnCE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uBAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,yBAAwBM,SAAA,EACrCE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,oBACfQ,EAAAA,EAAAA,KAAA,OAAKR,UAAU,oBACfQ,EAAAA,EAAAA,KAAA,OAAKR,UAAU,+BAOvBH,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,gBAAgBC,SAAUT,EAAac,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,4DAC9BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,aAAYM,SAAA,EACzBT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBM,UAC/BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uBAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAEnB,EAAMoC,iBACrCf,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,yEAIjCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeM,UAC5BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,sGAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAEnB,EAAMqC,cACrChB,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,yEAIjCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CACTC,UAAU,YACVe,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAE5BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,iBAAgBM,UAC7BT,EAAAA,EAAAA,MAAA,OAAKqB,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,SAAA,EACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,qJACRd,EAAAA,EAAAA,KAAA,QAAMc,EAAE,mDAGZzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,eAAcM,SAAA,EAC3BE,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaM,SAAEmB,KAAKC,MAAMvC,EAAMwC,UAAY,OAC5DnB,EAAAA,EAAAA,KAAA,QAAMR,UAAU,aAAYM,SAAC,qFAOrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,mBAAmBC,SAAUT,EAAac,SAAA,EAClEE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,qFAC9BE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,cAAaM,SACzB3B,EAAeiD,IAAI,CAACC,EAAOC,KAC1BjC,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAETC,UAAU,aACVC,SAAUT,EACVuB,WAAY,CAAEC,MAAO,KAAMrB,GAAI,GAC/BkB,QAASA,KAAMkB,OA/JDC,EA+JkBH,EAAMhD,QA9JhDS,EAAS,SAAU,CAAE0C,YADGA,OA+J4B1B,SAAA,EAE1CT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,kBAAiBM,SAAA,EAC9BE,EAAAA,EAAAA,KAAA,OAAKyB,IAAKJ,EAAM3C,UAAWgD,IAAKL,EAAM/C,MAAOqD,QAAQ,UACrD3B,EAAAA,EAAAA,KAAA,OAAKR,UAAU,iBAAgBM,SAAEuB,EAAM7C,YACvCwB,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeM,UAC5BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,0BAIdzB,EAAAA,EAAAA,MAAA,OAAKG,UAAU,aAAYM,SAAA,EACzBE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,cAAaM,SAAEuB,EAAM/C,SACnC0B,EAAAA,EAAAA,KAAA,KAAGR,UAAU,gBAAeM,SAAEuB,EAAM9C,WACpCc,EAAAA,EAAAA,MAAA,KAAGG,UAAU,cAAaM,SAAA,CAAEuB,EAAM5C,MAAM,gDAlBrC4C,EAAMhD,WA0BnBgB,EAAAA,EAAAA,MAACC,EAAAA,EAAOS,QAAO,CAACP,UAAU,mBAAmBC,SAAUT,EAAac,SAAA,EAClEE,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,uGAC9BT,EAAAA,EAAAA,MAAA,OAAKG,UAAU,gBAAeM,SAAA,EAC5BT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,sGAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,4FAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,wRAKrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,+KAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,wGAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,mPAKrCT,EAAAA,EAAAA,MAACC,EAAAA,EAAOC,IAAG,CAACC,UAAU,eAAee,WAAY,CAAEC,MAAO,MAAOV,SAAA,EAC/DE,EAAAA,EAAAA,KAAA,OAAKR,UAAU,eAAcM,UAC3BE,EAAAA,EAAAA,KAAA,OAAKU,MAAM,KAAKC,OAAO,KAAKC,QAAQ,YAAYC,KAAK,eAAcf,UACjEE,EAAAA,EAAAA,KAAA,QAAMc,EAAE,uDAGZd,EAAAA,EAAAA,KAAA,MAAIR,UAAU,gBAAeM,SAAC,uDAC9BE,EAAAA,EAAAA,KAAA,KAAGR,UAAU,sBAAqBM,SAAC,wQ", "sources": ["components/pages/Home.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport useAppStore from '../../stores/appStore';\nimport useRouter from '../../services/router';\nimport './Home.css';\n\nconst Home = () => {\n  const [featuredVideos] = useState([\n    {\n      id: 'dQw4w9WgXcQ',\n      title: 'مرحباً بك في مشغل يوتيوب المتقدم',\n      channel: 'YouTube Player Pro',\n      duration: '3:32',\n      views: '1.2M',\n      thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg'\n    },\n    {\n      id: 'kJQP7kiw5Fk',\n      title: 'كيفية استخدام مانع الإعلانات',\n      channel: 'دليل المستخدم',\n      duration: '4:42',\n      views: '850K',\n      thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg'\n    },\n    {\n      id: 'fJ9rUzIMcZQ',\n      title: 'الميزات الجديدة في الإصدار 2.0',\n      channel: 'أخبار التطبيق',\n      duration: '5:55',\n      views: '2.1M',\n      thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg'\n    }\n  ]);\n\n  const { stats, currentVideo } = useAppStore();\n  const { navigate } = useRouter();\n\n  const handleVideoClick = (videoId) => {\n    navigate('player', { videoId });\n  };\n\n  const handleSearchClick = () => {\n    navigate('search');\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 }\n  };\n\n  return (\n    <motion.div \n      className=\"home-page\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Hero Section */}\n      <motion.section className=\"hero-section\" variants={itemVariants}>\n        <div className=\"hero-content\">\n          <motion.h1 \n            className=\"hero-title\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.2 }}\n          >\n            مرحباً بك في مشغل يوتيوب المتقدم\n          </motion.h1>\n          <motion.p \n            className=\"hero-description\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4 }}\n          >\n            استمتع بمشاهدة فيديوهات يوتيوب بدون إعلانات مع واجهة عربية متقدمة وميزات احترافية\n          </motion.p>\n          <motion.div \n            className=\"hero-actions\"\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6 }}\n          >\n            <motion.button\n              className=\"hero-button primary\"\n              onClick={handleSearchClick}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z\"/>\n              </svg>\n              ابدأ البحث\n            </motion.button>\n            <motion.button\n              className=\"hero-button secondary\"\n              onClick={() => navigate('about')}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              تعرف على المزيد\n            </motion.button>\n          </motion.div>\n        </div>\n        <motion.div \n          className=\"hero-image\"\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ delay: 0.8 }}\n        >\n          <div className=\"hero-video-preview\">\n            <div className=\"video-preview-screen\">\n              <svg width=\"80\" height=\"80\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M8 5v14l11-7z\"/>\n              </svg>\n            </div>\n            <div className=\"video-preview-controls\">\n              <div className=\"control-button\"></div>\n              <div className=\"control-button\"></div>\n              <div className=\"control-button\"></div>\n            </div>\n          </div>\n        </motion.div>\n      </motion.section>\n\n      {/* Stats Section */}\n      <motion.section className=\"stats-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">إحصائياتك</h2>\n        <div className=\"stats-grid\">\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon videos\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M8 5v14l11-7z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{stats.videosWatched}</span>\n              <span className=\"stat-label\">فيديو مشاهد</span>\n            </div>\n          </motion.div>\n\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon ads\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{stats.adsBlocked}</span>\n              <span className=\"stat-label\">إعلان محجوب</span>\n            </div>\n          </motion.div>\n\n          <motion.div \n            className=\"stat-card\"\n            whileHover={{ scale: 1.02 }}\n          >\n            <div className=\"stat-icon time\">\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z\"/>\n                <path d=\"M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z\"/>\n              </svg>\n            </div>\n            <div className=\"stat-content\">\n              <span className=\"stat-number\">{Math.floor(stats.timeSpent / 60)}</span>\n              <span className=\"stat-label\">دقيقة مشاهدة</span>\n            </div>\n          </motion.div>\n        </div>\n      </motion.section>\n\n      {/* Featured Videos */}\n      <motion.section className=\"featured-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">فيديوهات مميزة</h2>\n        <div className=\"videos-grid\">\n          {featuredVideos.map((video, index) => (\n            <motion.div\n              key={video.id}\n              className=\"video-card\"\n              variants={itemVariants}\n              whileHover={{ scale: 1.02, y: -4 }}\n              onClick={() => handleVideoClick(video.id)}\n            >\n              <div className=\"video-thumbnail\">\n                <img src={video.thumbnail} alt={video.title} loading=\"lazy\" />\n                <div className=\"video-duration\">{video.duration}</div>\n                <div className=\"video-overlay\">\n                  <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                    <path d=\"M8 5v14l11-7z\"/>\n                  </svg>\n                </div>\n              </div>\n              <div className=\"video-info\">\n                <h3 className=\"video-title\">{video.title}</h3>\n                <p className=\"video-channel\">{video.channel}</p>\n                <p className=\"video-views\">{video.views} مشاهدة</p>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </motion.section>\n\n      {/* Features Section */}\n      <motion.section className=\"features-section\" variants={itemVariants}>\n        <h2 className=\"section-title\">المميزات الرئيسية</h2>\n        <div className=\"features-grid\">\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">مانع إعلانات قوي</h3>\n            <p className=\"feature-description\">\n              يحجب جميع أنواع الإعلانات لتجربة مشاهدة سلسة ومريحة\n            </p>\n          </motion.div>\n\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">واجهة عربية متقدمة</h3>\n            <p className=\"feature-description\">\n              تصميم عربي كامل مع دعم RTL وثيم داكن مريح للعينين\n            </p>\n          </motion.div>\n\n          <motion.div className=\"feature-card\" whileHover={{ scale: 1.02 }}>\n            <div className=\"feature-icon\">\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n              </svg>\n            </div>\n            <h3 className=\"feature-title\">أداء محسن</h3>\n            <p className=\"feature-description\">\n              تشغيل سريع وسلس مع استهلاك أقل للموارد والذاكرة\n            </p>\n          </motion.div>\n        </div>\n      </motion.section>\n    </motion.div>\n  );\n};\n\nexport default Home;\n"], "names": ["Home", "featured<PERSON><PERSON><PERSON>", "useState", "id", "title", "channel", "duration", "views", "thumbnail", "stats", "currentVideo", "useAppStore", "navigate", "useRouter", "itemVariants", "hidden", "opacity", "y", "visible", "_jsxs", "motion", "div", "className", "variants", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "initial", "animate", "children", "section", "_jsx", "h1", "delay", "p", "button", "onClick", "handleSearchClick", "whileHover", "scale", "whileTap", "width", "height", "viewBox", "fill", "d", "videosWatched", "adsBlocked", "Math", "floor", "timeSpent", "map", "video", "index", "handleVideoClick", "videoId", "src", "alt", "loading"], "sourceRoot": ""}