// YouTube Data API service
// This service handles all interactions with YouTube's real API

class YouTubeAPIService {
  constructor() {
    // For production, you would get this from environment variables
    // For now, we'll use a demo key or implement a proxy server
    this.apiKey = process.env.REACT_APP_YOUTUBE_API_KEY || 'DEMO_KEY';
    this.baseURL = 'https://www.googleapis.com/youtube/v3';
    
    // Fallback to a proxy server if no API key is available
    this.useProxy = !process.env.REACT_APP_YOUTUBE_API_KEY;
    this.proxyURL = 'https://youtube-proxy-api.herokuapp.com'; // Example proxy
  }

  // Search for videos using YouTube Data API
  async searchVideos(query, maxResults = 25, pageToken = '') {
    try {
      if (this.useProxy) {
        return await this.searchWithProxy(query, maxResults, pageToken);
      }

      const params = new URLSearchParams({
        part: 'snippet',
        q: query,
        type: 'video',
        maxResults: maxResults.toString(),
        key: this.apiKey,
        order: 'relevance',
        safeSearch: 'moderate',
        regionCode: 'SA', // Saudi Arabia for Arabic content
        relevanceLanguage: 'ar'
      });

      if (pageToken) {
        params.append('pageToken', pageToken);
      }

      const response = await fetch(`${this.baseURL}/search?${params}`);
      
      if (!response.ok) {
        throw new Error(`YouTube API error: ${response.status}`);
      }

      const data = await response.json();
      
      // Transform the data to our format
      return this.transformSearchResults(data);
    } catch (error) {
      console.error('YouTube search error:', error);
      // Fallback to mock data if API fails
      return this.getMockSearchResults(query);
    }
  }

  // Get video details by ID
  async getVideoDetails(videoId) {
    try {
      if (this.useProxy) {
        return await this.getVideoDetailsWithProxy(videoId);
      }

      const params = new URLSearchParams({
        part: 'snippet,statistics,contentDetails',
        id: videoId,
        key: this.apiKey
      });

      const response = await fetch(`${this.baseURL}/videos?${params}`);
      
      if (!response.ok) {
        throw new Error(`YouTube API error: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.items && data.items.length > 0) {
        return this.transformVideoDetails(data.items[0]);
      }
      
      throw new Error('Video not found');
    } catch (error) {
      console.error('YouTube video details error:', error);
      return this.getMockVideoDetails(videoId);
    }
  }

  // Search with proxy server (fallback method)
  async searchWithProxy(query, maxResults, pageToken) {
    try {
      const params = new URLSearchParams({
        q: query,
        maxResults: maxResults.toString(),
        type: 'video'
      });

      if (pageToken) {
        params.append('pageToken', pageToken);
      }

      const response = await fetch(`${this.proxyURL}/search?${params}`);
      
      if (!response.ok) {
        throw new Error(`Proxy API error: ${response.status}`);
      }

      const data = await response.json();
      return this.transformSearchResults(data);
    } catch (error) {
      console.error('Proxy search error:', error);
      return this.getMockSearchResults(query);
    }
  }

  // Get video details with proxy
  async getVideoDetailsWithProxy(videoId) {
    try {
      const response = await fetch(`${this.proxyURL}/video/${videoId}`);
      
      if (!response.ok) {
        throw new Error(`Proxy API error: ${response.status}`);
      }

      const data = await response.json();
      return this.transformVideoDetails(data);
    } catch (error) {
      console.error('Proxy video details error:', error);
      return this.getMockVideoDetails(videoId);
    }
  }

  // Transform YouTube API search results to our format
  transformSearchResults(data) {
    if (!data.items) {
      return { videos: [], nextPageToken: null, totalResults: 0 };
    }

    const videos = data.items.map(item => ({
      id: item.id.videoId || item.id,
      title: item.snippet.title,
      channel: item.snippet.channelTitle,
      description: item.snippet.description,
      thumbnail: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,
      publishedAt: item.snippet.publishedAt,
      duration: this.parseDuration(item.contentDetails?.duration),
      views: this.formatViews(item.statistics?.viewCount),
      channelId: item.snippet.channelId
    }));

    return {
      videos,
      nextPageToken: data.nextPageToken || null,
      totalResults: data.pageInfo?.totalResults || 0
    };
  }

  // Transform video details to our format
  transformVideoDetails(item) {
    return {
      id: item.id,
      title: item.snippet.title,
      channel: item.snippet.channelTitle,
      description: item.snippet.description,
      thumbnail: item.snippet.thumbnails.maxres?.url || item.snippet.thumbnails.high?.url,
      publishedAt: item.snippet.publishedAt,
      duration: this.parseDuration(item.contentDetails.duration),
      views: this.formatViews(item.statistics.viewCount),
      likes: this.formatViews(item.statistics.likeCount),
      channelId: item.snippet.channelId,
      tags: item.snippet.tags || []
    };
  }

  // Parse ISO 8601 duration to seconds
  parseDuration(duration) {
    if (!duration) return 0;
    
    const match = duration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);
    if (!match) return 0;
    
    const hours = parseInt(match[1]) || 0;
    const minutes = parseInt(match[2]) || 0;
    const seconds = parseInt(match[3]) || 0;
    
    return hours * 3600 + minutes * 60 + seconds;
  }

  // Format view count
  formatViews(viewCount) {
    if (!viewCount) return '0';
    
    const count = parseInt(viewCount);
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  }

  // Format duration from seconds to MM:SS or HH:MM:SS
  formatDuration(seconds) {
    if (!seconds) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }

  // Mock search results (fallback)
  getMockSearchResults(query) {
    const mockVideos = [
      {
        id: 'dQw4w9WgXcQ',
        title: `${query} - نتيجة البحث الأولى`,
        channel: 'قناة تجريبية',
        description: 'وصف مفصل للفيديو الأول في نتائج البحث...',
        thumbnail: 'https://img.youtube.com/vi/dQw4w9WgXcQ/mqdefault.jpg',
        duration: '3:32',
        views: '1.2M',
        publishedAt: '2023-01-15T10:00:00Z',
        channelId: 'UC123456789'
      },
      {
        id: 'kJQP7kiw5Fk',
        title: `شرح ${query} بالتفصيل`,
        channel: 'قناة التعليم',
        description: 'شرح شامل ومفصل حول الموضوع المطلوب...',
        thumbnail: 'https://img.youtube.com/vi/kJQP7kiw5Fk/mqdefault.jpg',
        duration: '4:42',
        views: '850K',
        publishedAt: '2023-02-20T15:30:00Z',
        channelId: 'UC987654321'
      },
      {
        id: 'fJ9rUzIMcZQ',
        title: `أفضل ${query} لهذا العام`,
        channel: 'قناة المراجعات',
        description: 'مراجعة شاملة لأفضل الخيارات المتاحة...',
        thumbnail: 'https://img.youtube.com/vi/fJ9rUzIMcZQ/mqdefault.jpg',
        duration: '5:55',
        views: '2.1M',
        publishedAt: '2023-03-10T12:15:00Z',
        channelId: 'UC456789123'
      }
    ];

    return {
      videos: mockVideos,
      nextPageToken: null,
      totalResults: mockVideos.length
    };
  }

  // Mock video details (fallback)
  getMockVideoDetails(videoId) {
    return {
      id: videoId,
      title: 'فيديو يوتيوب تجريبي',
      channel: 'قناة تجريبية',
      description: 'وصف تجريبي للفيديو...',
      thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
      duration: '3:45',
      views: '1.5M',
      likes: '25K',
      publishedAt: '2023-01-01T00:00:00Z',
      channelId: 'UC123456789',
      tags: ['تجريبي', 'فيديو', 'يوتيوب']
    };
  }

  // Get trending videos
  async getTrendingVideos(regionCode = 'SA', maxResults = 25) {
    try {
      if (this.useProxy) {
        return await this.getTrendingWithProxy(regionCode, maxResults);
      }

      const params = new URLSearchParams({
        part: 'snippet,statistics,contentDetails',
        chart: 'mostPopular',
        regionCode,
        maxResults: maxResults.toString(),
        key: this.apiKey,
        videoCategoryId: '0' // All categories
      });

      const response = await fetch(`${this.baseURL}/videos?${params}`);
      
      if (!response.ok) {
        throw new Error(`YouTube API error: ${response.status}`);
      }

      const data = await response.json();
      return this.transformSearchResults(data);
    } catch (error) {
      console.error('YouTube trending error:', error);
      return this.getMockSearchResults('الأكثر مشاهدة');
    }
  }

  // Get trending with proxy
  async getTrendingWithProxy(regionCode, maxResults) {
    try {
      const params = new URLSearchParams({
        regionCode,
        maxResults: maxResults.toString()
      });

      const response = await fetch(`${this.proxyURL}/trending?${params}`);
      
      if (!response.ok) {
        throw new Error(`Proxy API error: ${response.status}`);
      }

      const data = await response.json();
      return this.transformSearchResults(data);
    } catch (error) {
      console.error('Proxy trending error:', error);
      return this.getMockSearchResults('الأكثر مشاهدة');
    }
  }
}

// Create and export a singleton instance
const youtubeAPI = new YouTubeAPIService();
export default youtubeAPI;
